<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块导入测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 模块导入测试</h1>
        <p>逐步测试各个模块的导入，找出问题所在</p>
        
        <div class="test-section">
            <h3>测试1: Card模块</h3>
            <button onclick="testCardModule()">测试Card模块</button>
            <div id="cardResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: CardPattern模块</h3>
            <button onclick="testCardPatternModule()">测试CardPattern模块</button>
            <div id="cardPatternResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: GameState模块</h3>
            <button onclick="testGameStateModule()">测试GameState模块</button>
            <div id="gameStateResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: BiddingManager模块</h3>
            <button onclick="testBiddingManagerModule()">测试BiddingManager模块</button>
            <div id="biddingManagerResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试5: GameScene模块</h3>
            <button onclick="testGameSceneModule()">测试GameScene模块</button>
            <div id="gameSceneResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message, details = '') {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.innerHTML = `
                <strong>${success ? '✅ 通过' : '❌ 失败'}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
        }

        // 测试1: Card模块
        window.testCardModule = async function() {
            log('🧪 开始测试Card模块')
            
            try {
                const cardModule = await import('./src/game/Card.js')
                log('✅ Card模块导入成功')
                
                if (cardModule.Card && cardModule.Deck && cardModule.PlayerHand) {
                    log('✅ Card、Deck、PlayerHand类都存在')
                    
                    // 测试创建实例
                    const card = new cardModule.Card('spades', 'A')
                    const deck = new cardModule.Deck()
                    const hand = new cardModule.PlayerHand()
                    
                    log(`✅ 实例创建成功: ${card.getDisplayName()}`)
                    showResult('cardResult', true, 'Card模块测试通过')
                } else {
                    log('❌ Card模块缺少必要的类')
                    showResult('cardResult', false, 'Card模块不完整')
                }
                
            } catch (error) {
                log(`❌ Card模块测试失败: ${error.message}`)
                showResult('cardResult', false, 'Card模块导入失败', error.message)
            }
        }

        // 测试2: CardPattern模块
        window.testCardPatternModule = async function() {
            log('🧪 开始测试CardPattern模块')
            
            try {
                const cardPatternModule = await import('./src/game/CardPattern.js')
                log('✅ CardPattern模块导入成功')
                
                if (cardPatternModule.CardPattern) {
                    log('✅ CardPattern类存在')
                    
                    const pattern = new cardPatternModule.CardPattern()
                    log('✅ CardPattern实例创建成功')
                    showResult('cardPatternResult', true, 'CardPattern模块测试通过')
                } else {
                    log('❌ CardPattern类不存在')
                    showResult('cardPatternResult', false, 'CardPattern类不存在')
                }
                
            } catch (error) {
                log(`❌ CardPattern模块测试失败: ${error.message}`)
                showResult('cardPatternResult', false, 'CardPattern模块导入失败', error.message)
            }
        }

        // 测试3: GameState模块
        window.testGameStateModule = async function() {
            log('🧪 开始测试GameState模块')
            
            try {
                const gameStateModule = await import('./src/game/GameState.js')
                log('✅ GameState模块导入成功')
                
                if (gameStateModule.GameState) {
                    log('✅ GameState类存在')
                    
                    const gameState = new gameStateModule.GameState()
                    log('✅ GameState实例创建成功')
                    showResult('gameStateResult', true, 'GameState模块测试通过')
                } else {
                    log('❌ GameState类不存在')
                    showResult('gameStateResult', false, 'GameState类不存在')
                }
                
            } catch (error) {
                log(`❌ GameState模块测试失败: ${error.message}`)
                showResult('gameStateResult', false, 'GameState模块导入失败', error.message)
            }
        }

        // 测试4: BiddingManager模块
        window.testBiddingManagerModule = async function() {
            log('🧪 开始测试BiddingManager模块')
            
            try {
                const biddingManagerModule = await import('./src/game/BiddingManager.js')
                log('✅ BiddingManager模块导入成功')
                
                if (biddingManagerModule.BiddingManager) {
                    log('✅ BiddingManager类存在')
                    
                    const manager = new biddingManagerModule.BiddingManager()
                    log('✅ BiddingManager实例创建成功')
                    showResult('biddingManagerResult', true, 'BiddingManager模块测试通过')
                } else {
                    log('❌ BiddingManager类不存在')
                    showResult('biddingManagerResult', false, 'BiddingManager类不存在')
                }
                
            } catch (error) {
                log(`❌ BiddingManager模块测试失败: ${error.message}`)
                showResult('biddingManagerResult', false, 'BiddingManager模块导入失败', error.message)
            }
        }

        // 测试5: GameScene模块
        window.testGameSceneModule = async function() {
            log('🧪 开始测试GameScene模块')
            
            try {
                const gameSceneModule = await import('./src/game/GameScene.js')
                log('✅ GameScene模块导入成功')
                
                if (gameSceneModule.GameMainScene) {
                    log('✅ GameMainScene类存在')
                    
                    // 检查是否正确继承Phaser.Scene
                    if (typeof Phaser !== 'undefined') {
                        const scene = new gameSceneModule.GameMainScene()
                        if (scene instanceof Phaser.Scene) {
                            log('✅ GameMainScene正确继承Phaser.Scene')
                            showResult('gameSceneResult', true, 'GameScene模块测试通过')
                        } else {
                            log('❌ GameMainScene未正确继承Phaser.Scene')
                            showResult('gameSceneResult', false, 'GameMainScene继承问题')
                        }
                    } else {
                        log('⚠️ Phaser未加载，跳过继承检查')
                        showResult('gameSceneResult', true, 'GameScene模块导入成功（未检查继承）')
                    }
                } else {
                    log('❌ GameMainScene类不存在')
                    showResult('gameSceneResult', false, 'GameMainScene类不存在')
                }
                
            } catch (error) {
                log(`❌ GameScene模块测试失败: ${error.message}`)
                showResult('gameSceneResult', false, 'GameScene模块导入失败', error.message)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔍 模块导入测试页面已加载')
            log('请按顺序测试各个模块，找出问题所在')
        })
    </script>
</body>
</html>
