import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
    this.selectedCards = [] // 当前选中的牌
  }

  preload() {
    // 简化预加载，只加载必需资源
    console.log('GameMainScene: 开始预加载资源')

    // 添加加载错误处理
    this.load.on('loaderror', (file) => {
      console.warn(`⚠️ 资源加载失败: ${file.key} - ${file.src}`)
      // 继续执行，不让资源加载失败阻止游戏
    })

    this.load.on('complete', () => {
      console.log('✅ 资源加载完成')
    })

    // 暂时跳过所有图片资源，专注于叫牌功能
    console.log('🎯 跳过图片资源，专注于叫牌功能测试')
  }

  create() {
    // 简化游戏初始化，专注于叫牌功能
    console.log('🎮 GameMainScene: 创建简化游戏场景')

    try {
      // 创建简单背景
      console.log('🎨 创建简单背景')
      this.createSimpleBackground()

      // 创建简单标题
      console.log('📝 创建标题')
      this.createSimpleTitle()

      // 创建简化UI
      console.log('🖥️ 创建简化UI')
      this.createSimpleUI()

      // 初始化游戏状态
      console.log('🎯 初始化游戏状态')
      this.initializeGameState()

      console.log('✅ 简化游戏场景创建完成')
    } catch (error) {
      console.error('❌ 游戏场景创建失败:', error)
    }
  }

  createSimpleBackground() {
    // 创建简单的蓝色背景
    const bg = this.add.graphics()
    bg.fillStyle(0x1E3A8A, 1)
    bg.fillRect(0, 0, 1200, 800)
    console.log('✅ 简单背景创建完成')
  }

  createSimpleTitle() {
    // 创建简单标题
    const title = this.add.text(600, 40, '斗地主', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)

    this.uiElements.title = title
    console.log('✅ 简单标题创建完成')
  }

  createSimpleUI() {
    // 创建简化的UI界面
    this.uiElements = {}

    // 游戏阶段显示
    this.uiElements.gamePhase = this.add.text(600, 100, '等待开始', {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 开始游戏按钮
    this.createStartButton()

    // 创建叫牌历史面板
    this.createBiddingHistoryPanel()

    // 创建玩家信息显示
    this.createPlayerInfo()

    // 创建计时器
    this.createTimerDisplay()

    console.log('✅ 简化UI创建完成')
  }

  initializeGameState() {
    // 初始化游戏状态
    this.gameState = new GameState()
    this.selectedCards = []
    console.log('✅ 游戏状态初始化完成')
  }






  createStartButton() {
    // 创建简化的开始按钮
    const startButton = this.add.rectangle(600, 400, 200, 60, 0x4CAF50)
    startButton.setStrokeStyle(2, 0xFFFFFF)
    startButton.setInteractive({ useHandCursor: true })

    const startText = this.add.text(600, 400, '开始游戏', {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)

    startButton.on('pointerdown', () => {
      console.log('🎯 开始游戏按钮被点击')
      this.startNewGame()
    })

    startButton.on('pointerover', () => {
      startButton.setFillStyle(0x66BB6A)
    })

    startButton.on('pointerout', () => {
      startButton.setFillStyle(0x4CAF50)
    })

    this.uiElements.startButton = startButton
    this.uiElements.startText = startText
    console.log('✅ 开始按钮创建完成')
  }

  playSound(soundName, volume = 1.0) {
    // 播放音效的统一方法 - 暂时禁用避免404错误
    console.log(`🔊 播放音效: ${soundName} (音量: ${volume})`)
    // 暂时注释掉音效播放，专注于核心功能
    /*
    try {
      if (this.soundEffects && this.soundEffects[soundName]) {
        this.soundEffects[soundName].play({ volume })
      } else if (this.cache.audio.exists(soundName)) {
        this.sound.play(soundName, { volume })
      }
    } catch (error) {
      console.warn(`播放音效失败: ${soundName}`, error)
    }
    */
  }



  createStandardBackground() {
    // 创建蓝色渐变背景，类似标准斗地主
    const bg = this.add.graphics()
    bg.fillGradientStyle(0x1E3A8A, 0x1E3A8A, 0x1E40AF, 0x2563EB, 1)
    bg.fillRect(0, 0, 1200, 800)

    // 添加纹理效果
    const texture = this.add.graphics()
    texture.fillStyle(0xFFFFFF, 0.05)
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * 1200
      const y = Math.random() * 800
      texture.fillCircle(x, y, Math.random() * 2)
    }

    // 创建简化的游戏区域
    this.createSimpleGameArea()
  }

  createSimpleGameArea() {
    // 创建中央出牌区域 - 简化版本
    const centerX = 600
    const centerY = 300

    // 中央区域背景
    const centerArea = this.add.graphics()
    centerArea.fillStyle(0x000000, 0.2)
    centerArea.fillRoundedRect(centerX - 150, centerY - 80, 300, 160, 20)

    // 存储中央区域引用
    this.uiElements.centerPlayArea = {
      x: centerX,
      y: centerY
    }
  }

  createGameTable() {
    // 创建标准斗地主游戏桌面
    const centerX = 600
    const centerY = 350

    // 主游戏区域 - 椭圆形桌面
    const tableGraphics = this.add.graphics()

    // 桌面阴影
    tableGraphics.fillStyle(0x000000, 0.3)
    tableGraphics.fillEllipse(centerX + 5, centerY + 5, 500, 280)

    // 桌面主体 - 深绿色毛毡质感
    tableGraphics.fillGradientStyle(0x0F5132, 0x0F5132, 0x0A3D26, 0x0A3D26, 1)
    tableGraphics.fillEllipse(centerX, centerY, 500, 280)

    // 桌面边框 - 金色装饰
    tableGraphics.lineStyle(4, 0xDAA520, 1)
    tableGraphics.strokeEllipse(centerX, centerY, 500, 280)

    // 内边框装饰
    tableGraphics.lineStyle(2, 0xFFD700, 0.8)
    tableGraphics.strokeEllipse(centerX, centerY, 480, 260)

    // 中央出牌区域
    this.createCenterPlayArea(centerX, centerY)

    // 添加桌面装饰元素
    this.createTableDecorations(centerX, centerY)
  }

  createCenterPlayArea(centerX, centerY) {
    // 创建中央出牌区域
    const playAreaGraphics = this.add.graphics()

    // 出牌区域背景
    playAreaGraphics.fillStyle(0x1A5D3A, 0.6)
    playAreaGraphics.fillRoundedRect(centerX - 120, centerY - 60, 240, 120, 15)

    // 出牌区域边框
    playAreaGraphics.lineStyle(2, 0xFFD700, 0.6)
    playAreaGraphics.strokeRoundedRect(centerX - 120, centerY - 60, 240, 120, 15)

    // 出牌区域标识
    const playAreaText = this.add.text(centerX, centerY, '出牌区', {
      fontSize: '18px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      alpha: 0.7
    }).setOrigin(0.5)

    // 存储出牌区域引用
    this.uiElements.centerPlayArea = {
      graphics: playAreaGraphics,
      text: playAreaText,
      x: centerX,
      y: centerY
    }
  }

  createTableDecorations(centerX, centerY) {
    // 添加桌面装饰元素

    // 四个角的装饰图案
    const decorPositions = [
      { x: centerX - 200, y: centerY - 100, rotation: 0 },
      { x: centerX + 200, y: centerY - 100, rotation: Math.PI / 2 },
      { x: centerX + 200, y: centerY + 100, rotation: Math.PI },
      { x: centerX - 200, y: centerY + 100, rotation: -Math.PI / 2 }
    ]

    decorPositions.forEach(pos => {
      const decor = this.add.graphics()
      decor.fillStyle(0xDAA520, 0.4)

      // 创建花纹装饰
      decor.fillTriangle(0, -8, -6, 6, 6, 6)
      decor.fillCircle(0, -4, 3)

      decor.setPosition(pos.x, pos.y)
      decor.setRotation(pos.rotation)
    })

    // 添加中央logo区域
    const logoArea = this.add.graphics()
    logoArea.fillStyle(0xDAA520, 0.3)
    logoArea.fillCircle(centerX, centerY - 180, 25)
    logoArea.lineStyle(2, 0xFFD700, 0.8)
    logoArea.strokeCircle(centerX, centerY - 180, 25)

    // Logo文字
    this.add.text(centerX, centerY - 180, '斗', {
      fontSize: '20px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
  }

  createAmbientEffects() {
    // 创建环境装饰效果

    // 添加飘落的光点效果
    this.createFloatingParticles()

    // 添加边角装饰
    this.createAmbientCornerDecorations()
  }

  createFloatingParticles() {
    // 创建飘落的光点粒子效果
    for (let i = 0; i < 15; i++) {
      setTimeout(() => {
        this.createFloatingParticle()
      }, i * 800)
    }

    // 定期创建新的粒子
    this.time.addEvent({
      delay: 3000,
      callback: () => {
        this.createFloatingParticle()
      },
      loop: true
    })
  }

  createFloatingParticle() {
    const particle = this.add.graphics()
    const colors = [0xFFD700, 0xFFA500, 0xFFE4B5, 0xF0E68C]
    const color = colors[Math.floor(Math.random() * colors.length)]

    particle.fillStyle(color, 0.6)
    particle.fillCircle(0, 0, 2 + Math.random() * 3)

    const startX = Math.random() * 1200
    const startY = -20
    particle.setPosition(startX, startY)

    // 飘落动画
    this.tweens.add({
      targets: particle,
      y: 820,
      x: startX + (Math.random() - 0.5) * 100,
      alpha: 0,
      duration: 8000 + Math.random() * 4000,
      ease: 'Linear',
      onComplete: () => {
        particle.destroy()
      }
    })

    // 旋转动画
    this.tweens.add({
      targets: particle,
      rotation: Math.PI * 2,
      duration: 2000 + Math.random() * 2000,
      repeat: -1,
      ease: 'Linear'
    })
  }

  createAmbientCornerDecorations() {
    // 创建四个角落的装饰
    const corners = [
      { x: 50, y: 50, rotation: 0 },
      { x: 1150, y: 50, rotation: Math.PI / 2 },
      { x: 1150, y: 750, rotation: Math.PI },
      { x: 50, y: 750, rotation: -Math.PI / 2 }
    ]

    corners.forEach(corner => {
      const decoration = this.add.graphics()
      decoration.fillStyle(0xFFD700, 0.3)
      decoration.lineStyle(2, 0xDAA520, 0.8)

      // 创建装饰图案
      decoration.fillTriangle(0, -20, -15, 10, 15, 10)
      decoration.strokeTriangle(0, -20, -15, 10, 15, 10)
      decoration.fillCircle(0, -5, 8)
      decoration.strokeCircle(0, -5, 8)

      decoration.setPosition(corner.x, corner.y)
      decoration.setRotation(corner.rotation)

      // 添加缓慢的脉冲动画
      this.tweens.add({
        targets: decoration,
        alpha: 0.1,
        duration: 3000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      })
    })
  }

  createGameBorder() {
    // 创建游戏边框装饰
    const borderGraphics = this.add.graphics()

    // 外边框 - 金色
    borderGraphics.lineStyle(4, 0xFFD700, 1)
    borderGraphics.strokeRoundedRect(15, 15, 1170, 770, 20)

    // 内边框 - 深色
    borderGraphics.lineStyle(2, 0x8B4513, 1)
    borderGraphics.strokeRoundedRect(20, 20, 1160, 760, 18)

    // 角落装饰
    this.createCornerDecorations()
  }

  createCornerDecorations() {
    // 在四个角落添加装饰图案
    const corners = [
      { x: 40, y: 40 },      // 左上
      { x: 1160, y: 40 },    // 右上
      { x: 40, y: 760 },     // 左下
      { x: 1160, y: 760 }    // 右下
    ]

    corners.forEach((corner) => {
      const decoration = this.add.graphics()
      decoration.fillStyle(0xFFD700, 0.8)
      decoration.fillCircle(corner.x, corner.y, 10)
      decoration.lineStyle(2, 0x8B4513, 1)
      decoration.strokeCircle(corner.x, corner.y, 10)
    })
  }

  createAncientPatterns() {
    // 创建古代纹样装饰
    const patterns = this.add.graphics()

    // 四角装饰纹样
    const corners = [
      {x: 50, y: 50}, {x: 750, y: 50},
      {x: 50, y: 550}, {x: 750, y: 550}
    ]

    corners.forEach(corner => {
      patterns.lineStyle(3, 0xFFD700, 0.8)
      patterns.strokeRect(corner.x - 30, corner.y - 30, 60, 60)
      patterns.lineStyle(2, 0xFF6347, 0.6)
      patterns.strokeRect(corner.x - 20, corner.y - 20, 40, 40)

      // 中心装饰
      patterns.fillStyle(0xFFD700, 0.7)
      patterns.fillCircle(corner.x, corner.y, 8)
    })

    // 顶部和底部边框装饰
    patterns.lineStyle(4, 0xFFD700, 0.9)
    patterns.moveTo(100, 20)
    patterns.lineTo(700, 20)
    patterns.moveTo(100, 580)
    patterns.lineTo(700, 580)
    patterns.strokePath()
  }

  createCloudDecorations() {
    // 添加卡通云朵装饰
    const clouds = [
      {x: 150, y: 80, scale: 0.8},
      {x: 650, y: 100, scale: 0.6},
      {x: 100, y: 500, scale: 0.7},
      {x: 700, y: 520, scale: 0.5}
    ]

    clouds.forEach(cloud => {
      this.createCloud(cloud.x, cloud.y, cloud.scale)
    })
  }

  createCloud(x, y, scale) {
    const cloud = this.add.graphics()
    cloud.fillStyle(0xFFFFFF, 0.3)

    // 云朵主体
    cloud.fillCircle(x, y, 20 * scale)
    cloud.fillCircle(x - 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x + 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x - 8 * scale, y - 8 * scale, 12 * scale)
    cloud.fillCircle(x + 8 * scale, y - 8 * scale, 12 * scale)
  }



  createPlayerPositions() {
    // 标准斗地主布局 - 左右玩家垂直对齐
    const positions = [
      { x: 600, y: 720, name: '农民', position: 'bottom', playerId: 1 },
      { x: 80, y: 300, name: '地主', position: 'left', playerId: 2 },  // 左侧玩家
      { x: 1120, y: 300, name: '农民', position: 'right', playerId: 3 }  // 右侧玩家与左侧对齐
    ]

    positions.forEach((pos, index) => {
      // 创建简化的玩家头像区域
      this.createSimplePlayerAvatar(pos, index + 1)
    })
  }

  createSimplePlayerAvatar(pos, playerNum) {
    // 创建玩家信息容器
    const playerContainer = this.add.container(pos.x, pos.y)

    // 简化的头像背景 - 类似参考图
    const avatarBg = this.add.graphics()
    avatarBg.fillStyle(0xFFFFFF, 1)
    avatarBg.lineStyle(2, 0x666666, 1)
    avatarBg.fillCircle(0, 0, 30)
    avatarBg.strokeCircle(0, 0, 30)
    playerContainer.add(avatarBg)

    // 玩家头像 - 简化版本，添加错误处理
    try {
      if (this.cache.image.exists(`avatar_${playerNum}`)) {
        const avatar = this.add.image(0, 0, `avatar_${playerNum}`)
        avatar.setDisplaySize(50, 50)
        avatar.setOrigin(0.5, 0.5)
        playerContainer.add(avatar)
      } else {
        // 如果头像不存在，创建一个简单的替代图形
        const fallbackAvatar = this.add.graphics()
        fallbackAvatar.fillStyle(0x4CAF50, 1)
        fallbackAvatar.fillCircle(0, 0, 20)
        fallbackAvatar.lineStyle(2, 0xFFFFFF, 1)
        fallbackAvatar.strokeCircle(0, 0, 20)

        // 添加玩家编号
        const playerNumText = this.add.text(0, 0, playerNum.toString(), {
          fontSize: '16px',
          color: '#FFFFFF',
          fontFamily: 'Arial',
          fontWeight: 'bold'
        }).setOrigin(0.5)

        playerContainer.add([fallbackAvatar, playerNumText])
      }
    } catch (error) {
      console.warn(`创建玩家${playerNum}头像失败:`, error)
    }

    // 玩家名称 - 简化显示
    const nameText = this.add.text(0, 45, pos.name, {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(nameText)

    // 手牌数量显示 - 简化版本
    const cardCountBg = this.add.graphics()
    cardCountBg.fillStyle(0xFF4444, 1)
    cardCountBg.lineStyle(1, 0xFFFFFF, 1)
    cardCountBg.fillCircle(25, -25, 12)
    cardCountBg.strokeCircle(25, -25, 12)
    playerContainer.add(cardCountBg)

    const cardCountText = this.add.text(25, -25, '17', {
      fontSize: '10px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(cardCountText)

    // 金币显示 - 类似参考图
    if (pos.position === 'bottom') {
      const coinBg = this.add.graphics()
      coinBg.fillStyle(0xFFD700, 1)
      coinBg.lineStyle(1, 0xFFA500, 1)
      coinBg.fillRoundedRect(-40, -50, 80, 20, 10)
      coinBg.strokeRoundedRect(-40, -50, 80, 20, 10)
      playerContainer.add(coinBg)

      const coinText = this.add.text(0, -40, '2900', {
        fontSize: '12px',
        color: '#000000',
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0.5, 0.5)
      playerContainer.add(coinText)
    }

    // 添加玩家等级显示
    const levelBg = this.add.graphics()
    levelBg.fillStyle(0x4169E1, 0.9)
    levelBg.lineStyle(2, 0xFFD700, 1)
    levelBg.fillRoundedRect(-45, -55, 30, 20, 10)
    levelBg.strokeRoundedRect(-45, -55, 30, 20, 10)
    playerContainer.add(levelBg)

    const levelText = this.add.text(-30, -45, 'LV.1', {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(levelText)

    // 添加在线状态指示器
    const statusIndicator = this.add.graphics()
    statusIndicator.fillStyle(0x00FF00, 1)
    statusIndicator.fillCircle(40, 40, 8)
    statusIndicator.lineStyle(2, 0xFFFFFF, 1)
    statusIndicator.strokeCircle(40, 40, 8)
    playerContainer.add(statusIndicator)

    // 存储引用以便后续更新
    this.uiElements[`player${playerNum}Container`] = playerContainer
    this.uiElements[`player${playerNum}CardCount`] = cardCountText
    this.uiElements[`player${playerNum}Name`] = nameText
    this.uiElements[`player${playerNum}Level`] = levelText
    this.uiElements[`player${playerNum}Status`] = statusIndicator

    // 设置手牌区域
    this.playerHandAreas[pos.position] = {
      x: pos.x,
      y: pos.y,
      cards: []
    }
  }



  createGameUI() {
    // 创建现代化游戏状态显示
    const statusContainer = this.add.container(600, 100)

    // 状态背景
    const statusBg = this.add.graphics()
    statusBg.fillStyle(0x000000, 0.7)
    statusBg.lineStyle(2, 0xFFD700, 1)
    statusBg.fillRoundedRect(-150, -25, 300, 50, 25)
    statusBg.strokeRoundedRect(-150, -25, 300, 50, 25)
    statusContainer.add(statusBg)

    // 游戏状态文字
    this.uiElements.gamePhase = this.add.text(0, 0, '等待开始...', {
      fontSize: '22px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    statusContainer.add(this.uiElements.gamePhase)





    // 创建操作按钮区域
    this.createModernActionButtons()
  }

  createTimerDisplay() {
    // 创建计时器容器 - 位置调整到右上角
    const timerContainer = this.add.container(1100, 100)

    // 创建圆形计时器背景
    const timerBg = this.add.graphics()

    // 外圈 - 深色边框
    timerBg.lineStyle(4, 0x333333, 1)
    timerBg.strokeCircle(0, 0, 35)

    // 内圈 - 渐变背景
    timerBg.fillStyle(0x1a1a1a, 0.9)
    timerBg.fillCircle(0, 0, 32)

    // 内边框 - 金色装饰
    timerBg.lineStyle(2, 0xFFD700, 0.8)
    timerBg.strokeCircle(0, 0, 28)

    timerContainer.add(timerBg)

    // 创建进度圆环（用于显示倒计时进度）
    this.uiElements.timerProgress = this.add.graphics()
    timerContainer.add(this.uiElements.timerProgress)

    // 计时器数字
    this.uiElements.timer = this.add.text(0, 0, '20', {
      fontSize: '20px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    timerContainer.add(this.uiElements.timer)

    // 添加计时器图标（时钟符号）
    const clockIcon = this.add.text(0, -15, '⏰', {
      fontSize: '12px'
    }).setOrigin(0.5)
    timerContainer.add(clockIcon)

    this.uiElements.timerContainer = timerContainer
    timerContainer.setVisible(false)
  }

  // 移除游戏信息面板，保持界面简洁

  createPlayHistoryPanel() {
    // 创建出牌历史面板
    const historyContainer = this.add.container(600, 250)

    // 历史面板背景
    const historyBg = this.add.graphics()
    historyBg.fillStyle(0x000000, 0.6)
    historyBg.lineStyle(1, 0x888888, 1)
    historyBg.fillRoundedRect(-200, -40, 400, 80, 10)
    historyBg.strokeRoundedRect(-200, -40, 400, 80, 10)
    historyContainer.add(historyBg)

    // 历史标题
    const historyTitle = this.add.text(0, -15, '上次出牌', {
      fontSize: '12px',
      color: '#CCCCCC',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    historyContainer.add(historyTitle)

    // 历史内容
    this.uiElements.playHistory = this.add.text(0, 5, '暂无', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    historyContainer.add(this.uiElements.playHistory)

    this.uiElements.historyContainer = historyContainer
    historyContainer.setVisible(false)
  }

  createBiddingHistoryPanel() {
    // 创建叫牌历史面板 - 位置在左上角
    const biddingHistoryContainer = this.add.container(150, 150)

    // 叫牌历史面板背景 - 使用更亮的背景色
    const biddingBg = this.add.graphics()
    biddingBg.fillStyle(0x2a2a2a, 0.95) // 更亮的背景
    biddingBg.lineStyle(3, 0xFFD700, 1) // 更明显的边框
    biddingBg.fillRoundedRect(-120, -80, 240, 160, 12)
    biddingBg.strokeRoundedRect(-120, -80, 240, 160, 12)
    biddingHistoryContainer.add(biddingBg)

    // 叫牌历史标题
    const biddingTitle = this.add.text(0, -60, '叫牌历史', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    biddingHistoryContainer.add(biddingTitle)

    // 叫牌历史内容区域 - 使用更明显的文字颜色
    this.uiElements.biddingHistory = this.add.text(0, -10, '等待叫牌...', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      align: 'center',
      lineSpacing: 8,
      stroke: '#000000', // 添加黑色描边
      strokeThickness: 1
    }).setOrigin(0.5)
    biddingHistoryContainer.add(this.uiElements.biddingHistory)

    this.uiElements.biddingHistoryContainer = biddingHistoryContainer
    // 默认显示面板，这样可以看到效果
    biddingHistoryContainer.setVisible(true)
  }

  createPlayerInfo() {
    // 创建三个玩家的信息显示
    const playerPositions = [
      { x: 600, y: 750, name: '玩家', id: 1 }, // 底部玩家
      { x: 100, y: 200, name: 'AI-1', id: 2 }, // 左侧玩家
      { x: 1100, y: 200, name: 'AI-2', id: 3 }  // 右侧玩家
    ]

    playerPositions.forEach((pos) => {
      const playerContainer = this.add.container(pos.x, pos.y)

      // 玩家头像背景
      const avatarBg = this.add.graphics()
      avatarBg.fillStyle(0x333333, 0.8)
      avatarBg.lineStyle(2, 0xFFD700, 1)
      avatarBg.fillCircle(0, 0, 35)
      avatarBg.strokeCircle(0, 0, 35)
      playerContainer.add(avatarBg)

      // 玩家头像（简化版本）
      const avatar = this.add.graphics()
      avatar.fillStyle(0x4CAF50, 1)
      avatar.fillCircle(0, 0, 25)
      avatar.lineStyle(2, 0xFFFFFF, 1)
      avatar.strokeCircle(0, 0, 25)
      playerContainer.add(avatar)

      // 玩家编号
      const playerNumText = this.add.text(0, 0, pos.id.toString(), {
        fontSize: '16px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0.5)
      playerContainer.add(playerNumText)

      // 玩家昵称
      const nameText = this.add.text(0, 50, pos.name, {
        fontSize: '14px',
        color: '#FFFFFF',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      playerContainer.add(nameText)

      // 手牌数量显示
      const cardCountBg = this.add.graphics()
      cardCountBg.fillStyle(0x000000, 0.7)
      cardCountBg.fillRoundedRect(-15, 60, 30, 20, 5)
      playerContainer.add(cardCountBg)

      const cardCountText = this.add.text(0, 70, '17', {
        fontSize: '12px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0.5)
      playerContainer.add(cardCountText)

      // 存储引用
      this.uiElements[`player${pos.id}Container`] = playerContainer
      this.uiElements[`player${pos.id}CardCount`] = cardCountText
      this.uiElements[`player${pos.id}Name`] = nameText
    })

    console.log('✅ 玩家信息显示创建完成')
  }

  // 更新叫牌历史显示
  updateBiddingHistoryDisplay() {
    if (!this.uiElements.biddingHistory) return

    const biddingInfo = this.gameState.getBiddingInfo()
    const history = biddingInfo.biddingHistory

    if (history.length === 0) {
      this.uiElements.biddingHistory.setText('等待叫牌...')
      this.uiElements.biddingHistoryContainer.setVisible(false)
      return
    }

    // 显示叫牌历史面板
    this.uiElements.biddingHistoryContainer.setVisible(true)

    // 格式化叫牌历史
    const playerNames = ['玩家', 'AI-1', 'AI-2']
    let historyText = ''

    history.forEach((bid, index) => {
      const playerName = playerNames[bid.player] || `玩家${bid.player}`
      const bidText = bid.bidValue === 0 ? '不叫' : `${bid.bidValue}分`

      historyText += `${playerName}: ${bidText}`
      if (index < history.length - 1) {
        historyText += '\n'
      }
    })

    // 添加当前最高叫分信息
    if (biddingInfo.currentBid > 0) {
      historyText += `\n\n当前最高: ${biddingInfo.currentBid}分`
      const landlordName = playerNames[biddingInfo.landlord] || `玩家${biddingInfo.landlord}`
      historyText += `\n地主候选: ${landlordName}`
    }

    this.uiElements.biddingHistory.setText(historyText)

    console.log('🎯 叫牌历史已更新:', historyText)
  }

  createModernActionButtons() {
    // 按钮放在底部中央区域，类似参考图布局
    console.log('🔘 创建游戏按钮')
    const buttonY = 520
    const centerX = 600

    // 开始游戏按钮 - 居中显示
    console.log('🎮 创建标准开始游戏按钮')
    this.uiElements.startButton = this.createStandardButton(centerX, buttonY, '开始游戏', 'orange', () => this.startNewGame())
    console.log('✅ 开始游戏按钮创建完成:', this.uiElements.startButton)

    // 叫地主按钮
    this.uiElements.bidLandlordButton = this.createStandardButton(centerX - 80, buttonY, '叫地主', 'orange', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮
    this.uiElements.passBidButton = this.createStandardButton(centerX + 80, buttonY, '不出', 'blue', () => this.passBid())
    this.uiElements.passBidButton.setVisible(false)

    // 出牌按钮
    this.uiElements.playCardsButton = this.createStandardButton(centerX - 80, buttonY, '出牌', 'orange', () => this.playSelectedCards())
    this.uiElements.playCardsButton.setVisible(false)

    // 过牌按钮
    this.uiElements.passButton = this.createStandardButton(centerX + 80, buttonY, '不出', 'blue', () => this.passCards())
    this.uiElements.passButton.setVisible(false)

    // 提示按钮
    this.uiElements.hintButton = this.createStandardButton(centerX, buttonY + 50, '提示', 'blue', () => this.showHint())
    this.uiElements.hintButton.setVisible(false)
  }

  createButtonAreaDecoration(centerX, centerY) {
    // 创建按钮区域的装饰背景
    const decorBg = this.add.graphics()

    // 半透明背景
    decorBg.fillStyle(0x000000, 0.3)
    decorBg.fillRoundedRect(centerX - 100, centerY - 60, 200, 120, 15)

    // 装饰边框
    decorBg.lineStyle(2, 0xFFD700, 0.6)
    decorBg.strokeRoundedRect(centerX - 100, centerY - 60, 200, 120, 15)

    // 内边框
    decorBg.lineStyle(1, 0xFFFFFF, 0.3)
    decorBg.strokeRoundedRect(centerX - 98, centerY - 58, 196, 116, 13)

    // 角落装饰
    const corners = [
      { x: centerX - 90, y: centerY - 50 },
      { x: centerX + 90, y: centerY - 50 },
      { x: centerX - 90, y: centerY + 50 },
      { x: centerX + 90, y: centerY + 50 }
    ]

    corners.forEach(corner => {
      decorBg.fillStyle(0xFFD700, 0.4)
      decorBg.fillCircle(corner.x, corner.y, 4)
    })
  }

  createImageButton(x, y, imageKey, text, callback) {
    // 创建现代化的游戏按钮
    const buttonContainer = this.add.container(x, y)

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.4)
    shadow.fillRoundedRect(-55, -22, 110, 44, 22)
    buttonContainer.add(shadow)

    // 按钮背景 - 如果有图片资源则使用，否则创建现代风格按钮
    if (this.cache.image.exists(imageKey)) {
      const buttonImage = this.add.image(-2, -2, imageKey)
      buttonImage.setDisplaySize(110, 44)
      buttonContainer.add(buttonImage)
    } else {
      // 创建现代风格按钮背景
      const buttonBg = this.add.graphics()

      // 渐变背景
      buttonBg.fillGradientStyle(0x4CAF50, 0x4CAF50, 0x45A049, 0x45A049, 1)
      buttonBg.fillRoundedRect(-52, -20, 104, 40, 20)

      // 外边框
      buttonBg.lineStyle(2, 0xFFD700, 1)
      buttonBg.strokeRoundedRect(-52, -20, 104, 40, 20)

      // 内边框高光
      buttonBg.lineStyle(1, 0xFFFFFF, 0.6)
      buttonBg.strokeRoundedRect(-50, -18, 100, 36, 18)

      buttonContainer.add(buttonBg)
    }

    // 按钮文字 - 更现代的样式
    if (text && text !== '') {
      const buttonText = this.add.text(0, 0, text, {
        fontSize: '16px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontWeight: 'bold',
        stroke: '#000000',
        strokeThickness: 2
      }).setOrigin(0.5)
      buttonContainer.add(buttonText)
    }

    // 设置交互
    buttonContainer.setSize(110, 44)
    buttonContainer.setInteractive()

    // 添加点击效果
    buttonContainer.on('pointerdown', () => {
      buttonContainer.setScale(0.95)
      // 播放点击音效
      this.playSound('chupaiSound', 0.3)

      // 添加点击粒子效果
      this.createButtonClickEffect(x, y)

      callback()

      // 恢复按钮样式
      setTimeout(() => {
        buttonContainer.setScale(1)
      }, 150)
    })

    // 添加悬停效果
    buttonContainer.on('pointerover', () => {
      buttonContainer.setScale(1.05)
      buttonContainer.setTint(0xDDDDDD)

      // 添加悬停光晕效果
      this.createButtonHoverEffect(buttonContainer)
    })

    buttonContainer.on('pointerout', () => {
      buttonContainer.setScale(1)
      buttonContainer.clearTint()

      // 清除悬停效果
      this.clearButtonHoverEffect(buttonContainer)
    })

    return buttonContainer
  }

  createStandardButton(x, y, text, colorType, callback) {
    // 创建标准斗地主按钮样式，类似参考图
    console.log(`🔘 创建按钮: ${text} 位置:(${x}, ${y})`)
    const buttonContainer = this.add.container(x, y)

    // 按钮颜色配置
    const colors = {
      blue: { bg: 0x2563EB, border: 0x1D4ED8, text: '#FFFFFF' },
      orange: { bg: 0xF97316, border: 0xEA580C, text: '#FFFFFF' }
    }

    const color = colors[colorType] || colors.blue

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(-32, -12, 64, 24, 12)
    buttonContainer.add(shadow)

    // 按钮背景
    const buttonBg = this.add.graphics()
    buttonBg.fillStyle(color.bg, 1)
    buttonBg.fillRoundedRect(-30, -10, 60, 20, 10)
    buttonBg.lineStyle(1, color.border, 1)
    buttonBg.strokeRoundedRect(-30, -10, 60, 20, 10)
    buttonContainer.add(buttonBg)

    // 按钮文字
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: color.text,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    buttonContainer.add(buttonText)

    // 设置交互 - 增大交互区域
    buttonContainer.setSize(80, 40)
    buttonContainer.setInteractive()

    // 添加点击效果
    buttonContainer.on('pointerdown', () => {
      console.log(`🖱️ 按钮被点击: ${text}`)
      console.log('🔄 执行按钮回调函数')
      buttonContainer.setScale(0.95)

      try {
        callback()
        console.log('✅ 按钮回调执行成功')
      } catch (error) {
        console.error('❌ 按钮回调执行失败:', error)
      }

      setTimeout(() => {
        buttonContainer.setScale(1)
      }, 100)
    })

    // 添加悬停效果
    buttonContainer.on('pointerover', () => {
      buttonContainer.setScale(1.05)
    })

    buttonContainer.on('pointerout', () => {
      buttonContainer.setScale(1)
    })

    return buttonContainer
  }

  createButtonClickEffect(x, y) {
    // 创建按钮点击粒子效果
    for (let i = 0; i < 8; i++) {
      const particle = this.add.graphics()
      particle.fillStyle(0xFFD700, 1)
      particle.fillCircle(x, y, 3)

      const angle = (i / 8) * Math.PI * 2
      const distance = 30 + Math.random() * 20
      const targetX = x + Math.cos(angle) * distance
      const targetY = y + Math.sin(angle) * distance

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        duration: 400,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  createButtonHoverEffect(buttonContainer) {
    // 创建按钮悬停光晕效果
    if (buttonContainer.hoverGlow) {
      buttonContainer.hoverGlow.destroy()
    }

    const glow = this.add.graphics()
    glow.lineStyle(3, 0xFFD700, 0.6)
    glow.strokeRoundedRect(-55, -22, 110, 44, 22)
    glow.lineStyle(2, 0xFFFFFF, 0.4)
    glow.strokeRoundedRect(-53, -20, 106, 40, 20)

    buttonContainer.add(glow)
    buttonContainer.hoverGlow = glow

    // 添加脉冲动画
    this.tweens.add({
      targets: glow,
      alpha: 0.3,
      duration: 800,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  clearButtonHoverEffect(buttonContainer) {
    // 清除按钮悬停效果
    if (buttonContainer.hoverGlow) {
      buttonContainer.hoverGlow.destroy()
      buttonContainer.hoverGlow = null
    }
  }

  startNewGame() {
    console.log('🎮 开始新游戏')

    try {
      // 播放发牌音效
      this.playSound('fapaiSound', 0.4)

      console.log('📋 调用 gameState.startGame()')
      this.gameState.startGame()

      // 立即显示手牌，确保发牌后能看到牌
      setTimeout(() => {
        console.log('🃏 开始显示手牌')

        try {
          // 检查游戏状态
          const gameInfo = this.gameState.getGameInfo()
          console.log('🎮 当前游戏状态:', gameInfo)

          // 检查所有玩家
          console.log('👥 检查所有玩家:')
          for (let i = 1; i <= 3; i++) {
            const player = this.gameState.getPlayer(i)
            console.log(`👤 玩家${i}:`, player)
            if (player && player.hand) {
              console.log(`🎴 玩家${i}手牌数量:`, player.hand.getCards().length)
            }
          }



          this.displayPlayerCards()
          console.log('🔄 更新UI')
          this.updateUI()

          // 检查是否进入叫牌阶段，如果是则开始叫牌流程
          const currentGameInfo = this.gameState.getGameInfo()
          console.log('🎮 游戏开始后状态检查:', currentGameInfo)

          if (currentGameInfo.phase === 'bidding') {
            console.log('🎯 进入叫牌阶段，开始叫牌流程')
            console.log('🎯 当前玩家:', currentGameInfo.currentPlayer)

            // 立即检查是否轮到玩家
            if (currentGameInfo.currentPlayer === 0) {
              console.log('🎯 立即显示叫牌UI（轮到玩家）')
              this.showBiddingUI()
            } else {
              console.log('🤖 开始AI叫牌流程')
              setTimeout(() => {
                this.simulateAIBidding()
              }, 1000)
            }
          } else {
            console.log('❌ 未进入叫牌阶段，当前阶段:', currentGameInfo.phase)
          }
        } catch (error) {
          console.error('❌ 显示手牌时出错:', error)
        }
      }, 100)
    } catch (error) {
      console.error('❌ 开始游戏时出错:', error)
    }
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()
    console.log('更新UI，当前游戏状态:', gameInfo)

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach((player, index) => {
      const playerNum = index + 1
      const cardCountElement = this.uiElements[`player${playerNum}CardCount`]
      if (cardCountElement) {
        cardCountElement.setText(player.cardCount.toString())

        // 如果是地主，显示特殊标识
        if (player.isLandlord) {
          cardCountElement.setStyle({ color: '#FF0000' })
          // 添加地主标识
          this.showLandlordIndicator(playerNum)
        }
      }
    })

    // 更新当前玩家指示器
    this.updateCurrentPlayerIndicator(gameInfo.currentPlayer)

    // 更新按钮显示
    this.updateActionButtons(gameInfo)

    // 更新计时器
    this.updateTimer(gameInfo)

    // 更新叫牌历史显示
    if (gameInfo.phase === 'bidding') {
      this.updateBiddingHistoryDisplay()
    }

    // 更新出牌历史
    this.updatePlayHistory(gameInfo)
  }

  showLandlordIndicator(playerNum) {
    // 如果已经有地主标识，先清除
    if (this.uiElements[`landlord${playerNum}`]) {
      this.uiElements[`landlord${playerNum}`].destroy()
    }

    // 创建地主标识容器
    const playerContainer = this.uiElements[`player${playerNum}Container`]
    if (playerContainer) {
      const landlordContainer = this.add.container(-35, -35)

      // 地主标识背景
      const landlordBg = this.add.graphics()
      landlordBg.fillStyle(0xFF0000, 0.9)
      landlordBg.lineStyle(2, 0xFFD700, 1)
      landlordBg.fillCircle(0, 0, 20)
      landlordBg.strokeCircle(0, 0, 20)
      landlordContainer.add(landlordBg)

      // 地主图标或文字
      if (this.cache && this.cache.image && this.cache.image.exists && this.cache.image.exists('img_Card_dizhu')) {
        const landlordIcon = this.add.image(0, 0, 'img_Card_dizhu')
        landlordIcon.setDisplaySize(32, 32)
        landlordContainer.add(landlordIcon)
      } else {
        const landlordText = this.add.text(0, 0, '地\n主', {
          fontSize: '12px',
          color: '#FFFFFF',
          fontFamily: 'Arial',
          fontWeight: 'bold',
          align: 'center',
          lineSpacing: -2
        }).setOrigin(0.5)
        landlordContainer.add(landlordText)
      }

      // 添加闪烁效果
      this.tweens.add({
        targets: landlordContainer,
        scaleX: 1.1,
        scaleY: 1.1,
        duration: 800,
        yoyo: true,
        repeat: 2,
        ease: 'Sine.easeInOut'
      })

      playerContainer.add(landlordContainer)
      this.uiElements[`landlord${playerNum}`] = landlordContainer
    }
  }

  updateCurrentPlayerIndicator(currentPlayerIndex) {
    // 清除所有玩家的当前指示器
    for (let i = 1; i <= 3; i++) {
      if (this.uiElements[`currentIndicator${i}`]) {
        this.uiElements[`currentIndicator${i}`].setVisible(false)
      }
    }

    // 显示当前玩家的指示器
    const playerNum = currentPlayerIndex + 1
    if (!this.uiElements[`currentIndicator${playerNum}`]) {
      const playerContainer = this.uiElements[`player${playerNum}Container`]
      if (playerContainer) {
        const indicator = this.add.graphics()

        // 创建多层指示器效果
        indicator.lineStyle(5, 0x00FF00, 0.8)
        indicator.strokeCircle(0, 0, 65)
        indicator.lineStyle(3, 0x32CD32, 0.9)
        indicator.strokeCircle(0, 0, 60)
        indicator.lineStyle(2, 0x90EE90, 1)
        indicator.strokeCircle(0, 0, 55)

        playerContainer.add(indicator)
        this.uiElements[`currentIndicator${playerNum}`] = indicator

        // 添加旋转和脉冲效果
        this.tweens.add({
          targets: indicator,
          rotation: Math.PI * 2,
          duration: 2000,
          repeat: -1,
          ease: 'Linear'
        })

        this.tweens.add({
          targets: indicator,
          alpha: 0.4,
          scaleX: 1.1,
          scaleY: 1.1,
          duration: 1000,
          yoyo: true,
          repeat: -1,
          ease: 'Sine.easeInOut'
        })
      }
    } else {
      this.uiElements[`currentIndicator${playerNum}`].setVisible(true)
    }
  }

  updateTimer(gameInfo) {
    // 如果是叫牌阶段且轮到玩家，由叫牌倒计时控制
    if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 叫牌倒计时会自己控制计时器显示，这里不做处理
      return
    }

    if (gameInfo.phase === 'bidding' || gameInfo.phase === 'playing') {
      if (this.uiElements.timerContainer) {
        this.uiElements.timerContainer.setVisible(true)

        // 初始化计时器
        if (!this.gameTimer) {
          this.gameTimer = 20
          this.maxTime = 20
        }

        if (this.uiElements.timer) {
          this.uiElements.timer.setText(this.gameTimer.toString())

          // 更新进度圆环
          this.updateTimerProgress(this.gameTimer, this.maxTime)

          // 时间不足时变红并添加警告效果
          if (this.gameTimer <= 10) {
            this.uiElements.timer.setStyle({
              color: '#FF0000',
              fontSize: '22px' // 时间紧急时字体稍大
            })

            // 添加紧急闪烁效果
            if (this.gameTimer <= 5) {
              this.tweens.add({
                targets: this.uiElements.timer,
                alpha: 0.3,
                duration: 300,
                yoyo: true,
                repeat: 1,
                ease: 'Power2'
              })
            }
          } else {
            this.uiElements.timer.setStyle({
              color: '#FFFFFF',
              fontSize: '20px'
            })
          }
        }
      }
    } else {
      if (this.uiElements.timerContainer) {
        this.uiElements.timerContainer.setVisible(false)
      }
    }
  }

  updateTimerProgress(currentTime, maxTime) {
    if (!this.uiElements.timerProgress) return

    // 清除之前的进度圆环
    this.uiElements.timerProgress.clear()

    // 计算进度百分比
    const progress = currentTime / maxTime
    const angle = progress * 2 * Math.PI

    // 根据剩余时间选择颜色
    let progressColor = 0x00FF00 // 绿色
    if (progress < 0.5) {
      progressColor = 0xFFFF00 // 黄色
    }
    if (progress < 0.3) {
      progressColor = 0xFF6600 // 橙色
    }
    if (progress < 0.2) {
      progressColor = 0xFF0000 // 红色
    }

    // 绘制进度圆环
    this.uiElements.timerProgress.lineStyle(4, progressColor, 0.8)
    this.uiElements.timerProgress.beginPath()
    this.uiElements.timerProgress.arc(0, 0, 25, -Math.PI/2, -Math.PI/2 + angle, false)
    this.uiElements.timerProgress.strokePath()

    // 添加进度圆环的光晕效果
    if (progress < 0.3) {
      this.uiElements.timerProgress.lineStyle(2, progressColor, 0.4)
      this.uiElements.timerProgress.strokeCircle(0, 0, 27)
    }
  }

  updateActionButtons(gameInfo) {
    console.log('🔄 更新按钮显示，游戏阶段:', gameInfo.phase, '当前玩家:', gameInfo.currentPlayer)

    // 隐藏所有游戏按钮
    const gameButtons = [
      'startButton', 'bidLandlordButton', 'passBidButton',
      'playCardsButton', 'passButton', 'hintButton'
    ]

    gameButtons.forEach(buttonName => {
      if (this.uiElements[buttonName]) {
        console.log(`🔘 隐藏按钮: ${buttonName}`)
        this.uiElements[buttonName].setVisible(false)
      }
    })

    if (gameInfo.phase === 'waiting') {
      console.log('🎮 显示开始游戏按钮')
      if (this.uiElements.startButton) {
        console.log('✅ 开始游戏按钮存在，设置为可见')
        this.uiElements.startButton.setVisible(true)
      } else {
        console.log('❌ 开始游戏按钮不存在！')
      }
    } else if (gameInfo.phase === 'bidding') {
      // 叫牌阶段
      console.log('🎯 叫牌阶段，当前玩家:', gameInfo.currentPlayer)

      // 确保在叫牌阶段显示手牌
      console.log('🃏 叫牌阶段 - 确保显示手牌')
      this.displayPlayerCards()

      if (gameInfo.currentPlayer === 0) {
        // 轮到玩家1叫牌，显示叫牌UI
        console.log('🎯 轮到玩家叫牌，显示叫牌UI')
        this.showBiddingUI()
      } else {
        // 轮到AI叫牌，隐藏叫牌UI
        console.log('🤖 轮到AI叫牌，隐藏叫牌UI')
        this.hideBiddingUI()
      }
    } else if (gameInfo.phase === 'playing' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1出牌
      console.log('显示出牌按钮')
      if (this.uiElements.playCardsButton) {
        this.uiElements.playCardsButton.setVisible(true)
        this.addButtonPulseEffect(this.uiElements.playCardsButton)
      }
      if (this.uiElements.passButton) {
        this.uiElements.passButton.setVisible(true)
      }
      if (this.uiElements.hintButton) {
        this.uiElements.hintButton.setVisible(true)
      }
    }
  }

  addButtonPulseEffect(button) {
    // 为按钮添加脉冲效果
    this.tweens.add({
      targets: button,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 600,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  displayPlayerCards() {
    console.log('🃏 开始显示所有玩家手牌')

    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    console.log('👤 玩家1信息:', player1)
    if (player1 && player1.hand) {
      const cards = player1.hand.getCards()
      console.log('🎴 玩家1手牌数量:', cards.length)
      console.log('🎴 手牌详情:', cards.map(card => card.getDisplayName()))
      this.displayBottomPlayerCards(cards)
    } else {
      console.log('❌ 玩家1或手牌不存在')
    }

    // 显示玩家2（左侧）的手牌背面
    const player2 = this.gameState.getPlayer(2)
    console.log('👤 玩家2信息:', player2)
    if (player2 && player2.hand) {
      const cardCount = player2.hand.getCards().length
      console.log('🎴 玩家2手牌数量:', cardCount)
      this.displayLeftPlayerCards(cardCount)
    }

    // 显示玩家3（右侧）的手牌背面
    const player3 = this.gameState.getPlayer(3)
    console.log('👤 玩家3信息:', player3)
    if (player3 && player3.hand) {
      const cardCount = player3.hand.getCards().length
      console.log('🎴 玩家3手牌数量:', cardCount)
      this.displayRightPlayerCards(cardCount)
    }
  }

  displayBottomPlayerCards(cards) {
    console.log(`🎴 显示底部玩家手牌，数量: ${cards.length}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.bottom) {
      this.playerHandAreas.bottom = { cards: [] }
      console.log('🔧 初始化底部手牌区域')
    }

    // 清除之前的卡牌
    console.log(`🗑️ 清除之前的 ${this.playerHandAreas.bottom.cards.length} 张卡牌`)
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 响应式手牌布局 - 根据屏幕大小调整
    const totalCards = cards.length
    const gameWidth = this.scale.gameSize.width
    const gameHeight = this.scale.gameSize.height
    const scaleFactor = Math.min(gameWidth / 1200, gameHeight / 800)

    // 响应式卡牌尺寸
    const cardWidth = Math.max(50, 60 * scaleFactor)
    const _cardHeight = Math.max(70, 84 * scaleFactor) // 标记为未使用但保留

    // 重叠式线性排列 - 类似真实扑克牌
    // 计算卡牌重叠间距
    let cardOverlap = Math.max(15, 25 * scaleFactor) // 基础重叠距离

    // 根据卡牌数量动态调整重叠度
    if (totalCards > 10) {
      cardOverlap = Math.max(12, 20 * scaleFactor) // 卡牌多时增加重叠
    }
    if (totalCards > 15) {
      cardOverlap = Math.max(10, 15 * scaleFactor) // 卡牌很多时进一步增加重叠
    }
    if (totalCards > 20) {
      cardOverlap = Math.max(8, 12 * scaleFactor) // 极多卡牌时最大重叠
    }

    // 计算总宽度和起始位置
    const totalWidth = cardWidth + (totalCards - 1) * cardOverlap
    const startX = (gameWidth - totalWidth) / 2 + cardWidth / 2
    const baseY = Math.max(650, 720 * scaleFactor) // 手牌Y位置

    console.log(`📐 布局计算: 总卡牌${totalCards}张, 重叠距离${cardOverlap}px, 总宽度${totalWidth.toFixed(1)}px, 起始X${startX.toFixed(1)}px`)
    console.log(`📐 布局模式: 重叠式线性排列`)

    console.log(`🎴 开始创建 ${totalCards} 张手牌`)
    cards.forEach((card, index) => {
      // 重叠式线性排列 - 所有卡牌都使用这种布局
      const x = startX + index * cardOverlap
      const y = baseY
      // 不旋转，保持水平

      console.log(`🎴 创建第 ${index + 1} 张牌: ${card.getDisplayName()} 位置:(${x.toFixed(1)}, ${y.toFixed(1)})`)

      const cardSprite = this.createCardSprite(x, y, card)

      // 检查卡牌精灵是否创建成功
      if (cardSprite) {
        console.log(`✅ 卡牌精灵创建成功: ${card.getDisplayName()}`)

        // 调整卡牌层级 - 右边的卡牌层级更高（后抓的牌在上面）
        cardSprite.setDepth(1000 + index)

        // 确保卡牌可见
        cardSprite.setVisible(true)

        // 检查卡牌位置
        console.log(`📍 卡牌最终位置: (${cardSprite.x}, ${cardSprite.y}), 可见性: ${cardSprite.visible}`)

        this.playerHandAreas.bottom.cards.push(cardSprite)
      } else {
        console.error(`❌ 卡牌精灵创建失败: ${card.getDisplayName()}`)
      }
    })

    console.log(`✅ 手牌显示完成，共 ${this.playerHandAreas.bottom.cards.length} 张`)
  }

  displayLeftPlayerCards(cardCount) {
    console.log(`🎴 显示左侧玩家手牌背面，数量: ${cardCount}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.left) {
      this.playerHandAreas.left = { cards: [] }
    }

    // 清除之前的卡牌
    this.playerHandAreas.left.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.left.cards = []

    // 左侧玩家手牌垂直排列 - 改进的扇形布局
    const cardWidth = 40 // 侧面卡牌稍大
    const cardHeight = 56
    const cardOverlap = 18 // 垂直重叠距离

    // 扇形参数
    const fanAngle = Math.min(30, cardCount * 1.5) // 最大30度扇形
    const fanRadius = 200 // 扇形半径
    const centerX = 120 // 左侧中心X
    const centerY = 400 // 垂直中心

    for (let i = 0; i < cardCount; i++) {
      let x, y, rotation

      if (cardCount <= 5) {
        // 少量卡牌使用直线排列
        const totalHeight = cardHeight + (cardCount - 1) * cardOverlap
        const startY = (700 - totalHeight) / 2 + cardHeight / 2
        x = centerX
        y = startY + i * cardOverlap
        rotation = -Math.PI / 2
      } else {
        // 多量卡牌使用扇形排列
        const angleStep = fanAngle / (cardCount - 1)
        const currentAngle = (i * angleStep - fanAngle / 2) * (Math.PI / 180)

        x = centerX + Math.cos(currentAngle + Math.PI / 2) * fanRadius * 0.2
        y = centerY + Math.sin(currentAngle + Math.PI / 2) * fanRadius * 0.8
        rotation = -Math.PI / 2 + currentAngle * 0.3
      }

      // 创建卡背精灵
      const cardBack = this.createCardBackSprite(x, y, cardWidth, cardHeight)
      if (cardBack) {
        cardBack.setDepth(500 + i)
        cardBack.setRotation(rotation)

        // 添加轻微的随机偏移增加自然感
        cardBack.x += (Math.random() - 0.5) * 3
        cardBack.y += (Math.random() - 0.5) * 3

        this.playerHandAreas.left.cards.push(cardBack)
      }
    }

    console.log(`✅ 左侧玩家手牌背面显示完成，共 ${this.playerHandAreas.left.cards.length} 张`)
  }

  displayRightPlayerCards(cardCount) {
    console.log(`🎴 显示右侧玩家手牌背面，数量: ${cardCount}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.right) {
      this.playerHandAreas.right = { cards: [] }
    }

    // 清除之前的卡牌
    this.playerHandAreas.right.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.right.cards = []

    // 右侧玩家手牌垂直排列 - 改进的扇形布局
    const cardWidth = 40 // 侧面卡牌稍大
    const cardHeight = 56
    const cardOverlap = 18 // 垂直重叠距离

    // 扇形参数
    const fanAngle = Math.min(30, cardCount * 1.5) // 最大30度扇形
    const fanRadius = 200 // 扇形半径
    const centerX = 1080 // 右侧中心X
    const centerY = 400 // 垂直中心

    for (let i = 0; i < cardCount; i++) {
      let x, y, rotation

      if (cardCount <= 5) {
        // 少量卡牌使用直线排列
        const totalHeight = cardHeight + (cardCount - 1) * cardOverlap
        const startY = (700 - totalHeight) / 2 + cardHeight / 2
        x = centerX
        y = startY + i * cardOverlap
        rotation = Math.PI / 2
      } else {
        // 多量卡牌使用扇形排列
        const angleStep = fanAngle / (cardCount - 1)
        const currentAngle = (i * angleStep - fanAngle / 2) * (Math.PI / 180)

        x = centerX - Math.cos(currentAngle + Math.PI / 2) * fanRadius * 0.2
        y = centerY + Math.sin(currentAngle + Math.PI / 2) * fanRadius * 0.8
        rotation = Math.PI / 2 - currentAngle * 0.3
      }

      // 创建卡背精灵
      const cardBack = this.createCardBackSprite(x, y, cardWidth, cardHeight)
      if (cardBack) {
        cardBack.setDepth(500 + i)
        cardBack.setRotation(rotation)

        // 添加轻微的随机偏移增加自然感
        cardBack.x += (Math.random() - 0.5) * 3
        cardBack.y += (Math.random() - 0.5) * 3

        this.playerHandAreas.right.cards.push(cardBack)
      }
    }

    console.log(`✅ 右侧玩家手牌背面显示完成，共 ${this.playerHandAreas.right.cards.length} 张`)
  }

  createCardBackSprite(x, y, width = 60, height = 84) {
    try {
      // 创建卡背容器
      const cardBackContainer = this.add.container(x, y)

      // 卡牌阴影
      const shadow = this.add.graphics()
      shadow.fillStyle(0x000000, 0.4)
      shadow.fillRoundedRect(-width/2 + 2, -height/2 + 2, width, height, 6)
      cardBackContainer.add(shadow)

      // 卡牌背景 - 高质量渐变
      const cardBack = this.add.graphics()
      cardBack.fillGradientStyle(0x1a237e, 0x3949ab, 0x283593, 0x5c6bc0, 1)
      cardBack.fillRoundedRect(-width/2, -height/2, width, height, 8)

      // 外边框 - 金色
      cardBack.lineStyle(2, 0xFFD700, 0.8)
      cardBack.strokeRoundedRect(-width/2, -height/2, width, height, 8)

      // 内边框 - 银色
      cardBack.lineStyle(1, 0xC0C0C0, 0.6)
      cardBack.strokeRoundedRect(-width/2 + 3, -height/2 + 3, width - 6, height - 6, 6)

      cardBackContainer.add(cardBack)

      // 中央装饰图案 - 更复杂的设计
      const centerPattern = this.add.graphics()

      // 中央圆形
      centerPattern.fillStyle(0x7986cb, 0.7)
      centerPattern.fillCircle(0, 0, Math.min(width, height) * 0.2)

      // 装饰线条
      centerPattern.lineStyle(1, 0x9fa8da, 0.8)
      for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2
        const innerRadius = Math.min(width, height) * 0.1
        const outerRadius = Math.min(width, height) * 0.3
        const x1 = Math.cos(angle) * innerRadius
        const y1 = Math.sin(angle) * innerRadius
        const x2 = Math.cos(angle) * outerRadius
        const y2 = Math.sin(angle) * outerRadius
        centerPattern.lineBetween(x1, y1, x2, y2)
      }

      cardBackContainer.add(centerPattern)

      // 添加微妙的闪烁效果
      this.tweens.add({
        targets: centerPattern,
        alpha: 0.4,
        duration: 2000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      })

      return cardBackContainer
    } catch (error) {
      console.error('❌ 创建卡背精灵失败:', error)
      return null
    }
  }



  createCardSprite(x, y, card) {
    console.log(`🎴 创建卡牌精灵: ${card.getDisplayName()} 位置:(${x}, ${y})`)

    // 响应式卡牌尺寸 - 根据屏幕大小调整
    const gameWidth = this.scale.gameSize.width
    const gameHeight = this.scale.gameSize.height
    const scaleFactor = Math.min(gameWidth / 1200, gameHeight / 800)

    const baseWidth = 60
    const baseHeight = 84
    const displayWidth = Math.max(50, baseWidth * scaleFactor)   // 最小50px宽度
    const displayHeight = Math.max(70, baseHeight * scaleFactor) // 最小70px高度

    console.log(`📏 卡牌尺寸: ${displayWidth.toFixed(1)}x${displayHeight.toFixed(1)} (缩放因子: ${scaleFactor.toFixed(2)})`)

    // 创建卡牌容器
    const cardContainer = this.add.container(x, y)

    // 卡牌阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(-displayWidth/2 + 2, -displayHeight/2 + 2, displayWidth, displayHeight, 8)
    cardContainer.add(shadow)

    // 按照标准扑克牌样式显示
    console.log(`🎴 使用标准扑克牌样式显示: ${card.getDisplayName()}`)

    // 创建标准扑克牌背景
    const cardBg = this.add.graphics()

    // 纯白色背景
    cardBg.fillStyle(0xFFFFFF, 1)
    cardBg.fillRoundedRect(-displayWidth/2, -displayHeight/2, displayWidth, displayHeight, 6)

    // 黑色边框
    cardBg.lineStyle(1, 0x000000, 1)
    cardBg.strokeRoundedRect(-displayWidth/2, -displayHeight/2, displayWidth, displayHeight, 6)

    cardContainer.add(cardBg)

    // 标准扑克牌内容布局
    const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
    const cardColor = isRed ? '#DC143C' : '#000000'

    // 处理王牌特殊显示
    if (card.suit === 'joker') {
      const jokerColor = card.rank === 'small_joker' ? '#000000' : '#DC143C'
      const jokerText = card.rank === 'small_joker' ? 'JOKER' : 'JOKER'
      const jokerSize = card.rank === 'small_joker' ? '小王' : '大王'

      // 左上角标识
      const topJoker = this.add.text(-displayWidth/2 + 4, -displayHeight/2 + 4, jokerSize, {
        fontSize: '8px',
        color: jokerColor,
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0, 0)
      cardContainer.add(topJoker)

      // 右下角标识（倒置）
      const bottomJoker = this.add.text(displayWidth/2 - 4, displayHeight/2 - 4, jokerSize, {
        fontSize: '8px',
        color: jokerColor,
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(1, 1).setRotation(Math.PI)
      cardContainer.add(bottomJoker)

      // 中央显示JOKER
      const centerJoker = this.add.text(0, 0, jokerText, {
        fontSize: '16px',
        color: jokerColor,
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0.5)
      cardContainer.add(centerJoker)
    } else {
      // 标准扑克牌布局 - 左上角和右下角都有数字+花色

      // 左上角数字/字母
      const topRank = this.add.text(-displayWidth/2 + 4, -displayHeight/2 + 4, card.rank, {
        fontSize: '10px',
        color: cardColor,
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0, 0)
      cardContainer.add(topRank)

      // 左上角花色（在数字下方）
      const topSuit = this.add.text(-displayWidth/2 + 4, -displayHeight/2 + 15, this.getSuitSymbol(card.suit), {
        fontSize: '10px',
        color: cardColor,
        fontFamily: 'Arial'
      }).setOrigin(0, 0)
      cardContainer.add(topSuit)

      // 右下角数字/字母（倒置）
      const bottomRank = this.add.text(displayWidth/2 - 4, displayHeight/2 - 4, card.rank, {
        fontSize: '10px',
        color: cardColor,
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(1, 1).setRotation(Math.PI)
      cardContainer.add(bottomRank)

      // 右下角花色（倒置，在数字下方）
      const bottomSuit = this.add.text(displayWidth/2 - 4, displayHeight/2 - 15, this.getSuitSymbol(card.suit), {
        fontSize: '10px',
        color: cardColor,
        fontFamily: 'Arial'
      }).setOrigin(1, 1).setRotation(Math.PI)
      cardContainer.add(bottomSuit)

      // 中央大花色符号 - 清晰易识别
      const centerSuit = this.add.text(0, 0, this.getSuitSymbol(card.suit), {
        fontSize: '20px',
        color: cardColor,
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      cardContainer.add(centerSuit)
    }

    // 设置交互
    cardContainer.setSize(displayWidth, displayHeight)
    cardContainer.setInteractive()
    cardContainer.cardData = card
    cardContainer.selected = false
    cardContainer.originalY = y

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      console.log(`🖱️ 点击了卡牌: ${card.getDisplayName()}`)
      this.selectCard(cardContainer)
      // 播放卡牌音效
      if (this.playSound) {
        this.playSound('cardSound', 0.2)
      }
    })

    // 添加悬停效果
    cardContainer.on('pointerover', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1.05)
        cardContainer.setTint(0xdddddd) // 轻微变暗
      }
    })

    cardContainer.on('pointerout', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1)
        cardContainer.clearTint() // 恢复原色
      }
    })

    console.log(`✅ 卡牌容器创建成功: ${card.getDisplayName()} 位置:(${x}, ${y})`)
    return cardContainer
  }

  getCardSpritePosition(card) {
    // 卡牌雪碧图映射表
    const CARD_SPRITE_MAP = {
      // 黑桃 (Spades)
      'spades_3': { x: 238, y: 646, width: 116, height: 159 },
      'spades_4': { x: 120, y: 646, width: 116, height: 159 },
      'spades_5': { x: 2, y: 646, width: 116, height: 159 },
      'spades_6': { x: 1418, y: 485, width: 116, height: 159 },
      'spades_7': { x: 1300, y: 485, width: 116, height: 159 },
      'spades_8': { x: 1182, y: 485, width: 116, height: 159 },
      'spades_9': { x: 1064, y: 485, width: 116, height: 159 },
      'spades_10': { x: 946, y: 485, width: 116, height: 159 },
      'spades_J': { x: 828, y: 485, width: 116, height: 159 }, // J
      'spades_Q': { x: 710, y: 485, width: 116, height: 159 }, // Q
      'spades_K': { x: 592, y: 485, width: 116, height: 159 }, // K
      'spades_A': { x: 474, y: 485, width: 116, height: 159 }, // A
      'spades_2': { x: 356, y: 485, width: 116, height: 159 }, // 2

      // 红桃 (Hearts)
      'hearts_3': { x: 238, y: 485, width: 116, height: 159 },
      'hearts_4': { x: 120, y: 485, width: 116, height: 159 },
      'hearts_5': { x: 2, y: 485, width: 116, height: 159 },
      'hearts_6': { x: 1418, y: 324, width: 116, height: 159 },
      'hearts_7': { x: 1300, y: 324, width: 116, height: 159 },
      'hearts_8': { x: 1182, y: 324, width: 116, height: 159 },
      'hearts_9': { x: 1064, y: 324, width: 116, height: 159 },
      'hearts_10': { x: 946, y: 324, width: 116, height: 159 },
      'hearts_J': { x: 828, y: 324, width: 116, height: 159 }, // J
      'hearts_Q': { x: 710, y: 324, width: 116, height: 159 }, // Q
      'hearts_K': { x: 592, y: 324, width: 116, height: 159 }, // K
      'hearts_A': { x: 474, y: 324, width: 116, height: 159 }, // A
      'hearts_2': { x: 356, y: 324, width: 116, height: 159 }, // 2

      // 梅花 (Clubs)
      'clubs_3': { x: 238, y: 324, width: 116, height: 159 },
      'clubs_4': { x: 120, y: 324, width: 116, height: 159 },
      'clubs_5': { x: 2, y: 324, width: 116, height: 159 },
      'clubs_6': { x: 1418, y: 163, width: 116, height: 159 },
      'clubs_7': { x: 1300, y: 163, width: 116, height: 159 },
      'clubs_8': { x: 1182, y: 163, width: 116, height: 159 },
      'clubs_9': { x: 1064, y: 163, width: 116, height: 159 },
      'clubs_10': { x: 946, y: 163, width: 116, height: 159 },
      'clubs_J': { x: 828, y: 163, width: 116, height: 159 }, // J
      'clubs_Q': { x: 710, y: 163, width: 116, height: 159 }, // Q
      'clubs_K': { x: 592, y: 163, width: 116, height: 159 }, // K
      'clubs_A': { x: 474, y: 163, width: 116, height: 159 }, // A
      'clubs_2': { x: 356, y: 163, width: 116, height: 159 }, // 2

      // 方块 (Diamonds)
      'diamonds_3': { x: 238, y: 163, width: 116, height: 159 },
      'diamonds_4': { x: 120, y: 163, width: 116, height: 159 },
      'diamonds_5': { x: 2, y: 163, width: 116, height: 159 },
      'diamonds_6': { x: 1418, y: 2, width: 116, height: 159 },
      'diamonds_7': { x: 1300, y: 2, width: 116, height: 159 },
      'diamonds_8': { x: 1182, y: 2, width: 116, height: 159 },
      'diamonds_9': { x: 1064, y: 2, width: 116, height: 159 },
      'diamonds_10': { x: 946, y: 2, width: 116, height: 159 },
      'diamonds_J': { x: 828, y: 2, width: 116, height: 159 }, // J
      'diamonds_Q': { x: 710, y: 2, width: 116, height: 159 }, // Q
      'diamonds_K': { x: 592, y: 2, width: 116, height: 159 }, // K
      'diamonds_A': { x: 474, y: 2, width: 116, height: 159 }, // A
      'diamonds_2': { x: 356, y: 2, width: 116, height: 159 }, // 2

      // 王牌
      'joker_small': { x: 2, y: 2, width: 116, height: 159 }, // 小王
      'joker_big': { x: 120, y: 2, width: 116, height: 159 }, // 大王
    }

    let key
    if (card.suit === 'joker') {
      // 王牌：统一使用 small/big 标识
      key = card.rank === 'small_joker' ? 'joker_small' : 'joker_big'
    } else {
      // 普通牌：使用suit和rank
      key = `${card.suit}_${card.rank}`
    }

    console.log(`🔍 查找卡牌映射: ${card.getDisplayName()} -> ${key}`)
    const result = CARD_SPRITE_MAP[key]
    if (!result) {
      console.error(`❌ 未找到映射: ${key}`)
      console.log('可用的映射键:', Object.keys(CARD_SPRITE_MAP).slice(0, 10))
      console.log('卡牌信息:', { suit: card.suit, rank: card.rank })
    }
    return result
  }

  getCardImageKey(card) {
    // 生成卡牌图片的键名
    const suitMap = {
      'hearts': 'h',
      'diamonds': 'd',
      'clubs': 'c',
      'spades': 's'
    }

    const rankMap = {
      'A': '1', '2': '2', '3': '3', '4': '4', '5': '5', '6': '6', '7': '7',
      '8': '8', '9': '9', '10': '10', 'J': '11', 'Q': '12', 'K': '13'
    }

    if (card.rank === 'Joker') {
      return card.suit === 'red' ? 'card_joker_red' : 'card_joker_black'
    }

    const suit = suitMap[card.suit] || 'h'
    const rank = rankMap[card.rank] || '1'
    return `card_${suit}_${rank}`
  }

  createHighQualityCard(container, width, height, card) {
    // 创建高质量的自定义卡牌

    // 卡牌背景 - 渐变效果
    const cardBg = this.add.graphics()
    cardBg.fillGradientStyle(0xFFFFF8, 0xFFFFF8, 0xF8F8F0, 0xF8F8F0, 1)
    cardBg.fillRoundedRect(-width/2, -height/2, width, height, 10)

    // 外边框 - 深色边框
    cardBg.lineStyle(2, 0x333333, 1)
    cardBg.strokeRoundedRect(-width/2, -height/2, width, height, 10)

    // 内边框 - 装饰边框
    cardBg.lineStyle(1, 0x666666, 0.5)
    cardBg.strokeRoundedRect(-width/2 + 3, -height/2 + 3, width - 6, height - 6, 8)

    container.add(cardBg)

    // 卡牌内容
    const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
    const cardColor = isRed ? '#DC143C' : '#000000'

    // 左上角数字/字母 - 更大更清晰
    const topText = this.add.text(-width/2 + 8, -height/2 + 8, card.rank, {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0, 0)
    container.add(topText)

    // 左上角花色 - 更大更清晰
    const topSuit = this.add.text(-width/2 + 8, -height/2 + 26, this.getSuitSymbol(card.suit), {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial'
    }).setOrigin(0, 0)
    container.add(topSuit)

    // 中央大号显示 - 更突出
    const centerText = this.add.text(0, -12, card.rank, {
      fontSize: '28px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: isRed ? '#8B0000' : '#333333',
      strokeThickness: 1
    }).setOrigin(0.5)
    container.add(centerText)

    const centerSuit = this.add.text(0, 12, this.getSuitSymbol(card.suit), {
      fontSize: '32px',
      color: cardColor,
      fontFamily: 'Arial',
      stroke: isRed ? '#8B0000' : '#333333',
      strokeThickness: 1
    }).setOrigin(0.5)
    container.add(centerSuit)

    // 右下角数字/字母（倒置）
    const bottomText = this.add.text(width/2 - 8, height/2 - 8, card.rank, {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(1, 1).setRotation(Math.PI)
    container.add(bottomText)

    // 右下角花色（倒置）
    const bottomSuit = this.add.text(width/2 - 8, height/2 - 26, this.getSuitSymbol(card.suit), {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial'
    }).setOrigin(1, 1).setRotation(Math.PI)
    container.add(bottomSuit)
  }

  getSuitSymbol(suit) {
    const symbols = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return symbols[suit] || '?'
  }



  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      // 取消选中 - 添加平滑动画
      this.tweens.add({
        targets: cardContainer,
        y: cardContainer.originalY,
        scaleX: 1,
        scaleY: 1,
        rotation: cardContainer.originalRotation || 0,
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          cardContainer.clearTint()
        }
      })

      cardContainer.selected = false

      // 移除选中光环效果
      this.removeSelectionGlow(cardContainer)

      // 添加取消选中的粒子效果
      this.createDeselectEffect(cardContainer)

      // 从选中列表中移除
      const index = this.selectedCards.findIndex(card => card.id === cardContainer.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      // 选中卡牌 - 添加平滑动画
      if (!cardContainer.originalY) {
        cardContainer.originalY = cardContainer.y
      }
      if (!cardContainer.originalRotation) {
        cardContainer.originalRotation = cardContainer.rotation
      }

      this.tweens.add({
        targets: cardContainer,
        y: cardContainer.originalY - 25,
        scaleX: 1.15,
        scaleY: 1.15,
        rotation: 0, // 选中时回正
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          // 添加选中成功的粒子效果
          this.createSelectEffect(cardContainer)
        }
      })

      cardContainer.selected = true
      cardContainer.setTint(0xFFFF88) // 更亮的黄色高亮

      // 添加选中光环效果
      this.createSelectionGlow(cardContainer)

      // 添加到选中列表
      this.selectedCards.push(cardContainer.cardData)
    }

    console.log('当前选中的牌:', this.selectedCards.map(card => card.getDisplayName()))

    // 验证选中的牌是否可以出
    this.validateSelectedCards()

    // 显示牌型提示
    this.showCardPatternHint()
  }

  createSelectionGlow(cardContainer) {
    // 创建选中光环效果 - 适应60x84的卡牌尺寸
    if (cardContainer.glowEffect) {
      cardContainer.glowEffect.destroy()
    }

    const glow = this.add.graphics()

    // 外层光环 - 金色，适应60x84的卡牌
    glow.lineStyle(4, 0xFFD700, 0.9)
    glow.strokeRoundedRect(-32, -44, 64, 88, 10)

    // 中层光环 - 白色
    glow.lineStyle(2, 0xFFFFFF, 0.8)
    glow.strokeRoundedRect(-30, -42, 60, 84, 8)

    // 内层光环 - 淡蓝色
    glow.lineStyle(1, 0x87CEEB, 0.6)
    glow.strokeRoundedRect(-28, -40, 56, 80, 6)

    // 添加到卡牌容器
    cardContainer.add(glow)
    cardContainer.glowEffect = glow

    // 添加脉冲动画
    this.tweens.add({
      targets: glow,
      alpha: 0.3,
      scaleX: 1.08,
      scaleY: 1.08,
      duration: 800,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })

    // 添加旋转光环效果
    const rotatingGlow = this.add.graphics()
    rotatingGlow.lineStyle(2, 0xFFD700, 0.4)
    rotatingGlow.strokeRoundedRect(-35, -47, 70, 94, 12)
    cardContainer.add(rotatingGlow)

    this.tweens.add({
      targets: rotatingGlow,
      rotation: Math.PI * 2,
      duration: 3000,
      repeat: -1,
      ease: 'Linear'
    })
  }

  removeSelectionGlow(cardContainer) {
    // 移除选中光环效果
    if (cardContainer.glowEffect) {
      cardContainer.glowEffect.destroy()
      cardContainer.glowEffect = null
    }
  }

  createSelectEffect(cardContainer) {
    // 创建选中成功的粒子效果
    const particles = []
    const particleCount = 8

    for (let i = 0; i < particleCount; i++) {
      const particle = this.add.graphics()
      particle.fillStyle(0xFFD700, 1)
      particle.fillStar(cardContainer.x, cardContainer.y, 5, 4, 8, 0)
      particles.push(particle)

      const angle = (i / particleCount) * Math.PI * 2
      const distance = 30 + Math.random() * 20
      const targetX = cardContainer.x + Math.cos(angle) * distance
      const targetY = cardContainer.y + Math.sin(angle) * distance

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        duration: 600,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  createDeselectEffect(cardContainer) {
    // 创建取消选中的粒子效果
    const particles = []
    const particleCount = 6

    for (let i = 0; i < particleCount; i++) {
      const particle = this.add.graphics()
      particle.fillStyle(0x87CEEB, 0.8)
      particle.fillCircle(cardContainer.x, cardContainer.y, 3)
      particles.push(particle)

      const angle = (i / particleCount) * Math.PI * 2
      const distance = 20 + Math.random() * 15
      const targetX = cardContainer.x + Math.cos(angle) * distance
      const targetY = cardContainer.y + Math.sin(angle) * distance

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.2,
        scaleY: 0.2,
        duration: 400,
        ease: 'Power1',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  showCardPatternHint() {
    // 清除之前的提示
    if (this.uiElements.patternHint) {
      this.uiElements.patternHint.destroy()
      this.uiElements.patternHint = null
    }

    if (this.selectedCards.length === 0) {
      return
    }

    // 识别牌型
    const pattern = this.gameState.getCardPattern().identifyPattern(this.selectedCards)
    let hintText = ''

    if (pattern) {
      const patternNames = {
        'single': '单牌',
        'pair': '对子',
        'triple': '三张',
        'triple_with_single': '三带一',
        'triple_with_pair': '三带二',
        'straight': '顺子',
        'pair_straight': '连对',
        'triple_straight': '飞机',
        'bomb': '炸弹',
        'rocket': '火箭'
      }
      hintText = patternNames[pattern.type] || pattern.type
    } else {
      hintText = '无效牌型'
    }

    // 创建华丽的提示容器
    const hintContainer = this.add.container(600, 620)

    // 提示背景
    const hintBg = this.add.graphics()
    const bgColor = pattern ? 0x00AA00 : 0xAA0000
    hintBg.fillStyle(bgColor, 0.8)
    hintBg.lineStyle(2, 0xFFD700, 1)
    hintBg.fillRoundedRect(-80, -20, 160, 40, 20)
    hintBg.strokeRoundedRect(-80, -20, 160, 40, 20)
    hintContainer.add(hintBg)

    // 提示文字
    const hintTextObj = this.add.text(0, 0, hintText, {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    hintContainer.add(hintTextObj)

    this.uiElements.patternHint = hintContainer

    // 添加弹出动画
    hintContainer.setScale(0)
    this.tweens.add({
      targets: hintContainer,
      scaleX: 1,
      scaleY: 1,
      duration: 300,
      ease: 'Back.easeOut'
    })

    // 添加脉冲效果
    this.tweens.add({
      targets: hintContainer,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 800,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  // 验证选中的牌
  validateSelectedCards() {
    if (this.selectedCards.length === 0) {
      // 没有选中牌，禁用出牌按钮
      if (this.uiElements.playCardsButton) {
        this.uiElements.playCardsButton.setAlpha(0.5)
      }
      return
    }

    const canPlay = this.gameState.validateSelectedCards(this.selectedCards)
    if (this.uiElements.playCardsButton) {
      this.uiElements.playCardsButton.setAlpha(canPlay ? 1 : 0.5)
    }

    // 显示牌型提示
    const pattern = this.gameState.getCardPattern().identifyPattern(this.selectedCards)
    if (pattern) {
      console.log('选中牌型:', pattern.type)
    } else {
      console.log('无效牌型')
    }
  }

  // 玩家叫牌 (bidValue: 0=不叫, 1=1分, 2=2分, 3=3分)
  playerBid(bidValue) {
    console.log(`玩家叫牌: ${bidValue === 0 ? '不叫' : bidValue + '分'}`)

    // 停止叫牌倒计时
    this.stopBiddingTimer()

    const success = this.gameState.bid(1, bidValue)
    if (success) {
      this.updateUI()

      // 隐藏叫牌UI
      this.hideBiddingUI()

      // 如果游戏还在叫牌阶段，继续AI叫牌
      if (this.gameState.phase === 'bidding') {
        setTimeout(() => {
          this.simulateAIBidding()
        }, 1000)
      } else if (this.gameState.phase === 'playing') {
        // 叫牌结束，开始游戏
        this.startPlayingPhase()
      }
    }
  }

  // 兼容旧方法
  bidLandlord() {
    this.playerBid(1) // 默认叫1分
  }

  passBid() {
    this.playerBid(0) // 不叫
  }

  // 显示叫牌UI
  showBiddingUI() {
    console.log('🎯 显示叫牌UI开始')
    const biddingInfo = this.gameState.getBiddingInfo()
    console.log('🎯 叫牌信息:', biddingInfo)

    // 先隐藏之前的UI
    this.hideBiddingUI()

    // 创建叫牌UI容器 - 使用固定位置
    console.log('🎯 创建新的叫牌UI容器')
    this.biddingUI = this.add.container(600, 400) // 使用固定的中心位置
    this.biddingUI.setDepth(10000) // 确保在最上层
    console.log('🎯 叫牌UI容器创建完成，位置:', this.biddingUI.x, this.biddingUI.y)

    // 启动叫牌倒计时
    this.startBiddingTimer()

    // 创建简单的测试UI
    console.log('🎯 创建叫牌UI元素')

    // 背景
    const bg = this.add.rectangle(0, 0, 500, 350, 0x000000, 0.9)
    bg.setStrokeStyle(4, 0xffd700)
    this.biddingUI.add(bg)
    console.log('🎯 背景创建完成')

    // 标题
    const title = this.add.text(0, -120, '叫地主', {
      fontSize: '28px',
      color: '#ffd700',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    this.biddingUI.add(title)
    console.log('🎯 标题创建完成')

    // 当前叫分信息
    const currentBidText = biddingInfo.currentBid === 0 ? '暂无' : `${biddingInfo.currentBid}分`
    const bidInfo = this.add.text(0, -60, `当前叫分: ${currentBidText}`, {
      fontSize: '18px',
      color: '#ffffff',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    this.biddingUI.add(bidInfo)
    console.log('🎯 叫分信息创建完成')

    // 简化的按钮 - 先创建一个测试按钮
    const testButton = this.add.rectangle(0, 0, 120, 50, 0x4CAF50)
    testButton.setStrokeStyle(2, 0xffffff)
    testButton.setInteractive({ useHandCursor: true })

    const testButtonText = this.add.text(0, 0, '叫1分', {
      fontSize: '16px',
      color: '#ffffff',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)

    testButton.on('pointerdown', () => {
      console.log('🎯 测试按钮被点击')
      this.playerBid(1)
    })

    this.biddingUI.add([testButton, testButtonText])
    console.log('🎯 测试按钮创建完成')

    // 不叫按钮
    const passButton = this.add.rectangle(0, 60, 120, 50, 0xf44336)
    passButton.setStrokeStyle(2, 0xffffff)
    passButton.setInteractive({ useHandCursor: true })

    const passText = this.add.text(0, 60, '不叫', {
      fontSize: '16px',
      color: '#ffffff',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)

    passButton.on('pointerdown', () => {
      console.log('🎯 不叫按钮被点击')
      this.playerBid(0)
    })

    this.biddingUI.add([passButton, passText])
    console.log('🎯 不叫按钮创建完成')

    // 显示UI
    this.biddingUI.setVisible(true)
    console.log('🎯 叫牌UI显示完成，可见性:', this.biddingUI.visible)
    console.log('🎯 叫牌UI子元素数量:', this.biddingUI.list.length)
  }

  // 隐藏叫牌UI
  hideBiddingUI() {
    if (this.biddingUI) {
      console.log('🎯 隐藏叫牌UI')
      this.biddingUI.destroy()
      this.biddingUI = null
    }
    // 停止叫牌倒计时
    this.stopBiddingTimer()
  }

  // 启动叫牌倒计时
  startBiddingTimer() {
    console.log('🎯 启动叫牌倒计时')

    // 停止之前的计时器
    this.stopBiddingTimer()

    // 初始化倒计时
    this.biddingTimeLeft = 15 // 15秒倒计时
    this.maxBiddingTime = 15

    // 更新计时器显示
    this.updateBiddingTimer()

    // 启动倒计时
    this.biddingTimerInterval = setInterval(() => {
      this.biddingTimeLeft--
      console.log('🎯 叫牌倒计时:', this.biddingTimeLeft)

      // 更新计时器显示
      this.updateBiddingTimer()

      // 时间到了，自动选择不叫
      if (this.biddingTimeLeft <= 0) {
        console.log('🎯 叫牌超时，自动选择不叫')
        this.stopBiddingTimer()
        this.playerBid(0) // 自动不叫
      }
    }, 1000)
  }

  // 停止叫牌倒计时
  stopBiddingTimer() {
    if (this.biddingTimerInterval) {
      console.log('🎯 停止叫牌倒计时')
      clearInterval(this.biddingTimerInterval)
      this.biddingTimerInterval = null
    }
  }

  // 更新叫牌倒计时显示
  updateBiddingTimer() {
    // 更新游戏计时器显示
    if (this.uiElements.timer) {
      this.uiElements.timer.setText(this.biddingTimeLeft.toString())

      // 更新进度圆环
      this.updateTimerProgress(this.biddingTimeLeft, this.maxBiddingTime)

      // 时间不足时变红并添加警告效果
      if (this.biddingTimeLeft <= 10) {
        this.uiElements.timer.setStyle({
          color: '#FF0000',
          fontSize: '22px'
        })

        // 添加紧急闪烁效果
        if (this.biddingTimeLeft <= 5) {
          this.tweens.add({
            targets: this.uiElements.timer,
            alpha: 0.3,
            duration: 300,
            yoyo: true,
            repeat: 1
          })
        }
      } else {
        this.uiElements.timer.setStyle({
          color: '#FFFFFF',
          fontSize: '20px'
        })
      }
    }

    // 显示计时器容器
    if (this.uiElements.timerContainer) {
      this.uiElements.timerContainer.setVisible(true)
    }
  }

  // 开始游戏阶段
  startPlayingPhase() {
    console.log('开始游戏阶段')
    this.displayPlayerCards()
    setTimeout(() => {
      this.simulateAIPlaying()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    console.log('🎯 simulateAIBidding - 游戏状态:', gameInfo.phase, '当前玩家:', gameInfo.currentPlayer)

    if (gameInfo.phase !== 'bidding') {
      console.log('❌ 不在叫牌阶段，退出')
      return
    }

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) {
      // 轮到玩家，显示叫牌UI
      console.log('🎯 轮到玩家叫牌，显示叫牌UI')
      this.showBiddingUI()
      return
    }

    // 智能AI叫牌逻辑：根据手牌质量决定叫分
    const aiPlayer = this.gameState.getPlayer(currentPlayer + 1)
    const bidValue = this.getAIBidValue(aiPlayer.hand.getCards())

    console.log(`AI玩家${currentPlayer + 1} ${bidValue === 0 ? '不叫' : '叫' + bidValue + '分'}`)
    const success = this.gameState.bid(currentPlayer + 1, bidValue)

    if (success) {
      this.updateUI()

      // 如果还在叫牌阶段，继续模拟
      if (this.gameState.phase === 'bidding') {
        setTimeout(() => {
          this.simulateAIBidding()
        }, 1500) // 稍微延长时间，让玩家看清AI的选择
      } else if (this.gameState.phase === 'playing') {
        // 叫牌结束，开始游戏
        this.startPlayingPhase()
      }
    }
  }

  // AI智能叫分算法 - 增强版
  getAIBidValue(cards) {
    const biddingInfo = this.gameState.getBiddingInfo()
    const currentBid = biddingInfo.currentBid

    // 评估手牌强度
    const handStrength = this.evaluateHandStrength(cards)

    console.log(`🤖 AI手牌强度评估: ${handStrength}分`)

    // 位置因素调整 - 先叫牌的玩家更保守
    let positionAdjustment = 0
    if (biddingInfo.biddingHistory.length === 0) {
      // 第一个叫牌的玩家，更保守
      positionAdjustment = -5
      console.log(`🎯 位置调整: 第一个叫牌，保守-5分`)
    } else if (biddingInfo.biddingHistory.length === 1) {
      // 第二个叫牌的玩家，稍微保守
      positionAdjustment = -2
      console.log(`🎯 位置调整: 第二个叫牌，稍保守-2分`)
    } else {
      // 最后一个叫牌的玩家，可以更激进
      positionAdjustment = 3
      console.log(`🎯 位置调整: 最后叫牌，激进+3分`)
    }

    // 应用位置调整
    const adjustedStrength = handStrength + positionAdjustment

    // 增加随机性因子 (-3到+3分)
    const randomFactor = Math.floor(Math.random() * 7) - 3
    const finalStrength = adjustedStrength + randomFactor

    console.log(`🎯 强度调整: 基础${handStrength} + 位置${positionAdjustment} + 随机${randomFactor} = ${finalStrength}`)

    // 根据调整后的手牌强度决定叫分策略
    let bidValue = 0

    if (finalStrength >= 58 && currentBid < 3) {
      // 超强手牌，叫3分
      bidValue = 3
    } else if (finalStrength >= 42 && currentBid < 2) {
      // 强手牌，叫2分
      bidValue = 2
    } else if (finalStrength >= 28 && currentBid < 1) {
      // 中等手牌，叫1分
      bidValue = 1
    } else {
      // 弱手牌或无法加价，不叫
      bidValue = 0
    }

    // 特殊情况：如果有双王+炸弹，即使分数不够也要叫牌
    const hasDoubleJoker = this.hasDoubleJoker(cards)
    const bombCount = this.countBombs(cards)
    if (hasDoubleJoker && bombCount >= 1 && currentBid < 2) {
      bidValue = Math.max(bidValue, 2)
      console.log(`🎯 特殊策略: 双王+炸弹，强制叫2分`)
    }

    console.log(`🎯 AI决策: 当前叫分${currentBid}, 最终强度${finalStrength}, 决定${bidValue === 0 ? '不叫' : '叫' + bidValue + '分'}`)

    return bidValue
  }

  // 评估手牌强度 (返回0-100的分数)
  evaluateHandStrength(cards) {
    let score = 0
    let analysis = { details: [], totalScore: 0 }

    // 统计各种牌型
    const cardCounts = {}
    const suitCounts = { hearts: 0, diamonds: 0, clubs: 0, spades: 0, joker: 0 }

    cards.forEach(card => {
      const key = card.rank
      cardCounts[key] = (cardCounts[key] || 0) + 1
      suitCounts[card.suit]++
    })

    // 王牌加分 (大王15分，小王10分)
    if (suitCounts.joker > 0) {
      let jokerScore = 0
      cards.forEach(card => {
        if (card.suit === 'joker') {
          jokerScore += card.rank === 'big_joker' ? 15 : 10
        }
      })
      score += jokerScore
      analysis.details.push(`王牌: +${jokerScore}分`)
    }

    // 炸弹和牌型加分
    let bombs = 0, triples = 0, pairs = 0
    Object.entries(cardCounts).forEach(([rank, count]) => {
      if (count >= 4) {
        bombs++
        score += 15
        analysis.details.push(`炸弹${rank}: +15分`)
      } else if (count === 3) {
        triples++
        score += 5
        analysis.details.push(`三张${rank}: +5分`)
      } else if (count === 2) {
        pairs++
        score += 2
        analysis.details.push(`对子${rank}: +2分`)
      }
    })

    // 大牌加分（A, K, Q, J, 2）
    const bigCards = ['A', 'K', 'Q', 'J', '2']
    let bigCardScore = 0
    bigCards.forEach(rank => {
      if (cardCounts[rank]) {
        const cardScore = cardCounts[rank] * 3
        bigCardScore += cardScore
        analysis.details.push(`大牌${rank}×${cardCounts[rank]}: +${cardScore}分`)
      }
    })
    score += bigCardScore

    // 连牌加分 - 检测顺子潜力
    const straightBonus = this.evaluateStraightPotential(cardCounts)
    if (straightBonus > 0) {
      score += straightBonus
      analysis.details.push(`连牌潜力: +${straightBonus}分`)
    }

    // 手牌平衡性评估 - 避免过于分散的手牌
    const balanceScore = this.evaluateHandBalance(cardCounts)
    score += balanceScore
    if (balanceScore !== 0) {
      analysis.details.push(`手牌平衡: ${balanceScore > 0 ? '+' : ''}${balanceScore}分`)
    }

    // 输出详细分析日志
    console.log(`🤖 AI手牌强度分析:`)
    console.log(`📋 手牌: ${cards.map(c => c.getDisplayName()).join(' ')}`)
    console.log(`📊 牌型统计: 炸弹${bombs}个, 三张${triples}个, 对子${pairs}个`)
    console.log(`📊 评分详情:`, analysis.details)
    console.log(`🎯 总分: ${score}分`)

    return Math.min(score, 100) // 限制在100分以内
  }

  // 检测是否有双王
  hasDoubleJoker(cards) {
    let bigJoker = false, smallJoker = false
    cards.forEach(card => {
      if (card.suit === 'joker') {
        if (card.rank === 'big_joker') bigJoker = true
        if (card.rank === 'small_joker') smallJoker = true
      }
    })
    return bigJoker && smallJoker
  }

  // 统计炸弹数量
  countBombs(cards) {
    const cardCounts = {}
    cards.forEach(card => {
      const key = card.rank
      cardCounts[key] = (cardCounts[key] || 0) + 1
    })

    let bombCount = 0
    Object.values(cardCounts).forEach(count => {
      if (count >= 4) bombCount++
    })

    return bombCount
  }

  // 评估顺子潜力
  evaluateStraightPotential(cardCounts) {
    const ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
    let maxConsecutive = 0
    let currentConsecutive = 0

    ranks.forEach(rank => {
      if (cardCounts[rank] && cardCounts[rank] >= 1) {
        currentConsecutive++
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive)
      } else {
        currentConsecutive = 0
      }
    })

    // 5张以上连续牌有顺子潜力
    if (maxConsecutive >= 5) {
      return Math.min(maxConsecutive - 4, 6) * 2 // 最多12分
    }
    return 0
  }

  // 评估手牌平衡性
  evaluateHandBalance(cardCounts) {
    const counts = Object.values(cardCounts)
    const singleCards = counts.filter(c => c === 1).length
    const totalCards = counts.reduce((sum, c) => sum + c, 0)

    // 单牌过多扣分
    if (singleCards > totalCards * 0.6) {
      return -5 // 单牌太多，不利于组牌
    }

    // 有一定数量的对子和三张加分
    const pairs = counts.filter(c => c === 2).length
    const triples = counts.filter(c => c === 3).length

    if (pairs >= 2 || triples >= 2) {
      return 3 // 牌型较好
    }

    return 0
  }

  // 兼容旧方法 - 用于简单的叫牌判断
  evaluateHandForBidding(cards) {
    const strength = this.evaluateHandStrength(cards)
    return strength >= 35 // 35分以上认为可以叫牌
  }

  // 出牌
  playSelectedCards() {
    if (this.selectedCards.length === 0) {
      console.log('没有选中任何牌')
      return
    }

    const result = this.gameState.playCards(1, this.selectedCards)
    if (result.success) {
      console.log('出牌成功')

      // 播放出牌动画
      this.playCardAnimation(this.selectedCards, () => {
        this.selectedCards = []
        this.updateUI()
        this.displayPlayerCards()

        if (result.gameOver) {
          console.log('游戏结束，玩家获胜！')
          this.uiElements.gamePhase.setText('游戏结束 - 你赢了！')
          this.showGameOverEffect()
        } else {
          // 模拟AI出牌
          setTimeout(() => {
            this.simulateAIPlaying()
          }, 1000)
        }
      })
    } else {
      console.log('出牌失败:', result.message)
      // 显示错误提示
      this.showErrorMessage(result.message)
    }
  }

  playCardAnimation(cards, callback) {
    // 找到选中的卡牌精灵
    const selectedSprites = this.playerHandAreas.bottom.cards.filter(sprite =>
      sprite.selected && cards.some(card => card.id === sprite.cardData.id)
    )

    if (selectedSprites.length === 0) {
      callback()
      return
    }

    // 播放出牌音效
    this.playSound('chupaiSound', 0.5)

    // 获取中央出牌区域位置
    const centerX = this.uiElements.centerPlayArea?.x || 600
    const centerY = this.uiElements.centerPlayArea?.y || 350

    // 改进的出牌动画 - 更流畅的弧形轨迹
    selectedSprites.forEach((sprite, index) => {
      // 计算目标位置 - 扇形排列
      const totalCards = selectedSprites.length
      const cardSpacing = Math.min(70, 300 / totalCards) // 动态间距
      const startX = centerX - (totalCards - 1) * cardSpacing / 2
      const targetX = startX + index * cardSpacing
      const targetY = centerY

      // 创建弧形轨迹动画
      const midX = (sprite.x + targetX) / 2
      const midY = Math.min(sprite.y, targetY) - 100 // 弧形高度

      // 第一阶段：上升到弧形顶点
      this.tweens.add({
        targets: sprite,
        x: midX,
        y: midY,
        scaleX: 1.3,
        scaleY: 1.3,
        rotation: (Math.random() - 0.5) * 0.3, // 轻微旋转
        duration: 300,
        ease: 'Power2.easeOut',
        delay: index * 80,
        onComplete: () => {
          // 第二阶段：下降到目标位置
          this.tweens.add({
            targets: sprite,
            x: targetX,
            y: targetY,
            scaleX: 1.1,
            scaleY: 1.1,
            rotation: 0,
            alpha: 0.9,
            duration: 300,
            ease: 'Power2.easeIn',
            onComplete: () => {
              // 添加落地效果
              this.createCardLandEffect(targetX, targetY)

              if (index === selectedSprites.length - 1) {
                // 最后一张牌动画完成后，等待一下再清理
                setTimeout(() => {
                  selectedSprites.forEach(s => s.destroy())
                  callback()
                }, 500)
              }
            }
          })
        }
      })
    })

    // 添加出牌粒子效果
    this.createCardPlayEffect(centerX, centerY)
  }

  updateGameInfoPanel(gameInfo) {
    // 更新轮次信息
    if (this.uiElements.roundInfo) {
      this.uiElements.roundInfo.setText(`第${gameInfo.round || 1}轮`)
    }

    // 更新底牌信息
    if (this.uiElements.landlordCardsInfo) {
      if (gameInfo.phase === 'playing' && gameInfo.landlordCards) {
        const cardsText = gameInfo.landlordCards.map(card => card.getDisplayName()).join(' ')
        this.uiElements.landlordCardsInfo.setText(`底牌: ${cardsText}`)
        this.uiElements.landlordCardsInfo.setStyle({ color: '#FFD700' })
      } else {
        this.uiElements.landlordCardsInfo.setText('底牌: 未翻开')
        this.uiElements.landlordCardsInfo.setStyle({ color: '#CCCCCC' })
      }
    }

    // 更新倍数信息
    if (this.uiElements.multiplierInfo) {
      const multiplier = gameInfo.multiplier || 1
      this.uiElements.multiplierInfo.setText(`倍数: x${multiplier}`)

      // 根据倍数改变颜色
      if (multiplier >= 4) {
        this.uiElements.multiplierInfo.setStyle({ color: '#FF0000' })
      } else if (multiplier >= 2) {
        this.uiElements.multiplierInfo.setStyle({ color: '#FFA500' })
      } else {
        this.uiElements.multiplierInfo.setStyle({ color: '#00FF00' })
      }
    }
  }

  updatePlayHistory(gameInfo) {
    if (gameInfo.lastPlayedCards && gameInfo.lastPlayedCards.length > 0) {
      if (this.uiElements.historyContainer) {
        this.uiElements.historyContainer.setVisible(true)
      }

      if (this.uiElements.playHistory) {
        const cardsText = gameInfo.lastPlayedCards.map(card => card.getDisplayName()).join(' ')
        const playerName = gameInfo.lastPlayerName || `玩家${gameInfo.lastPlayerId}`
        this.uiElements.playHistory.setText(`${playerName}: ${cardsText}`)
      }
    } else {
      if (this.uiElements.historyContainer) {
        this.uiElements.historyContainer.setVisible(false)
      }
    }
  }

  createCardPlayEffect(x, y) {
    // 创建出牌特效 - 更华丽的效果
    const particles = []
    const particleCount = 15

    for (let i = 0; i < particleCount; i++) {
      const particle = this.add.graphics()

      // 随机选择粒子类型
      const particleType = Math.random()
      if (particleType < 0.4) {
        // 星形粒子
        particle.fillStyle(0xFFD700, 1)
        particle.fillStar(x, y, 5, 4, 8, 0)
      } else if (particleType < 0.7) {
        // 圆形粒子
        particle.fillStyle(0xFF6B6B, 0.8)
        particle.fillCircle(x, y, 4)
      } else {
        // 菱形粒子
        particle.fillStyle(0x4ECDC4, 0.9)
        particle.fillTriangle(x, y-6, x-4, y+3, x+4, y+3)
      }

      particles.push(particle)

      const angle = (i / particleCount) * Math.PI * 2
      const distance = 60 + Math.random() * 40
      const targetX = x + Math.cos(angle) * distance
      const targetY = y + Math.sin(angle) * distance

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        rotation: Math.PI * 2,
        duration: 1000 + Math.random() * 500,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }

    // 添加冲击波效果
    this.createShockwaveEffect(x, y)
  }

  createCardLandEffect(x, y) {
    // 创建卡牌落地效果
    const ripples = []
    const rippleCount = 3

    for (let i = 0; i < rippleCount; i++) {
      const ripple = this.add.graphics()
      ripple.lineStyle(2, 0xFFFFFF, 0.6)
      ripple.strokeCircle(x, y, 5)
      ripples.push(ripple)

      this.tweens.add({
        targets: ripple,
        scaleX: 3 + i * 0.5,
        scaleY: 3 + i * 0.5,
        alpha: 0,
        duration: 600,
        delay: i * 100,
        ease: 'Power2',
        onComplete: () => {
          ripple.destroy()
        }
      })
    }

    // 添加闪光效果
    const flash = this.add.graphics()
    flash.fillStyle(0xFFFFFF, 0.8)
    flash.fillCircle(x, y, 15)

    this.tweens.add({
      targets: flash,
      alpha: 0,
      scaleX: 2,
      scaleY: 2,
      duration: 300,
      ease: 'Power2',
      onComplete: () => {
        flash.destroy()
      }
    })
  }

  createShockwaveEffect(x, y) {
    // 创建冲击波效果
    const shockwave = this.add.graphics()
    shockwave.lineStyle(3, 0xFFD700, 0.8)
    shockwave.strokeCircle(x, y, 10)

    this.tweens.add({
      targets: shockwave,
      scaleX: 5,
      scaleY: 5,
      alpha: 0,
      duration: 800,
      ease: 'Power2',
      onComplete: () => {
        shockwave.destroy()
      }
    })
  }

  showErrorMessage(message) {
    // 显示错误消息
    const errorText = this.add.text(400, 300, message, {
      fontSize: '20px',
      color: '#FF4444',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加闪烁效果
    this.tweens.add({
      targets: errorText,
      alpha: 0,
      duration: 300,
      yoyo: true,
      repeat: 3,
      onComplete: () => {
        errorText.destroy()
      }
    })
  }

  showGameOverEffect() {
    // 游戏结束特效
    const winText = this.add.text(400, 300, '恭喜获胜！', {
      fontSize: '36px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 3
    }).setOrigin(0.5)

    // 缩放动画
    this.tweens.add({
      targets: winText,
      scaleX: 1.5,
      scaleY: 1.5,
      duration: 1000,
      ease: 'Bounce.easeOut'
    })

    // 创建庆祝粒子效果
    this.createCelebrationEffect()
  }

  createCelebrationEffect() {
    // 创建庆祝烟花效果
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        this.createFirework(
          300 + Math.random() * 600,
          200 + Math.random() * 400
        )
      }, i * 300)
    }

    // 创建飘落的金币效果
    this.createGoldCoinEffect()
  }

  createFirework(x, y) {
    // 创建单个烟花效果
    const colors = [0xFFD700, 0xFF6347, 0x32CD32, 0x1E90FF, 0xFF69B4, 0xFFA500]
    const particleCount = 20

    for (let i = 0; i < particleCount; i++) {
      const particle = this.add.graphics()
      const color = colors[Math.floor(Math.random() * colors.length)]
      particle.fillStyle(color, 1)
      particle.fillCircle(x, y, 4)

      const angle = (i / particleCount) * Math.PI * 2
      const speed = 100 + Math.random() * 100
      const targetX = x + Math.cos(angle) * speed
      const targetY = y + Math.sin(angle) * speed

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        duration: 1000 + Math.random() * 500,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  createGoldCoinEffect() {
    // 创建金币飘落效果
    for (let i = 0; i < 15; i++) {
      setTimeout(() => {
        const coin = this.add.graphics()
        coin.fillStyle(0xFFD700, 1)
        coin.lineStyle(2, 0xFFA500, 1)
        coin.fillCircle(0, 0, 8)
        coin.strokeCircle(0, 0, 8)

        const startX = Math.random() * 1200
        coin.setPosition(startX, -20)

        this.tweens.add({
          targets: coin,
          y: 820,
          rotation: Math.PI * 4,
          duration: 3000 + Math.random() * 1000,
          ease: 'Power1',
          onComplete: () => {
            coin.destroy()
          }
        })
      }, i * 200)
    }
  }

  // 过牌
  passCards() {
    const result = this.gameState.pass(1)
    if (result.success) {
      console.log('过牌成功')
      this.selectedCards = []
      this.updateUI()

      // 模拟AI出牌
      setTimeout(() => {
        this.simulateAIPlaying()
      }, 1000)
    }
  }

  // 提示功能
  showHint() {
    console.log('显示提示')
    // TODO: 实现提示逻辑
  }

  // 模拟AI出牌
  simulateAIPlaying() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'playing') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    const aiPlayer = this.gameState.getPlayer(currentPlayer + 1)
    if (!aiPlayer || aiPlayer.hand.getCardCount() === 0) return

    // 智能AI出牌逻辑
    const bestPlay = this.findBestAIPlay(aiPlayer.hand.getCards(), gameInfo.lastPlayedCards)

    if (bestPlay && bestPlay.length > 0) {
      const result = this.gameState.playCards(currentPlayer + 1, bestPlay)

      if (result.success) {
        console.log(`AI玩家${currentPlayer + 1} 出牌成功:`, bestPlay.map(c => c.getDisplayName()).join(' '))
        this.updateUI()

        if (result.gameOver) {
          console.log(`AI玩家${currentPlayer + 1} 获胜！`)
          this.uiElements.gamePhase.setText(`游戏结束 - 玩家${currentPlayer + 1}赢了！`)
          return
        }
      } else {
        // 出牌失败，改为过牌
        this.gameState.pass(currentPlayer + 1)
        console.log(`AI玩家${currentPlayer + 1} 过牌（出牌失败）`)
      }
    } else {
      // AI选择过牌
      this.gameState.pass(currentPlayer + 1)
      console.log(`AI玩家${currentPlayer + 1} 过牌`)
    }

    this.updateUI()

    // 继续下一轮
    setTimeout(() => {
      this.simulateAIPlaying()
    }, 1000)
  }

  findBestAIPlay(cards, lastPlayedCards) {
    // 寻找AI的最佳出牌
    console.log(`🤖 AI出牌分析开始:`)
    console.log(`📋 AI手牌: ${cards.map(c => c.getDisplayName()).join(' ')}`)

    // 如果是首出，选择最小的单牌
    if (!lastPlayedCards || lastPlayedCards.length === 0) {
      console.log(`🎯 首次出牌，选择最小单牌`)
      const result = this.findSmallestSingle(cards)
      console.log(`✅ 选择出牌: ${result ? result.map(c => c.getDisplayName()).join(' ') : '无'}`)
      return result
    }

    // 尝试找到能够压过上家的牌
    console.log(`📤 上家出牌: ${lastPlayedCards.map(c => c.getDisplayName()).join(' ')}`)
    const lastPattern = this.gameState.getCardPattern().identifyPattern(lastPlayedCards)
    if (!lastPattern) {
      console.log(`❌ 无法识别上家牌型，选择过牌`)
      return null
    }

    console.log(`🎴 上家牌型: ${lastPattern.type}, 价值: ${lastPattern.value}`)

    let result = null
    // 根据上家牌型寻找对应的牌型
    switch (lastPattern.type) {
      case 'single':
        console.log(`🔍 寻找更大的单牌...`)
        result = this.findBiggerSingle(cards, lastPlayedCards[0])
        break
      case 'pair':
        console.log(`🔍 寻找更大的对子...`)
        result = this.findBiggerPair(cards, lastPattern)
        break
      case 'triple':
        console.log(`🔍 寻找更大的三张...`)
        result = this.findBiggerTriple(cards, lastPattern)
        break
      case 'bomb':
        console.log(`🔍 寻找更大的炸弹...`)
        result = this.findBiggerBomb(cards, lastPattern)
        break
      default:
        console.log(`⚠️ 复杂牌型暂不支持，选择过牌`)
        result = null
    }

    if (result) {
      console.log(`✅ 找到可出牌: ${result.map(c => c.getDisplayName()).join(' ')}`)
    } else {
      console.log(`❌ 无法压过，选择过牌`)
    }

    return result
  }

  findSmallestSingle(cards) {
    // 找到最小的单牌
    const sortedCards = [...cards].sort((a, b) => this.getCardValue(a) - this.getCardValue(b))
    return [sortedCards[0]]
  }

  findBiggerSingle(cards, lastCard) {
    // 找到比上家更大的单牌
    const lastValue = this.getCardValue(lastCard)
    const biggerCards = cards.filter(card => this.getCardValue(card) > lastValue)

    if (biggerCards.length > 0) {
      // 选择最小的能压过的牌
      const sortedBigger = biggerCards.sort((a, b) => this.getCardValue(a) - this.getCardValue(b))
      return [sortedBigger[0]]
    }
    return null
  }

  findBiggerPair(cards, lastPattern) {
    // 找到比上家更大的对子
    const pairs = this.findPairs(cards)
    const biggerPairs = pairs.filter(pair => this.getCardValue(pair[0]) > lastPattern.value)

    if (biggerPairs.length > 0) {
      const sortedPairs = biggerPairs.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedPairs[0]
    }
    return null
  }

  findBiggerTriple(cards, lastPattern) {
    // 找到比上家更大的三张
    const triples = this.findTriples(cards)
    const biggerTriples = triples.filter(triple => this.getCardValue(triple[0]) > lastPattern.value)

    if (biggerTriples.length > 0) {
      const sortedTriples = biggerTriples.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedTriples[0]
    }
    return null
  }

  findBiggerBomb(cards, lastPattern) {
    // 找到比上家更大的炸弹
    const bombs = this.findBombs(cards)
    const biggerBombs = bombs.filter(bomb => this.getCardValue(bomb[0]) > lastPattern.value)

    if (biggerBombs.length > 0) {
      const sortedBombs = biggerBombs.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedBombs[0]
    }
    return null
  }

  findPairs(cards) {
    // 找到所有对子
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const pairs = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 2) {
        pairs.push([group[0], group[1]])
      }
    })
    return pairs
  }

  findTriples(cards) {
    // 找到所有三张
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const triples = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 3) {
        triples.push([group[0], group[1], group[2]])
      }
    })
    return triples
  }

  findBombs(cards) {
    // 找到所有炸弹
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const bombs = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 4) {
        bombs.push(group)
      }
    })
    return bombs
  }

  getCardValue(card) {
    // 获取卡牌的数值用于比较
    const rankValues = {
      '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
      'small_joker': 16, 'big_joker': 17
    }
    return rankValues[card.rank] || 0
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 1200,
        height: 800,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH,
          min: {
            width: 800,
            height: 600
          },
          max: {
            width: 1600,
            height: 1200
          }
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
