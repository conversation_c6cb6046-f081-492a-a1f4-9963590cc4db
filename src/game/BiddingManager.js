// 叫牌状态管理器
import { LandlordSelection } from './LandlordSelection.js'

export class BiddingManager {
  constructor() {
    this.landlordSelection = new LandlordSelection()
    this.eventListeners = new Map()
    this.currentPlayerSeat = 'south' // 当前玩家座位（默认南家）
    this.players = {}
    this.gameState = {
      phase: 'waiting', // waiting, bidding, completed
      round: 1,
      gameNumber: 1
    }
    
    // 超时处理
    this.timeoutHandler = null
    this.timeLimit = 15 // 叫牌时限（秒）
  }

  // 事件监听器管理
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error)
        }
      })
    }
  }

  // 初始化游戏
  initGame(players, round = 1, gameNumber = 1, currentPlayerSeat = 'south') {
    this.players = { ...players }
    this.currentPlayerSeat = currentPlayerSeat
    this.gameState = {
      phase: 'bidding',
      round,
      gameNumber
    }

    // 初始化叫牌系统
    const firstBidder = this.landlordSelection.initGame(players, round, gameNumber)
    
    console.log(`🎮 叫牌管理器初始化完成`)
    console.log(`- 轮次: ${round}-${gameNumber}`)
    console.log(`- 首叫者: ${firstBidder}`)
    console.log(`- 当前玩家: ${currentPlayerSeat}`)

    // 开始超时计时
    this.startTimeout()

    // 触发游戏开始事件
    this.emit('gameStarted', {
      firstBidder,
      currentPlayer: currentPlayerSeat,
      gameState: this.gameState
    })

    // 触发状态更新事件
    this.emitStateUpdate()

    return firstBidder
  }

  // 执行叫牌
  makeBid(bidOption, playerCards = null) {
    const status = this.landlordSelection.getStatus()
    
    // 验证游戏状态
    if (this.gameState.phase !== 'bidding') {
      return { success: false, reason: '当前不在叫牌阶段' }
    }

    // 验证是否轮到当前玩家
    if (status.currentBidder !== this.currentPlayerSeat) {
      return { success: false, reason: '还没轮到您叫牌' }
    }

    // 清除超时计时器
    this.clearTimeout()

    // 执行叫牌
    const result = this.landlordSelection.makeBid(this.currentPlayerSeat, bidOption, playerCards)
    
    if (result.success) {
      console.log(`🎯 ${this.currentPlayerSeat} 叫牌: ${this.landlordSelection.getBidDisplayName(bidOption)}`)
      
      // 触发叫牌事件
      this.emit('bidMade', {
        player: this.currentPlayerSeat,
        bidOption,
        result,
        status: this.landlordSelection.getStatus()
      })

      // 检查是否完成叫牌
      if (result.completed) {
        this.handleBiddingCompleted(result)
      } else {
        // 继续下一个玩家的叫牌
        this.startTimeout()
      }

      // 触发状态更新
      this.emitStateUpdate()
    } else {
      console.error(`❌ 叫牌失败: ${result.reason}`)
      this.emit('bidError', {
        player: this.currentPlayerSeat,
        bidOption,
        reason: result.reason
      })
    }

    return result
  }

  // 处理叫牌完成
  handleBiddingCompleted(result) {
    this.gameState.phase = 'completed'
    this.clearTimeout()

    if (result.redeal) {
      console.log('🔄 所有玩家都不叫，需要重新发牌')
      this.emit('redealRequired', {
        reason: result.reason,
        gameState: this.gameState
      })
    } else if (result.landlord) {
      console.log(`🎉 叫牌结束，${result.landlord} 成为地主`)
      this.emit('landlordSelected', {
        landlord: result.landlord,
        gameState: this.gameState,
        finalStatus: this.landlordSelection.getStatus()
      })
    }
  }

  // 开始超时计时
  startTimeout() {
    this.clearTimeout()
    
    const status = this.landlordSelection.getStatus()
    if (status.currentBidder === this.currentPlayerSeat && this.gameState.phase === 'bidding') {
      this.timeoutHandler = setTimeout(() => {
        console.log(`⏰ ${this.currentPlayerSeat} 叫牌超时`)
        this.handleTimeout()
      }, this.timeLimit * 1000)
    }
  }

  // 清除超时计时器
  clearTimeout() {
    if (this.timeoutHandler) {
      clearTimeout(this.timeoutHandler)
      this.timeoutHandler = null
    }
  }

  // 处理超时
  handleTimeout() {
    const result = this.landlordSelection.handleTimeout(this.currentPlayerSeat)
    
    if (result.success) {
      console.log(`⏰ ${this.currentPlayerSeat} 超时，自动选择不叫`)
      
      this.emit('bidTimeout', {
        player: this.currentPlayerSeat,
        result,
        status: this.landlordSelection.getStatus()
      })

      // 检查是否完成叫牌
      if (result.completed) {
        this.handleBiddingCompleted(result)
      } else {
        // 继续下一个玩家的叫牌
        this.startTimeout()
      }

      // 触发状态更新
      this.emitStateUpdate()
    }
  }

  // 获取当前状态（用于UI更新）
  getCurrentState() {
    const status = this.landlordSelection.getStatus()
    
    return {
      // 游戏状态
      gameState: this.gameState,
      
      // 叫牌状态
      biddingStatus: status,
      
      // 当前玩家相关
      isMyTurn: status.currentBidder === this.currentPlayerSeat,
      currentPlayer: this.currentPlayerSeat,
      
      // 可用选项
      availableBids: this.landlordSelection.getAvailableBids(),
      
      // 时间设置
      timeLimit: this.timeLimit,
      
      // 玩家信息
      players: this.players
    }
  }

  // 触发状态更新事件
  emitStateUpdate() {
    const currentState = this.getCurrentState()
    this.emit('stateUpdate', currentState)
  }

  // 检查特殊牌型
  checkSpecialCards(cards) {
    return this.landlordSelection.hasSpecialCards(cards)
  }

  // 获取叫牌显示名称
  getBidDisplayName(bidOption) {
    return this.landlordSelection.getBidDisplayName(bidOption)
  }

  // 重置状态
  reset() {
    this.clearTimeout()
    this.landlordSelection.reset()
    this.gameState = {
      phase: 'waiting',
      round: 1,
      gameNumber: 1
    }
    this.players = {}
    
    this.emit('reset', {
      gameState: this.gameState
    })
  }

  // 设置时间限制
  setTimeLimit(seconds) {
    this.timeLimit = Math.max(1, Math.min(60, seconds)) // 限制在1-60秒之间
  }

  // 获取座位显示名称
  getSeatDisplayName(seat) {
    const seatNames = {
      west: '西家',
      south: '南家',
      east: '东家'
    }
    return seatNames[seat] || seat
  }

  // 模拟其他玩家叫牌（用于测试）
  simulateOtherPlayerBid(playerSeat, bidOption) {
    if (playerSeat === this.currentPlayerSeat) {
      console.warn('不能模拟当前玩家的叫牌')
      return { success: false, reason: '不能模拟当前玩家的叫牌' }
    }

    const status = this.landlordSelection.getStatus()
    if (status.currentBidder !== playerSeat) {
      console.warn(`不是 ${playerSeat} 的叫牌回合`)
      return { success: false, reason: `不是 ${playerSeat} 的叫牌回合` }
    }

    this.clearTimeout()
    const result = this.landlordSelection.makeBid(playerSeat, bidOption)
    
    if (result.success) {
      console.log(`🤖 ${playerSeat} (模拟) 叫牌: ${this.getBidDisplayName(bidOption)}`)
      
      this.emit('bidMade', {
        player: playerSeat,
        bidOption,
        result,
        status: this.landlordSelection.getStatus(),
        simulated: true
      })

      if (result.completed) {
        this.handleBiddingCompleted(result)
      } else {
        this.startTimeout()
      }

      this.emitStateUpdate()
    }

    return result
  }

  // 销毁管理器
  destroy() {
    this.clearTimeout()
    this.eventListeners.clear()
    this.landlordSelection.reset()
  }
}
