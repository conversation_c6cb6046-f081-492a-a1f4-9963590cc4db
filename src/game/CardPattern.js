// 牌型识别和验证类
export class CardPattern {
  constructor() {
    this.patterns = {
      SINGLE: 'single',           // 单牌
      PAIR: 'pair',              // 对子
      TRIPLE: 'triple',          // 三张
      TRIPLE_WITH_SINGLE: 'triple_with_single',  // 三带一
      TRIPLE_WITH_PAIR: 'triple_with_pair',      // 三带二
      STRAIGHT: 'straight',      // 顺子
      PAIR_STRAIGHT: 'pair_straight',  // 连对
      TRIPLE_STRAIGHT: 'triple_straight', // 飞机
      BOMB: 'bomb',              // 炸弹
      ROCKET: 'rocket'           // 火箭（双王）
    }
  }

  // 识别牌型
  identifyPattern(cards) {
    if (!cards || cards.length === 0) {
      return null
    }

    // 按点数分组
    const groups = this.groupByRank(cards)
    const groupSizes = Object.values(groups).map(group => group.length).sort((a, b) => b - a)
    const groupCount = Object.keys(groups).length

    // 调试信息
    console.log('🎴 牌型识别调试:')
    console.log('📋 输入牌:', cards.map(c => c.getDisplayName()).join(' '))
    console.log('📊 分组情况:', groups)
    console.log('📈 组大小:', groupSizes)
    console.log('🔢 组数量:', groupCount)

    // 火箭（双王）
    if (cards.length === 2 && this.isRocket(cards)) {
      return {
        type: this.patterns.ROCKET,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.ROCKET)
      }
    }

    // 炸弹
    if (cards.length === 4 && groupSizes[0] === 4) {
      console.log('✅ 识别为炸弹')
      return {
        type: this.patterns.BOMB,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.BOMB)
      }
    }

    // 四带二（四张+两张单牌）
    if (cards.length === 6 && groupSizes[0] === 4 && groupSizes[1] === 1 && groupSizes[2] === 1) {
      console.log('✅ 识别为四带二（单牌）')
      return {
        type: this.patterns.BOMB, // 四带二也算炸弹类型
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.BOMB)
      }
    }

    // 四带二（四张+一对）
    if (cards.length === 6 && groupSizes[0] === 4 && groupSizes[1] === 2) {
      console.log('✅ 识别为四带二（对子）')
      return {
        type: this.patterns.BOMB, // 四带二也算炸弹类型
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.BOMB)
      }
    }

    // 单牌
    if (cards.length === 1) {
      console.log('✅ 识别为单牌')
      return {
        type: this.patterns.SINGLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.SINGLE)
      }
    }

    // 对子
    if (cards.length === 2 && groupSizes[0] === 2) {
      console.log('✅ 识别为对子')
      return {
        type: this.patterns.PAIR,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.PAIR)
      }
    }

    // 三张
    if (cards.length === 3 && groupSizes[0] === 3) {
      console.log('✅ 识别为三张')
      return {
        type: this.patterns.TRIPLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE)
      }
    }

    // 三带一
    if (cards.length === 4 && groupSizes[0] === 3 && groupSizes[1] === 1) {
      console.log('✅ 识别为三带一')
      return {
        type: this.patterns.TRIPLE_WITH_SINGLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_WITH_SINGLE)
      }
    }

    // 三带二
    if (cards.length === 5 && groupSizes[0] === 3 && groupSizes[1] === 2) {
      console.log('✅ 识别为三带二')
      return {
        type: this.patterns.TRIPLE_WITH_PAIR,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_WITH_PAIR)
      }
    }

    // 顺子
    if (cards.length >= 5 && this.isStraight(cards)) {
      console.log('✅ 识别为顺子')
      return {
        type: this.patterns.STRAIGHT,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.STRAIGHT)
      }
    }

    // 连对
    if (cards.length >= 6 && cards.length % 2 === 0 && this.isPairStraight(cards)) {
      console.log('✅ 识别为连对')
      return {
        type: this.patterns.PAIR_STRAIGHT,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.PAIR_STRAIGHT)
      }
    }

    // 飞机（三张连续）
    if (cards.length >= 6 && this.isTripleStraight(cards)) {
      const tripleInfo = this.analyzeTripleStraight(cards)
      console.log('✅ 识别为飞机:', tripleInfo.subtype)
      return {
        type: this.patterns.TRIPLE_STRAIGHT,
        subtype: tripleInfo.subtype, // 'pure', 'with_singles', 'with_pairs'
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_STRAIGHT)
      }
    }

    console.log('❌ 无法识别牌型')
    return null // 无效牌型
  }

  // 按点数分组
  groupByRank(cards) {
    const groups = {}
    cards.forEach(card => {
      const rank = card.rank
      if (!groups[rank]) {
        groups[rank] = []
      }
      groups[rank].push(card)
    })
    return groups
  }

  // 检查是否为火箭
  isRocket(cards) {
    if (cards.length !== 2) return false
    const ranks = cards.map(card => card.rank).sort()
    return ranks[0] === 'small_joker' && ranks[1] === 'big_joker'
  }

  // 检查是否为顺子
  isStraight(cards) {
    if (cards.length < 5) return false

    // 王不能组成顺子
    if (cards.some(card => card.suit === 'joker')) return false

    // 获取所有牌的数值并去重排序
    const values = [...new Set(cards.map(card => card.value))].sort((a, b) => a - b)

    // 顺子必须是连续的不同点数
    if (values.length !== cards.length) return false

    // 检查是否连续
    for (let i = 1; i < values.length; i++) {
      if (values[i] !== values[i-1] + 1) {
        return false
      }
    }

    // 2不能在顺子中（斗地主规则：顺子最大到A）
    if (values.includes(15)) return false // 15是2的值

    // 顺子最大到A(14)
    if (values[values.length - 1] > 14) return false

    return true
  }

  // 检查是否为连对
  isPairStraight(cards) {
    if (cards.length < 6 || cards.length % 2 !== 0) return false

    const groups = this.groupByRank(cards)
    const ranks = Object.keys(groups)

    // 每个点数必须恰好有2张
    if (!ranks.every(rank => groups[rank].length === 2)) {
      return false
    }

    // 连对至少需要3对
    if (ranks.length < 3) return false

    // 检查点数是否连续
    const values = ranks.map(rank => {
      const card = groups[rank][0]
      return card.value
    }).sort((a, b) => a - b)

    // 2不能在连对中
    if (values.includes(15)) return false

    // 连对最大到A
    if (values[values.length - 1] > 14) return false

    return this.isConsecutive(values)
  }

  // 检查是否为飞机
  isTripleStraight(cards) {
    const groups = this.groupByRank(cards)
    const triples = []
    const others = []

    Object.entries(groups).forEach(([rank, cardGroup]) => {
      if (cardGroup.length >= 3) {
        triples.push(rank)
        // 如果有4张，剩下的1张作为带牌
        if (cardGroup.length === 4) {
          others.push(cardGroup[3])
        }
      } else {
        others.push(...cardGroup)
      }
    })

    if (triples.length < 2) return false

    // 检查三张是否连续
    const tripleValues = triples.map(rank => {
      const card = groups[rank][0]
      return card.value
    }).sort((a, b) => a - b)

    if (!this.isConsecutive(tripleValues)) return false

    // 2不能在飞机中
    if (tripleValues.includes(15)) return false

    // 飞机最大到A
    if (tripleValues[tripleValues.length - 1] > 14) return false

    // 检查带牌数量是否正确
    const expectedOthers = triples.length // 飞机带单牌
    const expectedOthers2 = triples.length * 2 // 飞机带对子

    // 飞机不带牌、带单牌、或带对子都是合法的
    if (others.length === 0) {
      console.log('🛩️ 飞机不带牌')
      return true
    } else if (others.length === expectedOthers) {
      console.log('🛩️ 飞机带单牌')
      return true
    } else if (others.length === expectedOthers2) {
      // 检查带牌是否都是对子
      const otherGroups = this.groupByRank(others)
      const allPairs = Object.values(otherGroups).every(group => group.length === 2)
      if (allPairs) {
        console.log('🛩️ 飞机带对子')
        return true
      }
    }

    console.log('❌ 飞机带牌数量不正确')
    return false
  }

  // 分析飞机牌型的详细信息
  analyzeTripleStraight(cards) {
    const groups = this.groupByRank(cards)
    const triples = []
    const others = []

    Object.entries(groups).forEach(([rank, cardGroup]) => {
      if (cardGroup.length >= 3) {
        triples.push(rank)
        if (cardGroup.length === 4) {
          others.push(cardGroup[3])
        }
      } else {
        others.push(...cardGroup)
      }
    })

    if (others.length === 0) {
      return { subtype: 'pure', triples: triples.length }
    } else if (others.length === triples.length) {
      return { subtype: 'with_singles', triples: triples.length }
    } else if (others.length === triples.length * 2) {
      const otherGroups = this.groupByRank(others)
      const allPairs = Object.values(otherGroups).every(group => group.length === 2)
      if (allPairs) {
        return { subtype: 'with_pairs', triples: triples.length }
      }
    }

    return { subtype: 'invalid', triples: triples.length }
  }

  // 检查数组是否连续
  isConsecutive(values) {
    for (let i = 1; i < values.length; i++) {
      if (values[i] !== values[i-1] + 1) {
        return false
      }
    }
    return true
  }

  // 计算牌型权重（用于比较大小）
  calculateWeight(cards, patternType) {
    const baseWeights = {
      [this.patterns.SINGLE]: 1000,
      [this.patterns.PAIR]: 2000,
      [this.patterns.TRIPLE]: 3000,
      [this.patterns.TRIPLE_WITH_SINGLE]: 4000,
      [this.patterns.TRIPLE_WITH_PAIR]: 5000,
      [this.patterns.STRAIGHT]: 6000,
      [this.patterns.PAIR_STRAIGHT]: 7000,
      [this.patterns.TRIPLE_STRAIGHT]: 8000,
      [this.patterns.BOMB]: 9000,
      [this.patterns.ROCKET]: 10000
    }

    const baseWeight = baseWeights[patternType] || 0

    // 获取主牌的点数（用于同类型比较）
    let mainValue = 0
    if (patternType === this.patterns.ROCKET) {
      mainValue = 100 // 火箭最大
    } else if (patternType === this.patterns.BOMB) {
      mainValue = cards[0].value // 炸弹取任意一张的值
    } else if (patternType === this.patterns.TRIPLE_WITH_SINGLE || patternType === this.patterns.TRIPLE_WITH_PAIR) {
      // 三带牌型以三张的值为主
      const groups = this.groupByRank(cards)
      const tripleRank = Object.keys(groups).find(rank => groups[rank].length === 3)
      mainValue = groups[tripleRank][0].value
    } else if (patternType === this.patterns.STRAIGHT || patternType === this.patterns.PAIR_STRAIGHT) {
      // 顺子和连对以最小值为主
      mainValue = Math.min(...cards.map(card => card.value))
    } else if (patternType === this.patterns.TRIPLE_STRAIGHT) {
      // 飞机以最小的三张为主
      const groups = this.groupByRank(cards)
      const triples = Object.keys(groups).filter(rank => groups[rank].length >= 3)
      const tripleValues = triples.map(rank => groups[rank][0].value).sort((a, b) => a - b)
      mainValue = tripleValues[0]
    } else {
      // 其他牌型取最大值的牌
      mainValue = Math.max(...cards.map(card => card.value))
    }

    return baseWeight + mainValue
  }

  // 比较两个牌型
  comparePatterns(pattern1, pattern2) {
    if (!pattern1 || !pattern2) return 0

    // 火箭最大
    if (pattern1.type === this.patterns.ROCKET) return 1
    if (pattern2.type === this.patterns.ROCKET) return -1

    // 炸弹大于其他牌型（除火箭）
    if (pattern1.type === this.patterns.BOMB && pattern2.type !== this.patterns.BOMB) return 1
    if (pattern2.type === this.patterns.BOMB && pattern1.type !== this.patterns.BOMB) return -1

    // 同类型比较
    if (pattern1.type === pattern2.type) {
      // 对于顺子、连对、飞机，还需要比较长度
      if (pattern1.type === this.patterns.STRAIGHT ||
          pattern1.type === this.patterns.PAIR_STRAIGHT ||
          pattern1.type === this.patterns.TRIPLE_STRAIGHT) {
        if (pattern1.cards.length !== pattern2.cards.length) {
          return 0 // 长度不同无法比较
        }
      }

      return pattern1.weight > pattern2.weight ? 1 : (pattern1.weight < pattern2.weight ? -1 : 0)
    }

    // 不同类型无法比较
    return 0
  }

  // 验证出牌是否合法
  canPlay(cards, lastPattern = null) {
    console.log('🔍 验证出牌是否合法')
    console.log('📋 当前出牌:', cards.map(c => c.getDisplayName()).join(' '))
    console.log('📋 上家出牌:', lastPattern ? `${lastPattern.type} (${lastPattern.cards.map(c => c.getDisplayName()).join(' ')})` : '无')

    const pattern = this.identifyPattern(cards)
    if (!pattern) {
      console.log('❌ 牌型识别失败')
      return { valid: false, reason: '无效的牌型组合' }
    }

    console.log('✅ 当前牌型:', pattern.type)

    // 如果没有上家出牌（首出），任何合法牌型都可以出
    if (!lastPattern) {
      console.log('✅ 首出牌，允许出牌')
      return { valid: true, pattern: pattern }
    }

    // 火箭（双王）可以压任何牌型
    if (pattern.type === this.patterns.ROCKET) {
      console.log('✅ 火箭可以压任何牌型')
      return { valid: true, pattern: pattern }
    }

    // 炸弹可以压除火箭外的任何牌型
    if (pattern.type === this.patterns.BOMB && lastPattern.type !== this.patterns.ROCKET) {
      console.log('✅ 炸弹可以压非火箭牌型')
      return { valid: true, pattern: pattern }
    }

    // 如果上家是火箭，只有火箭能压
    if (lastPattern.type === this.patterns.ROCKET) {
      console.log('❌ 无法压过火箭')
      return { valid: false, reason: '无法压过火箭' }
    }

    // 如果上家是炸弹，只有更大的炸弹或火箭能压
    if (lastPattern.type === this.patterns.BOMB && pattern.type !== this.patterns.BOMB) {
      console.log('❌ 只有炸弹能压炸弹')
      return { valid: false, reason: '只有炸弹能压炸弹' }
    }

    // 同类型牌型比较
    if (pattern.type === lastPattern.type) {
      // 对于顺子、连对、飞机，还需要比较长度和子类型
      if (pattern.type === this.patterns.STRAIGHT ||
          pattern.type === this.patterns.PAIR_STRAIGHT) {
        if (pattern.cards.length !== lastPattern.cards.length) {
          console.log('❌ 顺子/连对长度不匹配')
          return { valid: false, reason: '牌型长度不匹配' }
        }
      } else if (pattern.type === this.patterns.TRIPLE_STRAIGHT) {
        // 飞机需要比较长度和子类型
        if (pattern.cards.length !== lastPattern.cards.length) {
          console.log('❌ 飞机长度不匹配')
          return { valid: false, reason: '飞机长度不匹配' }
        }
        if (pattern.subtype !== lastPattern.subtype) {
          console.log('❌ 飞机子类型不匹配')
          return { valid: false, reason: '飞机类型不匹配（不带牌/带单牌/带对子）' }
        }
      }

      const comparison = this.comparePatterns(pattern, lastPattern)
      if (comparison > 0) {
        console.log('✅ 同类型牌型，当前更大')
        return { valid: true, pattern: pattern }
      } else {
        console.log('❌ 同类型牌型，当前较小')
        return { valid: false, reason: '牌型大小不够' }
      }
    }

    // 不同类型无法比较（除了炸弹和火箭的特殊情况）
    console.log('❌ 不同牌型无法比较')
    return { valid: false, reason: '牌型不匹配' }
  }

  // 获取详细的出牌提示信息
  getPlayHint(cards, lastPattern = null) {
    const pattern = this.identifyPattern(cards)

    if (!pattern) {
      return {
        valid: false,
        message: '无效的牌型组合',
        suggestions: this.getSuggestions(cards)
      }
    }

    if (!lastPattern) {
      return {
        valid: true,
        message: `可以出牌：${this.getPatternDisplayName(pattern)}`,
        pattern: pattern
      }
    }

    const validation = this.canPlay(cards, lastPattern)
    if (validation.valid) {
      return {
        valid: true,
        message: `可以出牌：${this.getPatternDisplayName(pattern)} 压过 ${this.getPatternDisplayName(lastPattern)}`,
        pattern: pattern
      }
    } else {
      return {
        valid: false,
        message: validation.reason,
        suggestions: this.getCounterSuggestions(lastPattern)
      }
    }
  }

  // 获取牌型显示名称
  getPatternDisplayName(pattern) {
    if (!pattern) return '未知牌型'

    const patternNames = {
      'single': '单牌',
      'pair': '对子',
      'triple': '三张',
      'triple_with_single': '三带一',
      'triple_with_pair': '三带二',
      'straight': '顺子',
      'pair_straight': '连对',
      'triple_straight': '飞机',
      'bomb': '炸弹',
      'rocket': '火箭'
    }

    let name = patternNames[pattern.type] || pattern.type

    // 添加飞机子类型信息
    if (pattern.type === 'triple_straight' && pattern.subtype) {
      const subtypeNames = {
        'pure': '不带牌',
        'with_singles': '带单牌',
        'with_pairs': '带对子'
      }
      name += `（${subtypeNames[pattern.subtype] || pattern.subtype}）`
    }

    return name
  }

  // 获取改进建议
  getSuggestions(cards) {
    const suggestions = []

    if (cards.length === 0) {
      suggestions.push('请选择要出的牌')
    } else if (cards.length === 1) {
      suggestions.push('单牌可以直接出')
    } else if (cards.length === 2) {
      suggestions.push('检查是否为对子或火箭')
    } else {
      suggestions.push('检查牌型组合是否正确')
      suggestions.push('常见牌型：三张、三带一、三带二、顺子、连对、飞机、炸弹')
    }

    return suggestions
  }

  // 获取反制建议
  getCounterSuggestions(lastPattern) {
    const suggestions = []

    switch (lastPattern.type) {
      case 'single':
        suggestions.push('需要更大的单牌、炸弹或火箭')
        break
      case 'pair':
        suggestions.push('需要更大的对子、炸弹或火箭')
        break
      case 'triple':
        suggestions.push('需要更大的三张、炸弹或火箭')
        break
      case 'bomb':
        suggestions.push('需要更大的炸弹或火箭')
        break
      case 'rocket':
        suggestions.push('火箭无法被压制')
        break
      default:
        suggestions.push('需要相同类型且更大的牌型、炸弹或火箭')
    }

    return suggestions
  }
}
