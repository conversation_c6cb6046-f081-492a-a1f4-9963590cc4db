import { Deck, PlayerHand } from './Card.js'
import { CardPattern } from './CardPattern.js'
import { EnhancedPlayValidator } from './EnhancedPlayValidator.js'

// 游戏状态管理类
export class GameState {
  constructor() {
    this.phase = 'waiting' // waiting, dealing, bidding, playing, finished
    this.players = [
      { id: 1, name: '玩家1 (我)', hand: new PlayerHand(), isLandlord: false, position: 'bottom' },
      { id: 2, name: '玩家2', hand: new PlayerHand(), isLandlord: false, position: 'left' },
      { id: 3, name: '玩家3', hand: new PlayerHand(), isLandlord: false, position: 'right' }
    ]
    this.deck = new Deck()
    this.landlordCards = [] // 地主牌（底牌）
    this.currentPlayer = 0 // 当前操作玩家索引
    this.biddingRound = 0 // 叫牌轮次
    this.biddingHistory = [] // 叫牌历史
    this.landlordId = null // 地主ID
    this.currentBid = 0 // 当前最高叫分 (0, 1, 2, 3)
    this.currentBidder = null // 当前最高叫分者ID
    this.lastPlayedCards = [] // 上一次出的牌
    this.lastPlayedPattern = null // 上一次出牌的牌型
    this.lastPlayerId = null // 上一次出牌的玩家ID
    this.passCount = 0 // 连续过牌次数
    this.cardPattern = new CardPattern() // 牌型识别器

    // 初始化增强出牌验证器
    this.enhancedValidator = new EnhancedPlayValidator(this.cardPattern)

    // 计分相关
    this.bombCount = 0 // 本局炸弹数量
    this.rocketCount = 0 // 本局火箭数量
    this.gameResult = null // 游戏结果
    this.scores = {} // 玩家得分
    this.playHistory = [] // 出牌历史，用于计分
  }

  // 开始游戏
  startGame() {
    console.log('GameState: 开始游戏')
    this.phase = 'dealing'
    this.deck.shuffle()
    this.dealCards()
  }

  // 发牌
  dealCards() {
    console.log('GameState: 发牌')
    
    // 每个玩家发17张牌
    this.players.forEach(player => {
      const cards = this.deck.deal(17)
      player.hand.addCards(cards)
    })
    
    // 剩余3张作为地主牌
    this.landlordCards = this.deck.deal(3)
    
    console.log('GameState: 发牌完成，开始叫牌')
    this.phase = 'bidding'
    this.currentPlayer = 0
  }

  // 叫牌 - 支持分数制叫牌 (bidValue: 0=不叫, 1=1分, 2=2分, 3=3分)
  bid(playerId, bidValue) {
    console.log(`GameState: 玩家${playerId} 叫牌: ${bidValue === 0 ? '不叫' : bidValue + '分'}`)

    // 验证叫牌有效性
    if (bidValue < 0 || bidValue > 3) {
      console.error(`GameState: 无效的叫牌值: ${bidValue}`)
      return false
    }

    if (bidValue > 0 && bidValue <= this.currentBid) {
      console.error(`GameState: 叫牌值${bidValue}不能小于等于当前最高叫分${this.currentBid}`)
      return false
    }

    // 记录叫牌历史
    this.biddingHistory.push({
      playerId,
      bidValue,
      bidType: bidValue === 0 ? 'pass' : 'bid',
      round: this.biddingRound,
      timestamp: Date.now()
    })

    if (bidValue > 0) {
      // 有人叫分
      this.currentBid = bidValue
      this.currentBidder = playerId

      // 如果叫3分，立即结束叫牌
      if (bidValue === 3) {
        this.landlordId = playerId
        this.setLandlord(playerId)
        this.phase = 'playing'
        this.currentPlayer = this.players.findIndex(p => p.id === playerId)
        console.log(`GameState: 玩家${playerId} 叫3分成为地主，游戏开始`)
        return true
      }
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3

    // 检查叫牌是否结束
    const currentRoundBids = this.biddingHistory.filter(bid => bid.round === this.biddingRound)

    // 所有人都叫过一轮
    if (currentRoundBids.length === 3) {
      if (this.currentBid === 0) {
        // 所有人都不叫，重新开始
        console.log('GameState: 所有人都不叫，重新开始')
        this.restartGame()
        return false
      } else {
        // 有人叫分，确定地主
        this.landlordId = this.currentBidder
        this.setLandlord(this.currentBidder)
        this.phase = 'playing'
        this.currentPlayer = this.players.findIndex(p => p.id === this.currentBidder)
        console.log(`GameState: 玩家${this.currentBidder} 以${this.currentBid}分成为地主，游戏开始`)
        return true
      }
    }

    return true
  }

  // 设置地主
  setLandlord(playerId) {
    console.log(`GameState: 设置地主 - 玩家${playerId}`)

    // 清除所有玩家的地主标识
    this.players.forEach(player => {
      player.isLandlord = false
    })

    // 设置新地主
    const landlord = this.players.find(p => p.id === playerId)
    if (landlord) {
      landlord.isLandlord = true
      console.log(`GameState: 玩家${playerId} 成为地主`)

      // 地主获得底牌
      if (this.landlordCards && this.landlordCards.length > 0) {
        console.log(`GameState: 地主获得底牌:`, this.landlordCards.map(c => c.getDisplayName()).join(' '))
        landlord.hand.addCards(this.landlordCards)
        console.log(`GameState: 地主现在有 ${landlord.hand.getCardCount()} 张牌`)
      } else {
        console.log('⚠️ 警告: 没有底牌可分配')
      }
    } else {
      console.log(`❌ 错误: 找不到玩家${playerId}`)
    }
  }

  // 开始出牌阶段
  startPlaying() {
    console.log('GameState: 开始出牌阶段')
    this.phase = 'playing'

    // 如果有地主，地主先出牌
    if (this.landlordId) {
      const landlordIndex = this.players.findIndex(p => p.id === this.landlordId)
      if (landlordIndex !== -1) {
        this.currentPlayer = landlordIndex
        console.log(`GameState: 地主(玩家${this.landlordId})先出牌`)
      }
    }

    // 清空出牌记录
    this.lastPlayedCards = []
    this.lastPlayedPattern = null
    this.lastPlayerId = null
    this.passCount = 0

    console.log(`GameState: 出牌阶段开始，当前玩家: ${this.currentPlayer}`)
  }

  // 出牌
  playCards(playerId, cards) {
    console.log(`GameState: 玩家${playerId} 出牌`, cards)

    const player = this.players.find(p => p.id === playerId)
    if (!player) return { success: false, message: '玩家不存在' }

    // 验证牌型
    const pattern = this.cardPattern.identifyPattern(cards)
    if (!pattern) {
      return { success: false, message: '无效的牌型' }
    }

    // 使用增强验证器验证是否可以出牌
    const playerCards = player ? player.hand.getCards() : null
    const enhancedValidation = this.enhancedValidator.validatePlay(
      cards,
      this.lastPlayedPattern,
      playerCards,
      { gamePhase: this.phase, currentPlayer: this.currentPlayer }
    )

    if (!enhancedValidation.valid) {
      return {
        success: false,
        message: enhancedValidation.message || '牌型不符合规则',
        errorType: enhancedValidation.errorType,
        details: enhancedValidation.details
      }
    }

    // 验证玩家是否拥有这些牌
    const hasAllCards = cards.every(card =>
      player.hand.hasCard(card.id)
    )
    if (!hasAllCards) {
      return { success: false, message: '玩家没有这些牌' }
    }

    // 移除玩家手中的牌
    player.hand.removeCards(cards)

    // 记录出牌历史
    this.playHistory.push({
      playerId,
      cards: cards.map(c => ({ rank: c.rank, suit: c.suit })),
      pattern: pattern,
      timestamp: Date.now()
    })

    // 统计炸弹和火箭
    if (pattern.type === 'bomb') {
      this.bombCount++
      console.log(`GameState: 炸弹计数 +1，当前总数: ${this.bombCount}`)
    } else if (pattern.type === 'rocket') {
      this.rocketCount++
      console.log(`GameState: 火箭计数 +1，当前总数: ${this.rocketCount}`)
    }

    // 更新游戏状态
    this.lastPlayedCards = cards
    this.lastPlayedPattern = pattern
    this.lastPlayerId = playerId
    this.passCount = 0

    console.log(`GameState: 玩家${playerId} 出牌成功，牌型: ${pattern.type}`)

    // 检查是否有玩家出完牌
    if (player.hand.getCardCount() === 0) {
      this.phase = 'finished'
      console.log(`GameState: 玩家${playerId} 获胜！`)

      // 计算游戏结果和得分
      this.calculateGameResult(playerId)

      return { success: true, gameOver: true, winner: playerId, result: this.gameResult }
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3
    return { success: true, pattern: pattern }
  }

  // 过牌
  pass(playerId) {
    console.log(`GameState: 玩家${playerId} 过牌`)

    this.passCount++

    // 如果连续两个玩家过牌，清空上次出牌记录
    if (this.passCount >= 2) {
      this.lastPlayedCards = []
      this.lastPlayedPattern = null
      this.lastPlayerId = null
      this.passCount = 0
      console.log('GameState: 清空上次出牌记录，新一轮开始')
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3
    return { success: true }
  }

  // 获取叫牌信息
  getBiddingInfo() {
    return {
      currentBid: this.currentBid,
      currentBidder: this.currentBidder,
      biddingHistory: this.biddingHistory,
      currentPlayer: this.currentPlayer,
      phase: this.phase
    }
  }

  // 重新开始游戏
  restartGame() {
    this.phase = 'waiting'
    this.players.forEach(player => {
      player.hand = new PlayerHand()
      player.isLandlord = false
    })
    this.deck = new Deck()
    this.landlordCards = []
    this.currentPlayer = 0
    this.biddingRound++
    this.biddingHistory = []
    this.landlordId = null
    this.currentBid = 0
    this.currentBidder = null
    this.lastPlayedCards = []
    this.lastPlayedPattern = null
    this.lastPlayerId = null
    this.passCount = 0

    // 重置计分数据
    this.resetScoring()
  }

  // 获取当前玩家
  getCurrentPlayer() {
    return this.players[this.currentPlayer]
  }

  // 获取玩家
  getPlayer(id) {
    return this.players.find(p => p.id === id)
  }

  // 获取游戏状态
  getGameInfo() {
    return {
      phase: this.phase,
      currentPlayer: this.currentPlayer,
      landlordId: this.landlordId,
      landlordCards: this.landlordCards, // 添加底牌信息
      currentBid: this.currentBid, // 添加当前叫分
      biddingHistory: this.biddingHistory,
      lastPlayedCards: this.lastPlayedCards,
      lastPlayedPattern: this.lastPlayedPattern,
      lastPlayerId: this.lastPlayerId,
      passCount: this.passCount,
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        cardCount: p.hand.getCardCount(),
        isLandlord: p.isLandlord,
        position: p.position
      }))
    }
  }

  // 获取牌型识别器
  getCardPattern() {
    return this.cardPattern
  }

  // 验证选中的牌是否可以出
  validateSelectedCards(cards) {
    const result = this.cardPattern.canPlay(cards, this.lastPlayedPattern)
    return result.valid
  }

  // 获取出牌验证详细信息（使用增强验证器）
  getPlayValidationInfo(cards) {
    const playerId = this.players[this.currentPlayer].id
    const player = this.getPlayer(playerId)
    const playerCards = player ? player.hand.getCards() : null

    // 使用增强验证器进行详细验证
    const enhancedResult = this.enhancedValidator.validatePlay(
      cards,
      this.lastPlayedPattern,
      playerCards,
      { gamePhase: this.phase, currentPlayer: this.currentPlayer }
    )

    // 保持向后兼容性，同时提供增强信息
    return {
      valid: enhancedResult.valid,
      reason: enhancedResult.message,
      pattern: enhancedResult.pattern,
      errorType: enhancedResult.errorType,
      details: enhancedResult.details,
      isFirstPlay: enhancedResult.isFirstPlay,
      isSpecialPlay: enhancedResult.isSpecialPlay,
      // 原始验证结果作为备用
      originalResult: this.cardPattern.canPlay(cards, this.lastPlayedPattern)
    }
  }

  // 获取可出牌的提示
  getPlayableHints(playerId) {
    const player = this.players.find(p => p.id === playerId)
    if (!player) return ['玩家不存在']

    const hints = []
    const playerCards = player.hand.getCards()

    // 如果没有上次出牌，可以出任意牌型
    if (!this.lastPlayedPattern || !this.lastPlayedCards || this.lastPlayedCards.length === 0) {
      hints.push('首次出牌，可出任意牌型')

      // 推荐一些基本牌型
      const singles = playerCards.slice(0, 3)
      if (singles.length > 0) {
        hints.push(`推荐单牌: ${singles.map(c => c.getDisplayName()).join(', ')}`)
      }

      return hints
    }

    // 根据上次出牌类型给出提示
    const lastPattern = this.lastPlayedPattern

    if (lastPattern.type === 'single') {
      const biggerSingles = playerCards.filter(card =>
        this.getCardValue(card) > this.getCardValue(this.lastPlayedCards[0])
      )
      if (biggerSingles.length > 0) {
        hints.push(`可出单牌: ${biggerSingles.slice(0, 3).map(c => c.getDisplayName()).join(', ')}`)
      }
    }

    // 检查炸弹
    const bombs = this.findBombs(playerCards)
    if (bombs.length > 0) {
      hints.push(`炸弹: ${bombs[0][0].rank}`)
    }

    // 检查火箭
    if (this.hasRocket(playerCards)) {
      hints.push('火箭: 大王+小王')
    }

    if (hints.length === 0) {
      hints.push('没有可出的牌，建议过牌')
    }

    return hints
  }

  // 辅助方法：查找炸弹
  findBombs(cards) {
    const rankCounts = {}
    cards.forEach(card => {
      rankCounts[card.rank] = (rankCounts[card.rank] || 0) + 1
    })

    const bombs = []
    Object.entries(rankCounts).forEach(([rank, count]) => {
      if (count >= 4) {
        const bombCards = cards.filter(card => card.rank === rank)
        bombs.push(bombCards)
      }
    })

    return bombs
  }

  // 辅助方法：检查是否有火箭
  hasRocket(cards) {
    const hasBigJoker = cards.some(card => card.rank === 'big_joker')
    const hasSmallJoker = cards.some(card => card.rank === 'small_joker')
    return hasBigJoker && hasSmallJoker
  }

  // 辅助方法：获取卡牌数值
  getCardValue(card) {
    const values = {
      '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
      'small_joker': 16, 'big_joker': 17
    }
    return values[card.rank] || 0
  }

  // 计算游戏结果和得分
  calculateGameResult(winnerId) {
    console.log(`GameState: 计算游戏结果，获胜者: 玩家${winnerId}`)

    const winner = this.players.find(p => p.id === winnerId)
    const isLandlordWin = winner && winner.isLandlord

    // 基础分数 = 叫分
    let baseScore = this.currentBid || 1

    // 炸弹倍数计算（三炸封顶）
    const bombMultiplier = Math.min(this.bombCount + this.rocketCount, 3)
    const totalMultiplier = Math.pow(2, bombMultiplier)

    // 最终分数
    const finalScore = baseScore * totalMultiplier

    console.log(`GameState: 基础分数: ${baseScore}, 炸弹倍数: ${bombMultiplier}, 最终倍数: ${totalMultiplier}, 最终分数: ${finalScore}`)

    // 计算各玩家得分
    this.scores = {}
    this.players.forEach(player => {
      if (player.isLandlord) {
        // 地主得分
        this.scores[player.id] = isLandlordWin ? finalScore * 2 : -finalScore * 2
      } else {
        // 农民得分
        this.scores[player.id] = isLandlordWin ? -finalScore : finalScore
      }
    })

    // 检查特殊情况
    const specialConditions = this.checkSpecialConditions()

    this.gameResult = {
      winnerId,
      isLandlordWin,
      baseScore,
      bombCount: this.bombCount,
      rocketCount: this.rocketCount,
      totalMultiplier,
      finalScore,
      scores: this.scores,
      specialConditions,
      playHistory: this.playHistory
    }

    console.log('GameState: 游戏结果:', this.gameResult)
    return this.gameResult
  }

  // 检查特殊情况
  checkSpecialConditions() {
    const conditions = []

    // 检查春天（地主一轮出完）
    if (this.gameResult && this.gameResult.isLandlordWin) {
      const farmerPlayed = this.playHistory.some(play => {
        const player = this.players.find(p => p.id === play.playerId)
        return player && !player.isLandlord
      })

      if (!farmerPlayed) {
        conditions.push('春天')
        console.log('GameState: 检测到春天！')
      }
    }

    // 检查反春（农民一轮获胜）
    if (this.gameResult && !this.gameResult.isLandlordWin) {
      const landlordPlayCount = this.playHistory.filter(play => {
        const player = this.players.find(p => p.id === play.playerId)
        return player && player.isLandlord
      }).length

      if (landlordPlayCount <= 1) {
        conditions.push('反春')
        console.log('GameState: 检测到反春！')
      }
    }

    // 检查炸弹封顶
    if (this.bombCount + this.rocketCount >= 3) {
      conditions.push('三炸封顶')
      console.log('GameState: 检测到三炸封顶！')
    }

    return conditions
  }

  // 获取游戏结果
  getGameResult() {
    return this.gameResult
  }

  // 重置计分数据
  resetScoring() {
    this.bombCount = 0
    this.rocketCount = 0
    this.gameResult = null
    this.scores = {}
    this.playHistory = []
  }
}
