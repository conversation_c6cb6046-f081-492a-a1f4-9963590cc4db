// 游戏音频管理器
export class AudioManager {
  constructor(scene) {
    this.scene = scene
    this.enabled = true
    this.musicVolume = 0.6
    this.effectVolume = 0.8
    
    // 音频资源配置
    this.audioAssets = {
      // 背景音乐
      bgm: {
        main: 'assets/audio/bgm/main_theme.mp3',
        game: 'assets/audio/bgm/game_music.mp3',
        victory: 'assets/audio/bgm/victory.mp3'
      },
      
      // 音效
      effects: {
        // 卡牌相关
        cardDeal: 'assets/audio/effects/card_deal.mp3',
        cardPlay: 'assets/audio/effects/card_play.mp3',
        cardShuffle: 'assets/audio/effects/card_shuffle.mp3',
        
        // 游戏动作
        buttonClick: 'assets/audio/effects/button_click.mp3',
        bidding: 'assets/audio/effects/bidding.mp3',
        pass: 'assets/audio/effects/pass.mp3',
        
        // 特殊牌型
        bomb: 'assets/audio/effects/bomb.mp3',
        rocket: 'assets/audio/effects/rocket.mp3',
        straight: 'assets/audio/effects/straight.mp3',
        
        // 游戏结果
        win: 'assets/audio/effects/win.mp3',
        lose: 'assets/audio/effects/lose.mp3',
        
        // 提示音
        warning: 'assets/audio/effects/warning.mp3',
        notification: 'assets/audio/effects/notification.mp3'
      }
    }
    
    // 当前播放的背景音乐
    this.currentBGM = null
    
    // 音频实例缓存
    this.audioCache = new Map()
    
    console.log('🎵 音频管理器初始化完成')
  }

  // 预加载音频资源
  preloadAudio() {
    console.log('🎵 开始预加载音频资源')
    
    // 加载背景音乐
    Object.entries(this.audioAssets.bgm).forEach(([key, path]) => {
      this.scene.load.audio(`bgm_${key}`, path)
    })
    
    // 加载音效
    Object.entries(this.audioAssets.effects).forEach(([key, path]) => {
      this.scene.load.audio(`effect_${key}`, path)
    })
    
    console.log('✅ 音频资源预加载配置完成')
  }

  // 初始化音频系统
  initialize() {
    console.log('🎵 初始化音频系统')
    
    // 设置音频上下文
    this.setupAudioContext()
    
    // 创建音频实例
    this.createAudioInstances()
    
    // 设置音量
    this.updateVolume()
    
    console.log('✅ 音频系统初始化完成')
  }

  // 设置音频上下文
  setupAudioContext() {
    // 处理浏览器音频策略
    if (this.scene.sound.context && this.scene.sound.context.state === 'suspended') {
      // 等待用户交互后恢复音频上下文
      const resumeAudio = () => {
        this.scene.sound.context.resume().then(() => {
          console.log('🎵 音频上下文已恢复')
          document.removeEventListener('click', resumeAudio)
          document.removeEventListener('touchstart', resumeAudio)
        })
      }
      
      document.addEventListener('click', resumeAudio)
      document.addEventListener('touchstart', resumeAudio)
    }
  }

  // 创建音频实例
  createAudioInstances() {
    // 创建背景音乐实例
    Object.keys(this.audioAssets.bgm).forEach(key => {
      const audioKey = `bgm_${key}`
      if (this.scene.cache.audio.exists(audioKey)) {
        const audio = this.scene.sound.add(audioKey, {
          loop: true,
          volume: this.musicVolume
        })
        this.audioCache.set(audioKey, audio)
      }
    })
    
    // 创建音效实例
    Object.keys(this.audioAssets.effects).forEach(key => {
      const audioKey = `effect_${key}`
      if (this.scene.cache.audio.exists(audioKey)) {
        const audio = this.scene.sound.add(audioKey, {
          loop: false,
          volume: this.effectVolume
        })
        this.audioCache.set(audioKey, audio)
      }
    })
  }

  // 播放背景音乐
  playBGM(musicKey, fadeIn = true) {
    if (!this.enabled) return
    
    console.log(`🎵 播放背景音乐: ${musicKey}`)
    
    const audioKey = `bgm_${musicKey}`
    const newBGM = this.audioCache.get(audioKey)
    
    if (!newBGM) {
      console.warn(`背景音乐不存在: ${musicKey}`)
      return
    }
    
    // 停止当前背景音乐
    if (this.currentBGM && this.currentBGM.isPlaying) {
      if (fadeIn) {
        this.fadeOut(this.currentBGM, 1000, () => {
          this.currentBGM.stop()
          this.startNewBGM(newBGM, fadeIn)
        })
      } else {
        this.currentBGM.stop()
        this.startNewBGM(newBGM, fadeIn)
      }
    } else {
      this.startNewBGM(newBGM, fadeIn)
    }
  }

  // 开始播放新的背景音乐
  startNewBGM(bgm, fadeIn) {
    this.currentBGM = bgm
    
    if (fadeIn) {
      bgm.setVolume(0)
      bgm.play()
      this.fadeIn(bgm, 1000, this.musicVolume)
    } else {
      bgm.setVolume(this.musicVolume)
      bgm.play()
    }
  }

  // 停止背景音乐
  stopBGM(fadeOut = true) {
    if (!this.currentBGM || !this.currentBGM.isPlaying) return
    
    console.log('🎵 停止背景音乐')
    
    if (fadeOut) {
      this.fadeOut(this.currentBGM, 1000, () => {
        this.currentBGM.stop()
        this.currentBGM = null
      })
    } else {
      this.currentBGM.stop()
      this.currentBGM = null
    }
  }

  // 播放音效
  playEffect(effectKey, options = {}) {
    if (!this.enabled) return
    
    const {
      volume = this.effectVolume,
      delay = 0,
      rate = 1,
      detune = 0
    } = options
    
    console.log(`🔊 播放音效: ${effectKey}`)
    
    const audioKey = `effect_${effectKey}`
    const effect = this.audioCache.get(audioKey)
    
    if (!effect) {
      console.warn(`音效不存在: ${effectKey}`)
      return
    }
    
    // 创建新的音效实例（避免重叠播放问题）
    const effectInstance = this.scene.sound.add(audioKey, {
      volume: volume,
      rate: rate,
      detune: detune
    })
    
    // 播放完成后销毁实例
    effectInstance.once('complete', () => {
      effectInstance.destroy()
    })
    
    if (delay > 0) {
      this.scene.time.delayedCall(delay, () => {
        effectInstance.play()
      })
    } else {
      effectInstance.play()
    }
    
    return effectInstance
  }

  // 淡入效果
  fadeIn(audio, duration, targetVolume) {
    if (!audio) return
    
    this.scene.tweens.add({
      targets: audio,
      volume: targetVolume,
      duration: duration,
      ease: 'Linear'
    })
  }

  // 淡出效果
  fadeOut(audio, duration, onComplete) {
    if (!audio) return
    
    this.scene.tweens.add({
      targets: audio,
      volume: 0,
      duration: duration,
      ease: 'Linear',
      onComplete: onComplete
    })
  }

  // 设置音乐音量
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume))
    
    if (this.currentBGM) {
      this.currentBGM.setVolume(this.musicVolume)
    }
    
    console.log(`🎵 音乐音量设置为: ${this.musicVolume}`)
  }

  // 设置音效音量
  setEffectVolume(volume) {
    this.effectVolume = Math.max(0, Math.min(1, volume))
    console.log(`🔊 音效音量设置为: ${this.effectVolume}`)
  }

  // 更新音量
  updateVolume() {
    // 更新背景音乐音量
    this.audioCache.forEach((audio, key) => {
      if (key.startsWith('bgm_')) {
        audio.setVolume(this.musicVolume)
      } else if (key.startsWith('effect_')) {
        audio.setVolume(this.effectVolume)
      }
    })
  }

  // 启用/禁用音频
  setEnabled(enabled) {
    this.enabled = enabled
    
    if (!enabled) {
      // 停止所有音频
      this.stopBGM(false)
      this.scene.sound.stopAll()
    }
    
    console.log(`🎵 音频${enabled ? '启用' : '禁用'}`)
  }

  // 暂停所有音频
  pauseAll() {
    if (this.currentBGM && this.currentBGM.isPlaying) {
      this.currentBGM.pause()
    }
    
    this.scene.sound.pauseAll()
    console.log('⏸️ 所有音频已暂停')
  }

  // 恢复所有音频
  resumeAll() {
    if (this.currentBGM && this.currentBGM.isPaused) {
      this.currentBGM.resume()
    }
    
    this.scene.sound.resumeAll()
    console.log('▶️ 所有音频已恢复')
  }

  // 获取音频状态
  getStatus() {
    return {
      enabled: this.enabled,
      musicVolume: this.musicVolume,
      effectVolume: this.effectVolume,
      currentBGM: this.currentBGM ? this.currentBGM.key : null,
      isPlaying: this.currentBGM ? this.currentBGM.isPlaying : false
    }
  }

  // 销毁音频管理器
  destroy() {
    console.log('🗑️ 销毁音频管理器')
    
    // 停止所有音频
    this.stopBGM(false)
    this.scene.sound.stopAll()
    
    // 清理缓存
    this.audioCache.clear()
    this.currentBGM = null
  }
}
