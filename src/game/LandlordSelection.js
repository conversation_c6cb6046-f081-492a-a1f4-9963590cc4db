// 叫牌系统核心类
export class LandlordSelection {
  constructor() {
    // 叫牌选项
    this.bidOptions = {
      NO_BID: 'no_bid',      // 不叫
      ONE_POINT: 'one_point', // 1分
      TWO_POINT: 'two_point', // 2分
      THREE_POINT: 'three_point' // 3分
    }

    // 座位定义（按逆时针顺序）
    this.seats = {
      WEST: 'west',   // 西家（左）
      SOUTH: 'south', // 南家（下）
      EAST: 'east'    // 东家（右）
    }

    // 叫牌状态
    this.bidState = {
      WAITING: 'waiting',     // 等待叫牌
      IN_PROGRESS: 'in_progress', // 叫牌进行中
      COMPLETED: 'completed'  // 叫牌结束
    }

    this.reset()
  }

  // 重置叫牌状态
  reset() {
    this.currentState = this.bidState.WAITING
    this.currentBidder = null
    this.bidHistory = []
    this.landlord = null
    this.highestBid = null
    this.gameRound = 1
    this.gameNumber = 1
    this.players = {}
    this.firstBidder = null
  }

  // 初始化游戏
  initGame(players, round = 1, gameNumber = 1) {
    this.reset()
    this.gameRound = round
    this.gameNumber = gameNumber
    this.players = { ...players }
    
    // 确定首叫者
    this.firstBidder = this.determineFirstBidder()
    this.currentBidder = this.firstBidder
    this.currentState = this.bidState.IN_PROGRESS
    
    console.log(`🎮 第${round}轮第${gameNumber}局开始，首叫者: ${this.firstBidder}`)
    return this.firstBidder
  }

  // 确定首叫者
  determineFirstBidder() {
    if (this.gameNumber === 1) {
      // 每轮第1局，西家首叫
      return this.seats.WEST
    } else {
      // 从第2局开始，按逆时针轮流首叫
      const seatOrder = [this.seats.WEST, this.seats.SOUTH, this.seats.EAST]
      const firstBidderIndex = (this.gameNumber - 1) % 3
      return seatOrder[firstBidderIndex]
    }
  }

  // 获取下一个叫牌者
  getNextBidder(currentSeat) {
    const seatOrder = [this.seats.WEST, this.seats.SOUTH, this.seats.EAST]
    const currentIndex = seatOrder.indexOf(currentSeat)
    const nextIndex = (currentIndex + 1) % 3
    return seatOrder[nextIndex]
  }

  // 检查玩家是否有特殊牌型（双王或4个2）
  hasSpecialCards(cards) {
    if (!cards || cards.length === 0) return { hasDoubleJoker: false, hasFour2s: false }

    // 检查双王
    const jokers = cards.filter(card => card.suit === 'joker')
    const hasDoubleJoker = jokers.length === 2

    // 检查4个2
    const twos = cards.filter(card => card.rank === '2')
    const hasFour2s = twos.length === 4

    return { hasDoubleJoker, hasFour2s }
  }

  // 验证叫牌是否合法
  validateBid(playerSeat, bidOption, playerCards = null) {
    // 检查游戏状态
    if (this.currentState !== this.bidState.IN_PROGRESS) {
      return { valid: false, reason: '当前不在叫牌阶段' }
    }

    // 检查是否轮到该玩家
    if (playerSeat !== this.currentBidder) {
      return { valid: false, reason: '还没轮到您叫牌' }
    }

    // 检查叫牌选项是否有效
    if (!Object.values(this.bidOptions).includes(bidOption)) {
      return { valid: false, reason: '无效的叫牌选项' }
    }

    // 检查叫牌规则：后叫者只能叫更高分或不叫
    if (bidOption !== this.bidOptions.NO_BID) {
      const bidValue = this.getBidValue(bidOption)
      const currentHighestValue = this.highestBid ? this.getBidValue(this.highestBid.option) : 0
      
      if (bidValue <= currentHighestValue) {
        return { valid: false, reason: '叫牌分数必须高于当前最高分' }
      }
    }

    // 检查特殊牌型提示
    if (playerCards) {
      const specialCards = this.hasSpecialCards(playerCards)
      if ((specialCards.hasDoubleJoker || specialCards.hasFour2s) && bidOption !== this.bidOptions.NO_BID) {
        return { 
          valid: true, 
          warning: '您持有特殊牌型（双王或4个2），建议考虑不叫分' 
        }
      }
    }

    return { valid: true }
  }

  // 执行叫牌
  makeBid(playerSeat, bidOption, playerCards = null) {
    const validation = this.validateBid(playerSeat, bidOption, playerCards)
    if (!validation.valid) {
      return { success: false, reason: validation.reason }
    }

    // 记录叫牌历史
    const bidRecord = {
      player: playerSeat,
      option: bidOption,
      value: this.getBidValue(bidOption),
      timestamp: Date.now()
    }
    this.bidHistory.push(bidRecord)

    // 更新最高叫牌
    if (bidOption !== this.bidOptions.NO_BID) {
      this.highestBid = bidRecord
    }

    console.log(`🎯 ${playerSeat} 叫牌: ${this.getBidDisplayName(bidOption)}`)

    // 检查是否叫3分（立即结束）
    if (bidOption === this.bidOptions.THREE_POINT) {
      this.landlord = playerSeat
      this.currentState = this.bidState.COMPLETED
      console.log(`🎉 ${playerSeat} 叫3分，立即成为地主！`)
      return { 
        success: true, 
        completed: true, 
        landlord: this.landlord,
        warning: validation.warning 
      }
    }

    // 移动到下一个叫牌者
    this.currentBidder = this.getNextBidder(this.currentBidder)

    // 检查是否完成一轮叫牌
    if (this.currentBidder === this.firstBidder) {
      return this.completeBidding()
    }

    return { 
      success: true, 
      completed: false, 
      nextBidder: this.currentBidder,
      warning: validation.warning 
    }
  }

  // 完成叫牌
  completeBidding() {
    // 检查是否所有人都不叫
    const allNoBid = this.bidHistory.every(bid => bid.option === this.bidOptions.NO_BID)
    
    if (allNoBid) {
      console.log('🔄 所有玩家都不叫，重新发牌')
      return { 
        success: true, 
        completed: true, 
        redeal: true, 
        reason: '所有玩家都选择不叫，需要重新发牌' 
      }
    }

    // 确定地主（最高叫牌者）
    if (this.highestBid) {
      this.landlord = this.highestBid.player
      this.currentState = this.bidState.COMPLETED
      console.log(`🎉 叫牌结束，${this.landlord} 成为地主（叫${this.getBidDisplayName(this.highestBid.option)}）`)
      return { 
        success: true, 
        completed: true, 
        landlord: this.landlord 
      }
    }

    return { success: false, reason: '叫牌状态异常' }
  }

  // 获取叫牌数值
  getBidValue(bidOption) {
    const values = {
      [this.bidOptions.NO_BID]: 0,
      [this.bidOptions.ONE_POINT]: 1,
      [this.bidOptions.TWO_POINT]: 2,
      [this.bidOptions.THREE_POINT]: 3
    }
    return values[bidOption] || 0
  }

  // 获取叫牌显示名称
  getBidDisplayName(bidOption) {
    const names = {
      [this.bidOptions.NO_BID]: '不叫',
      [this.bidOptions.ONE_POINT]: '1分',
      [this.bidOptions.TWO_POINT]: '2分',
      [this.bidOptions.THREE_POINT]: '3分'
    }
    return names[bidOption] || '未知'
  }

  // 处理超时
  handleTimeout(playerSeat) {
    if (playerSeat === this.currentBidder && this.currentState === this.bidState.IN_PROGRESS) {
      console.log(`⏰ ${playerSeat} 叫牌超时，自动选择不叫`)
      return this.makeBid(playerSeat, this.bidOptions.NO_BID)
    }
    return { success: false, reason: '超时处理失败' }
  }

  // 获取当前状态信息
  getStatus() {
    return {
      state: this.currentState,
      currentBidder: this.currentBidder,
      firstBidder: this.firstBidder,
      bidHistory: [...this.bidHistory],
      highestBid: this.highestBid,
      landlord: this.landlord,
      gameRound: this.gameRound,
      gameNumber: this.gameNumber
    }
  }

  // 获取可用的叫牌选项
  getAvailableBids() {
    const currentHighestValue = this.highestBid ? this.getBidValue(this.highestBid.option) : 0
    const available = [this.bidOptions.NO_BID] // 总是可以不叫

    // 添加比当前最高分更高的选项
    if (currentHighestValue < 1) available.push(this.bidOptions.ONE_POINT)
    if (currentHighestValue < 2) available.push(this.bidOptions.TWO_POINT)
    if (currentHighestValue < 3) available.push(this.bidOptions.THREE_POINT)

    return available
  }
}
