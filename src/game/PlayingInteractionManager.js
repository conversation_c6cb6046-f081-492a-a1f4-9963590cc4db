// 出牌界面交互管理器
export class PlayingInteractionManager {
  constructor(gameScene) {
    this.gameScene = gameScene
    this.selectedCards = []
    this.suggestedPlays = []
    this.autoSortEnabled = true
    this.quickSelectEnabled = true
    
    // 交互状态
    this.isSelecting = false
    this.lastClickTime = 0
    this.clickThreshold = 200 // 防抖时间
    
    // UI元素
    this.uiElements = {
      quickSelectPanel: null,
      suggestionPanel: null,
      sortButton: null,
      clearButton: null
    }
    
    // 事件监听器
    this.eventListeners = new Map()
    
    console.log('🎮 出牌交互管理器初始化完成')
  }

  // 初始化交互界面
  initialize() {
    this.createQuickSelectPanel()
    this.createSuggestionPanel()
    this.createUtilityButtons()
    this.setupEventListeners()
    
    console.log('✅ 出牌交互界面初始化完成')
  }

  // 创建快速选择面板
  createQuickSelectPanel() {
    const scene = this.gameScene
    const panelX = 100
    const panelY = 300

    // 创建面板容器
    const panel = scene.add.container(panelX, panelY)
    panel.setDepth(12000)

    // 面板背景
    const bg = scene.add.graphics()
    bg.fillStyle(0x2C3E50, 0.9)
    bg.lineStyle(2, 0x3498DB, 1)
    bg.fillRoundedRect(-50, -120, 100, 240, 10)
    bg.strokeRoundedRect(-50, -120, 100, 240, 10)
    panel.add(bg)

    // 标题
    const title = scene.add.text(0, -100, '快选', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    panel.add(title)

    // 快速选择按钮
    const quickButtons = [
      { text: '单牌', action: () => this.quickSelectSingle() },
      { text: '对子', action: () => this.quickSelectPair() },
      { text: '三张', action: () => this.quickSelectTriple() },
      { text: '顺子', action: () => this.quickSelectStraight() },
      { text: '炸弹', action: () => this.quickSelectBomb() },
      { text: '清空', action: () => this.clearSelection() }
    ]

    quickButtons.forEach((btn, index) => {
      const buttonY = -70 + index * 30
      const button = this.createQuickButton(scene, 0, buttonY, btn.text, btn.action)
      panel.add(button)
    })

    this.uiElements.quickSelectPanel = panel
    panel.setVisible(false) // 默认隐藏
  }

  // 创建建议面板
  createSuggestionPanel() {
    const scene = this.gameScene
    const panelX = 1100
    const panelY = 300

    // 创建面板容器
    const panel = scene.add.container(panelX, panelY)
    panel.setDepth(12000)

    // 面板背景
    const bg = scene.add.graphics()
    bg.fillStyle(0x27AE60, 0.9)
    bg.lineStyle(2, 0x2ECC71, 1)
    bg.fillRoundedRect(-60, -150, 120, 300, 10)
    bg.strokeRoundedRect(-60, -150, 120, 300, 10)
    panel.add(bg)

    // 标题
    const title = scene.add.text(0, -130, '建议', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    panel.add(title)

    this.uiElements.suggestionPanel = panel
    panel.setVisible(false) // 默认隐藏
  }

  // 创建工具按钮
  createUtilityButtons() {
    const scene = this.gameScene
    const buttonY = 700

    // 自动排序按钮
    const sortButton = scene.add.container(150, buttonY)
    const sortBg = scene.add.graphics()
    sortBg.fillStyle(0x9B59B6, 0.8)
    sortBg.lineStyle(2, 0x8E44AD, 1)
    sortBg.fillRoundedRect(-30, -15, 60, 30, 8)
    sortBg.strokeRoundedRect(-30, -15, 60, 30, 8)
    sortButton.add(sortBg)

    const sortText = scene.add.text(0, 0, '排序', {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    sortButton.add(sortText)

    sortButton.setInteractive({ useHandCursor: true })
    sortButton.on('pointerdown', () => this.autoSortCards())
    this.uiElements.sortButton = sortButton

    // 清空选择按钮
    const clearButton = scene.add.container(250, buttonY)
    const clearBg = scene.add.graphics()
    clearBg.fillStyle(0xE74C3C, 0.8)
    clearBg.lineStyle(2, 0xC0392B, 1)
    clearBg.fillRoundedRect(-30, -15, 60, 30, 8)
    clearBg.strokeRoundedRect(-30, -15, 60, 30, 8)
    clearButton.add(clearBg)

    const clearText = scene.add.text(0, 0, '清空', {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    clearButton.add(clearText)

    clearButton.setInteractive({ useHandCursor: true })
    clearButton.on('pointerdown', () => this.clearSelection())
    this.uiElements.clearButton = clearButton

    // 默认隐藏
    sortButton.setVisible(false)
    clearButton.setVisible(false)
  }

  // 创建快速按钮
  createQuickButton(scene, x, y, text, action) {
    const button = scene.add.container(x, y)
    
    const bg = scene.add.graphics()
    bg.fillStyle(0x34495E, 0.8)
    bg.lineStyle(1, 0x5DADE2, 1)
    bg.fillRoundedRect(-25, -10, 50, 20, 5)
    bg.strokeRoundedRect(-25, -10, 50, 20, 5)
    button.add(bg)

    const buttonText = scene.add.text(0, 0, text, {
      fontSize: '10px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    button.add(buttonText)

    button.setInteractive({ useHandCursor: true })
    button.on('pointerdown', action)
    
    // 悬停效果
    button.on('pointerover', () => {
      bg.clear()
      bg.fillStyle(0x5DADE2, 0.9)
      bg.lineStyle(1, 0x85C1E9, 1)
      bg.fillRoundedRect(-25, -10, 50, 20, 5)
      bg.strokeRoundedRect(-25, -10, 50, 20, 5)
    })
    
    button.on('pointerout', () => {
      bg.clear()
      bg.fillStyle(0x34495E, 0.8)
      bg.lineStyle(1, 0x5DADE2, 1)
      bg.fillRoundedRect(-25, -10, 50, 20, 5)
      bg.strokeRoundedRect(-25, -10, 50, 20, 5)
    })

    return button
  }

  // 设置事件监听
  setupEventListeners() {
    // 监听游戏状态变化
    this.addEventListener('gamePhaseChanged', (phase) => {
      if (phase === 'playing') {
        this.showInteractionUI()
      } else {
        this.hideInteractionUI()
      }
    })

    // 监听卡牌选择变化
    this.addEventListener('cardsSelected', (cards) => {
      this.selectedCards = cards
      this.updateSuggestions()
    })
  }

  // 显示交互界面
  showInteractionUI() {
    console.log('🎮 显示出牌交互界面')
    
    if (this.uiElements.quickSelectPanel) {
      this.uiElements.quickSelectPanel.setVisible(true)
      this.animateIn(this.uiElements.quickSelectPanel, 'left')
    }
    
    if (this.uiElements.suggestionPanel) {
      this.uiElements.suggestionPanel.setVisible(true)
      this.animateIn(this.uiElements.suggestionPanel, 'right')
    }
    
    if (this.uiElements.sortButton) {
      this.uiElements.sortButton.setVisible(true)
    }
    
    if (this.uiElements.clearButton) {
      this.uiElements.clearButton.setVisible(true)
    }

    // 生成出牌建议
    this.generatePlaySuggestions()
  }

  // 隐藏交互界面
  hideInteractionUI() {
    console.log('🎮 隐藏出牌交互界面')
    
    Object.values(this.uiElements).forEach(element => {
      if (element) {
        element.setVisible(false)
      }
    })
  }

  // 动画进入效果
  animateIn(element, direction) {
    const scene = this.gameScene
    const originalX = element.x
    
    if (direction === 'left') {
      element.x = originalX - 100
    } else if (direction === 'right') {
      element.x = originalX + 100
    }
    
    element.setAlpha(0)
    
    scene.tweens.add({
      targets: element,
      x: originalX,
      alpha: 1,
      duration: 300,
      ease: 'Back.easeOut'
    })
  }

  // 快速选择单牌
  quickSelectSingle() {
    console.log('🎯 快速选择单牌')
    this.clearSelection()
    
    const playerCards = this.getPlayerCards()
    if (playerCards.length > 0) {
      // 选择最小的单牌
      const sortedCards = [...playerCards].sort((a, b) => a.value - b.value)
      this.selectCards([sortedCards[0]])
    }
  }

  // 快速选择对子
  quickSelectPair() {
    console.log('🎯 快速选择对子')
    this.clearSelection()
    
    const pairs = this.findPairs()
    if (pairs.length > 0) {
      this.selectCards(pairs[0])
    }
  }

  // 快速选择三张
  quickSelectTriple() {
    console.log('🎯 快速选择三张')
    this.clearSelection()
    
    const triples = this.findTriples()
    if (triples.length > 0) {
      this.selectCards(triples[0])
    }
  }

  // 快速选择顺子
  quickSelectStraight() {
    console.log('🎯 快速选择顺子')
    this.clearSelection()
    
    const straights = this.findStraights()
    if (straights.length > 0) {
      this.selectCards(straights[0])
    }
  }

  // 快速选择炸弹
  quickSelectBomb() {
    console.log('🎯 快速选择炸弹')
    this.clearSelection()
    
    const bombs = this.findBombs()
    if (bombs.length > 0) {
      this.selectCards(bombs[0])
    }
  }

  // 清空选择
  clearSelection() {
    console.log('🧹 清空卡牌选择')
    this.gameScene.selectedCards = []
    this.selectedCards = []
    
    // 重置所有卡牌的选中状态
    if (this.gameScene.playerHandAreas && this.gameScene.playerHandAreas.bottom) {
      this.gameScene.playerHandAreas.bottom.cards.forEach(cardSprite => {
        if (cardSprite.selected) {
          this.gameScene.selectCard(cardSprite) // 取消选中
        }
      })
    }
    
    this.updateSuggestions()
  }

  // 自动排序卡牌
  autoSortCards() {
    console.log('🔄 自动排序卡牌')
    
    // 触发GameScene的排序功能
    if (this.gameScene.sortPlayerCards) {
      this.gameScene.sortPlayerCards()
    }
    
    // 添加排序动画反馈
    this.showSortFeedback()
  }

  // 显示排序反馈
  showSortFeedback() {
    const scene = this.gameScene
    const feedback = scene.add.text(600, 400, '卡牌已排序', {
      fontSize: '18px',
      color: '#2ECC71',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    
    feedback.setAlpha(0)
    scene.tweens.add({
      targets: feedback,
      alpha: 1,
      y: 380,
      duration: 300,
      ease: 'Back.easeOut',
      onComplete: () => {
        scene.tweens.add({
          targets: feedback,
          alpha: 0,
          y: 360,
          duration: 500,
          delay: 1000,
          onComplete: () => feedback.destroy()
        })
      }
    })
  }

  // 获取玩家手牌
  getPlayerCards() {
    const gameInfo = this.gameScene.gameState.getGameInfo()
    return gameInfo.players[0].hand.getCards() // 玩家1的手牌
  }

  // 查找对子
  findPairs() {
    const cards = this.getPlayerCards()
    const groups = this.groupCardsByRank(cards)
    const pairs = []
    
    Object.values(groups).forEach(group => {
      if (group.length >= 2) {
        pairs.push(group.slice(0, 2))
      }
    })
    
    return pairs.sort((a, b) => a[0].value - b[0].value)
  }

  // 查找三张
  findTriples() {
    const cards = this.getPlayerCards()
    const groups = this.groupCardsByRank(cards)
    const triples = []
    
    Object.values(groups).forEach(group => {
      if (group.length >= 3) {
        triples.push(group.slice(0, 3))
      }
    })
    
    return triples.sort((a, b) => a[0].value - b[0].value)
  }

  // 查找顺子
  findStraights() {
    const cards = this.getPlayerCards()
    const straights = []
    
    // 简化的顺子查找逻辑
    const sortedCards = [...cards].sort((a, b) => a.value - b.value)
    
    for (let i = 0; i <= sortedCards.length - 5; i++) {
      const straight = []
      for (let j = i; j < sortedCards.length && straight.length < 12; j++) {
        if (straight.length === 0 || sortedCards[j].value === straight[straight.length - 1].value + 1) {
          straight.push(sortedCards[j])
        } else {
          break
        }
      }
      
      if (straight.length >= 5) {
        straights.push(straight)
      }
    }
    
    return straights
  }

  // 查找炸弹
  findBombs() {
    const cards = this.getPlayerCards()
    const groups = this.groupCardsByRank(cards)
    const bombs = []
    
    Object.values(groups).forEach(group => {
      if (group.length === 4) {
        bombs.push(group)
      }
    })
    
    return bombs.sort((a, b) => a[0].value - b[0].value)
  }

  // 按点数分组卡牌
  groupCardsByRank(cards) {
    const groups = {}
    cards.forEach(card => {
      const rank = card.rank
      if (!groups[rank]) {
        groups[rank] = []
      }
      groups[rank].push(card)
    })
    return groups
  }

  // 选择指定卡牌
  selectCards(cards) {
    if (!cards || cards.length === 0) return
    
    console.log('🎯 选择卡牌:', cards.map(c => c.getDisplayName()).join(' '))
    
    // 通过GameScene的selectCard方法选择卡牌
    cards.forEach(card => {
      const cardSprite = this.findCardSprite(card)
      if (cardSprite && !cardSprite.selected) {
        this.gameScene.selectCard(cardSprite)
      }
    })
  }

  // 查找卡牌精灵
  findCardSprite(card) {
    if (!this.gameScene.playerHandAreas || !this.gameScene.playerHandAreas.bottom) {
      return null
    }
    
    return this.gameScene.playerHandAreas.bottom.cards.find(sprite => 
      sprite.cardData && sprite.cardData.id === card.id
    )
  }

  // 生成出牌建议
  generatePlaySuggestions() {
    console.log('💡 生成出牌建议')
    
    const gameInfo = this.gameScene.gameState.getGameInfo()
    const lastPattern = gameInfo.lastPlayedPattern
    
    // 根据上家出牌生成建议
    this.suggestedPlays = this.findValidPlays(lastPattern)
    this.updateSuggestionDisplay()
  }

  // 查找有效出牌
  findValidPlays(lastPattern) {
    const suggestions = []
    
    if (!lastPattern) {
      // 首次出牌，建议最小的牌型
      const singles = this.getPlayerCards().slice(0, 3)
      const pairs = this.findPairs().slice(0, 2)
      const triples = this.findTriples().slice(0, 1)
      
      suggestions.push(...singles.map(card => [card]))
      suggestions.push(...pairs)
      suggestions.push(...triples)
    } else {
      // 根据上家牌型找对应的更大牌型
      // 这里简化处理，实际应该根据牌型规则匹配
      const bombs = this.findBombs()
      suggestions.push(...bombs)
    }
    
    return suggestions.slice(0, 5) // 最多5个建议
  }

  // 更新建议显示
  updateSuggestionDisplay() {
    if (!this.uiElements.suggestionPanel) return
    
    // 清除旧的建议按钮
    this.uiElements.suggestionPanel.list.forEach(item => {
      if (item.isSuggestionButton) {
        item.destroy()
      }
    })
    
    // 添加新的建议按钮
    this.suggestedPlays.forEach((play, index) => {
      const buttonY = -100 + index * 40
      const playText = play.map(c => c.getDisplayName()).join('')
      
      const button = this.createQuickButton(
        this.gameScene, 
        0, 
        buttonY, 
        playText, 
        () => this.selectCards(play)
      )
      
      button.isSuggestionButton = true
      this.uiElements.suggestionPanel.add(button)
    })
  }

  // 更新建议
  updateSuggestions() {
    this.generatePlaySuggestions()
  }

  // 添加事件监听器
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  // 触发事件
  dispatchEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => callback(data))
    }
  }

  // 销毁管理器
  destroy() {
    console.log('🗑️ 销毁出牌交互管理器')
    
    Object.values(this.uiElements).forEach(element => {
      if (element) {
        element.destroy()
      }
    })
    
    this.eventListeners.clear()
  }
}
