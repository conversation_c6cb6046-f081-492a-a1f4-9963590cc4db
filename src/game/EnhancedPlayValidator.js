// 增强的出牌规则验证器
export class EnhancedPlayValidator {
  constructor(cardPattern) {
    this.cardPattern = cardPattern
    
    // 错误类型定义
    this.errorTypes = {
      INVALID_PATTERN: 'invalid_pattern',
      PATTERN_MISMATCH: 'pattern_mismatch',
      INSUFFICIENT_WEIGHT: 'insufficient_weight',
      CARD_COUNT_MISMATCH: 'card_count_mismatch',
      SPECIAL_RULE_VIOLATION: 'special_rule_violation',
      PLAYER_CONSTRAINT: 'player_constraint'
    }
    
    // 建议类型定义
    this.suggestionTypes = {
      PLAY_HIGHER: 'play_higher',
      PLAY_BOMB: 'play_bomb',
      PLAY_ROCKET: 'play_rocket',
      CHANGE_PATTERN: 'change_pattern',
      PASS_TURN: 'pass_turn'
    }
    
    console.log('🛡️ 增强出牌规则验证器初始化完成')
  }

  // 主要验证方法 - 返回详细的验证结果
  validatePlay(cards, lastPattern = null, playerCards = null, gameContext = null) {
    console.log('🔍 开始增强出牌验证')
    console.log('📋 当前出牌:', cards.map(c => c.getDisplayName()).join(' '))
    console.log('📋 上家牌型:', lastPattern ? lastPattern.type : '无')
    
    // 基础验证
    const basicValidation = this.performBasicValidation(cards)
    if (!basicValidation.valid) {
      return this.createValidationResult(false, basicValidation)
    }
    
    const currentPattern = basicValidation.pattern
    
    // 首出牌验证
    if (!lastPattern) {
      return this.validateFirstPlay(currentPattern, cards, playerCards, gameContext)
    }
    
    // 跟牌验证
    return this.validateFollowPlay(currentPattern, lastPattern, cards, playerCards, gameContext)
  }

  // 基础验证：牌型识别
  performBasicValidation(cards) {
    if (!cards || cards.length === 0) {
      return {
        valid: false,
        errorType: this.errorTypes.INVALID_PATTERN,
        message: '请选择要出的牌',
        details: '没有选择任何卡牌'
      }
    }

    const pattern = this.cardPattern.identifyPattern(cards)
    if (!pattern) {
      return {
        valid: false,
        errorType: this.errorTypes.INVALID_PATTERN,
        message: '无效的牌型组合',
        details: this.analyzeInvalidPattern(cards)
      }
    }

    return {
      valid: true,
      pattern: pattern
    }
  }

  // 分析无效牌型的具体原因
  analyzeInvalidPattern(cards) {
    const cardCount = cards.length
    const groups = this.cardPattern.groupByRank(cards)
    const groupSizes = Object.values(groups).map(group => group.length).sort((a, b) => b - a)
    
    // 根据卡牌数量和分组情况给出具体建议
    if (cardCount === 1) {
      return '单牌应该只选择一张牌'
    } else if (cardCount === 2) {
      if (groupSizes[0] === 1) {
        return '对子需要两张相同点数的牌，当前选择的是两张不同的牌'
      }
    } else if (cardCount === 3) {
      if (groupSizes[0] === 2) {
        return '三张需要三张相同点数的牌，当前只有两张相同'
      } else if (groupSizes[0] === 1) {
        return '三张需要三张相同点数的牌，当前选择的都是不同的牌'
      }
    } else if (cardCount === 4) {
      if (groupSizes[0] === 3 && groupSizes[1] === 1) {
        return '三带一：三张相同点数的牌加一张单牌'
      } else if (groupSizes[0] === 4) {
        return '炸弹：四张相同点数的牌'
      } else {
        return '四张牌可以组成三带一或炸弹，请检查牌型组合'
      }
    } else if (cardCount === 5) {
      if (groupSizes[0] === 3 && groupSizes[1] === 2) {
        return '三带二：三张相同点数的牌加一对'
      } else {
        return '五张牌可以组成三带二或顺子，请检查牌型组合'
      }
    } else if (cardCount >= 5) {
      return '多张牌可以组成顺子、连对、飞机等牌型，请检查是否符合规则'
    }
    
    return '牌型组合不符合斗地主规则'
  }

  // 验证首出牌
  validateFirstPlay(pattern, cards, playerCards, gameContext) {
    console.log('✅ 首出牌验证')
    
    // 首出牌基本上都是合法的，但可以给出策略建议
    const suggestions = this.generateFirstPlaySuggestions(pattern, cards, playerCards, gameContext)
    
    return this.createValidationResult(true, {
      pattern: pattern,
      message: '可以出牌',
      suggestions: suggestions,
      isFirstPlay: true
    })
  }

  // 验证跟牌
  validateFollowPlay(currentPattern, lastPattern, cards, playerCards, gameContext) {
    console.log('🔍 跟牌验证')
    
    // 火箭可以压任何牌
    if (currentPattern.type === this.cardPattern.patterns.ROCKET) {
      return this.createValidationResult(true, {
        pattern: currentPattern,
        message: '火箭可以压任何牌型',
        isSpecialPlay: true
      })
    }
    
    // 炸弹类型的特殊处理
    const isBombType = this.isBombType(currentPattern.type)
    const lastIsBombType = this.isBombType(lastPattern.type)
    
    if (isBombType && !lastIsBombType) {
      return this.createValidationResult(true, {
        pattern: currentPattern,
        message: '炸弹可以压非炸弹牌型',
        isSpecialPlay: true
      })
    }
    
    // 同类型牌型比较
    if (this.isSamePatternType(currentPattern, lastPattern)) {
      return this.validateSameTypePattern(currentPattern, lastPattern, cards)
    }
    
    // 不同类型牌型
    return this.validateDifferentTypePattern(currentPattern, lastPattern, cards, playerCards)
  }

  // 判断是否为炸弹类型
  isBombType(patternType) {
    return [
      this.cardPattern.patterns.BOMB,
      this.cardPattern.patterns.BOMB_WITH_SINGLES,
      this.cardPattern.patterns.BOMB_WITH_PAIRS
    ].includes(patternType)
  }

  // 判断是否为相同牌型类型
  isSamePatternType(pattern1, pattern2) {
    // 基础牌型直接比较
    if (pattern1.type === pattern2.type) {
      return true
    }
    
    // 炸弹类型之间可以比较
    if (this.isBombType(pattern1.type) && this.isBombType(pattern2.type)) {
      return true
    }
    
    return false
  }

  // 验证相同类型牌型
  validateSameTypePattern(currentPattern, lastPattern, cards) {
    // 检查牌数是否匹配
    if (cards.length !== lastPattern.cards.length) {
      return this.createValidationResult(false, {
        errorType: this.errorTypes.CARD_COUNT_MISMATCH,
        message: `牌数不匹配：需要${lastPattern.cards.length}张牌，当前${cards.length}张`,
        details: `上家出了${lastPattern.cards.length}张牌，您也需要出${lastPattern.cards.length}张牌`,
        suggestions: this.generateCardCountSuggestions(lastPattern, cards)
      })
    }
    
    // 比较牌型大小
    const comparison = this.cardPattern.comparePatterns(currentPattern, lastPattern)
    if (comparison > 0) {
      return this.createValidationResult(true, {
        pattern: currentPattern,
        message: '可以出牌，牌型更大',
        comparison: comparison
      })
    } else {
      return this.createValidationResult(false, {
        errorType: this.errorTypes.INSUFFICIENT_WEIGHT,
        message: '牌型大小不够',
        details: this.generateWeightComparisonDetails(currentPattern, lastPattern),
        suggestions: this.generateHigherCardSuggestions(lastPattern, currentPattern)
      })
    }
  }

  // 验证不同类型牌型
  validateDifferentTypePattern(currentPattern, lastPattern, cards, playerCards) {
    // 只有炸弹和火箭可以压其他类型
    if (!this.isBombType(currentPattern.type) && currentPattern.type !== this.cardPattern.patterns.ROCKET) {
      return this.createValidationResult(false, {
        errorType: this.errorTypes.PATTERN_MISMATCH,
        message: '牌型不匹配',
        details: `上家出的是${this.getPatternDisplayName(lastPattern.type)}，您需要出相同类型的牌型或炸弹`,
        suggestions: this.generatePatternMatchSuggestions(lastPattern, playerCards)
      })
    }
    
    // 如果上家是炸弹，当前必须是更大的炸弹或火箭
    if (this.isBombType(lastPattern.type)) {
      if (currentPattern.type === this.cardPattern.patterns.ROCKET) {
        return this.createValidationResult(true, {
          pattern: currentPattern,
          message: '火箭可以压炸弹',
          isSpecialPlay: true
        })
      }
      
      if (this.isBombType(currentPattern.type)) {
        const comparison = this.cardPattern.comparePatterns(currentPattern, lastPattern)
        if (comparison > 0) {
          return this.createValidationResult(true, {
            pattern: currentPattern,
            message: '炸弹更大，可以出牌',
            isSpecialPlay: true
          })
        } else {
          return this.createValidationResult(false, {
            errorType: this.errorTypes.INSUFFICIENT_WEIGHT,
            message: '炸弹大小不够',
            details: '需要更大的炸弹或火箭才能压过上家的炸弹',
            suggestions: this.generateBombSuggestions(lastPattern, playerCards)
          })
        }
      }
    }
    
    return this.createValidationResult(false, {
      errorType: this.errorTypes.PATTERN_MISMATCH,
      message: '无法出牌',
      details: '当前牌型无法压过上家的牌',
      suggestions: [{ type: this.suggestionTypes.PASS_TURN, message: '建议过牌' }]
    })
  }

  // 生成首出牌建议
  generateFirstPlaySuggestions(pattern, cards, playerCards, gameContext) {
    const suggestions = []
    
    // 根据牌型给出策略建议
    switch (pattern.type) {
      case this.cardPattern.patterns.SINGLE:
        suggestions.push({
          type: 'strategy',
          message: '建议出小牌，保留大牌'
        })
        break
      case this.cardPattern.patterns.BOMB:
      case this.cardPattern.patterns.ROCKET:
        suggestions.push({
          type: 'warning',
          message: '首出炸弹/火箭可能不是最佳策略'
        })
        break
      default:
        suggestions.push({
          type: 'info',
          message: '首出牌，可以控制节奏'
        })
    }
    
    return suggestions
  }

  // 生成牌数匹配建议
  generateCardCountSuggestions(lastPattern, currentCards) {
    const needed = lastPattern.cards.length
    const current = currentCards.length
    const diff = needed - current
    
    if (diff > 0) {
      return [{
        type: this.suggestionTypes.CHANGE_PATTERN,
        message: `请再选择${diff}张牌，或选择炸弹/火箭`
      }]
    } else {
      return [{
        type: this.suggestionTypes.CHANGE_PATTERN,
        message: `请减少${-diff}张牌，或选择炸弹/火箭`
      }]
    }
  }

  // 生成权重比较详情
  generateWeightComparisonDetails(currentPattern, lastPattern) {
    const currentWeight = currentPattern.weight
    const lastWeight = lastPattern.weight
    
    return `当前牌型权重: ${currentWeight}，上家牌型权重: ${lastWeight}，需要更大的牌`
  }

  // 生成更大牌的建议
  generateHigherCardSuggestions(lastPattern, currentPattern) {
    return [{
      type: this.suggestionTypes.PLAY_HIGHER,
      message: `需要比${this.getPatternDisplayName(lastPattern.type)}更大的牌`
    }, {
      type: this.suggestionTypes.PLAY_BOMB,
      message: '或者使用炸弹/火箭'
    }]
  }

  // 生成牌型匹配建议
  generatePatternMatchSuggestions(lastPattern, playerCards) {
    const suggestions = []
    
    suggestions.push({
      type: this.suggestionTypes.CHANGE_PATTERN,
      message: `需要出${this.getPatternDisplayName(lastPattern.type)}类型的牌`
    })
    
    suggestions.push({
      type: this.suggestionTypes.PLAY_BOMB,
      message: '或者使用炸弹/火箭压牌'
    })
    
    return suggestions
  }

  // 生成炸弹建议
  generateBombSuggestions(lastPattern, playerCards) {
    return [{
      type: this.suggestionTypes.PLAY_BOMB,
      message: '需要更大的炸弹'
    }, {
      type: this.suggestionTypes.PLAY_ROCKET,
      message: '或者使用火箭'
    }]
  }

  // 获取牌型显示名称
  getPatternDisplayName(patternType) {
    const displayNames = {
      [this.cardPattern.patterns.SINGLE]: '单牌',
      [this.cardPattern.patterns.PAIR]: '对子',
      [this.cardPattern.patterns.TRIPLE]: '三张',
      [this.cardPattern.patterns.TRIPLE_WITH_SINGLE]: '三带一',
      [this.cardPattern.patterns.TRIPLE_WITH_PAIR]: '三带二',
      [this.cardPattern.patterns.STRAIGHT]: '顺子',
      [this.cardPattern.patterns.PAIR_STRAIGHT]: '连对',
      [this.cardPattern.patterns.TRIPLE_STRAIGHT]: '飞机',
      [this.cardPattern.patterns.BOMB]: '炸弹',
      [this.cardPattern.patterns.BOMB_WITH_SINGLES]: '四带二（单）',
      [this.cardPattern.patterns.BOMB_WITH_PAIRS]: '四带二（对）',
      [this.cardPattern.patterns.ROCKET]: '火箭'
    }
    
    return displayNames[patternType] || patternType
  }

  // 创建验证结果
  createValidationResult(valid, data = {}) {
    const result = {
      valid: valid,
      timestamp: Date.now(),
      ...data
    }
    
    console.log('🎯 验证结果:', valid ? '✅ 通过' : '❌ 失败')
    if (!valid && data.message) {
      console.log('📝 失败原因:', data.message)
    }
    
    return result
  }

  // 获取详细的出牌提示
  getPlayHint(cards, lastPattern = null, playerCards = null, gameContext = null) {
    const validation = this.validatePlay(cards, lastPattern, playerCards, gameContext)
    
    return {
      valid: validation.valid,
      pattern: validation.pattern,
      message: validation.message || (validation.valid ? '可以出牌' : '无法出牌'),
      details: validation.details,
      suggestions: validation.suggestions || [],
      errorType: validation.errorType,
      isFirstPlay: validation.isFirstPlay || false,
      isSpecialPlay: validation.isSpecialPlay || false
    }
  }

  // 分析玩家手牌，提供出牌建议
  analyzePlayerCards(playerCards, lastPattern = null) {
    console.log('🔍 分析玩家手牌，提供出牌建议')
    
    const suggestions = []
    
    // 如果没有上家出牌，建议首出策略
    if (!lastPattern) {
      suggestions.push(...this.generateFirstPlayStrategySuggestions(playerCards))
    } else {
      suggestions.push(...this.generateFollowPlaySuggestions(playerCards, lastPattern))
    }
    
    return suggestions
  }

  // 生成首出策略建议
  generateFirstPlayStrategySuggestions(playerCards) {
    const suggestions = []
    
    // 分析手牌结构
    const groups = this.cardPattern.groupByRank(playerCards)
    const singles = []
    const pairs = []
    const triples = []
    
    Object.values(groups).forEach(group => {
      if (group.length === 1) singles.push(group[0])
      else if (group.length === 2) pairs.push(group)
      else if (group.length >= 3) triples.push(group)
    })
    
    // 建议出小牌
    if (singles.length > 0) {
      const smallestSingle = singles.sort((a, b) => a.value - b.value)[0]
      suggestions.push({
        type: 'recommendation',
        cards: [smallestSingle],
        message: `建议出最小单牌: ${smallestSingle.getDisplayName()}`
      })
    }
    
    return suggestions
  }

  // 生成跟牌建议
  generateFollowPlaySuggestions(playerCards, lastPattern) {
    const suggestions = []
    
    // 根据上家牌型找对应的牌
    // 这里可以实现更复杂的AI逻辑
    
    suggestions.push({
      type: 'info',
      message: `需要出${this.getPatternDisplayName(lastPattern.type)}或更大的牌`
    })
    
    return suggestions
  }
}
