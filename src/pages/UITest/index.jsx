import React, { useState, useRef, useEffect } from 'react'
import {
  Button,
  Card,
  Input,
  Select,
  Switch,
  Slider,
  Progress,
  Tag,
  Alert,
  message,
  Space,
  Divider,
  Typography,
  Row,
  Col
} from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined
} from '@ant-design/icons'
import GameScene from '../../game/GameScene'
import { LandlordSelection } from '../../components/LandlordSelection'
import './style.css'

const { Title, Paragraph, Text } = Typography
const { Option } = Select

const UITest = () => {
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState(30)
  const [switchValue, setSwitchValue] = useState(true)
  const [sliderValue, setSliderValue] = useState(50)
  const [biddingVisible, setBiddingVisible] = useState(false)
  const [currentBid, setCurrentBid] = useState(0)
  const gameContainerRef = useRef(null)
  const gameInstanceRef = useRef(null)

  const handleTestAction = (actionName) => {
    setLoading(true)
    message.success(`执行${actionName}测试成功！`)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }

  const handleProgressTest = () => {
    setProgress(0)
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer)
          message.success('进度条测试完成！')
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  const initializeGame = () => {
    if (gameContainerRef.current && !gameInstanceRef.current) {
      try {
        console.log('🎮 初始化游戏...')
        gameInstanceRef.current = new GameScene(gameContainerRef.current)
        message.success('游戏初始化成功！')
      } catch (error) {
        console.error('游戏初始化失败:', error)
        message.error('游戏初始化失败：' + error.message)
      }
    }
  }

  const startTestGame = () => {
    if (gameInstanceRef.current) {
      try {
        console.log('🎮 开始测试游戏...')
        gameInstanceRef.current.startNewGame()
        message.success('游戏开始！')
      } catch (error) {
        console.error('开始游戏失败:', error)
        message.error('开始游戏失败：' + error.message)
      }
    } else {
      message.warning('请先初始化游戏！')
    }
  }

  const destroyGame = () => {
    if (gameInstanceRef.current) {
      try {
        gameInstanceRef.current.destroy()
        gameInstanceRef.current = null
        message.success('游戏已销毁！')
      } catch (error) {
        console.error('销毁游戏失败:', error)
        message.error('销毁游戏失败：' + error.message)
      }
    }
  }

  // 叫牌测试相关函数
  const showBiddingTest = () => {
    setBiddingVisible(true)
    setCurrentBid(0)
    message.info('叫牌倒计时测试开始！')
  }

  const handleBidConfirm = (bidValue) => {
    setCurrentBid(bidValue)
    setBiddingVisible(false)
    message.success(`叫牌成功：${bidValue}分`)
  }

  const handleBidPass = () => {
    setBiddingVisible(false)
    message.info('选择不叫')
  }

  useEffect(() => {
    return () => {
      destroyGame()
    }
  }, [])

  return (
    <div className="ui-test-page">
      <div className="test-header">
        <Title level={1}>🎮 UI组件测试页面</Title>
        <Paragraph>
          这是斗地主游戏的UI组件测试页面，用于测试各种界面组件和交互效果。
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        {/* 按钮测试区域 */}
        <Col xs={24} md={12}>
          <Card title="🔘 按钮组件测试" className="test-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Space wrap>
                <Button type="primary" icon={<PlayCircleOutlined />}>
                  开始游戏
                </Button>
                <Button type="default" icon={<PauseCircleOutlined />}>
                  暂停游戏
                </Button>
                <Button type="dashed" icon={<ReloadOutlined />}>
                  重新开始
                </Button>
              </Space>
              
              <Space wrap>
                <Button size="large" type="primary">
                  大按钮
                </Button>
                <Button size="middle" type="default">
                  中按钮
                </Button>
                <Button size="small" type="link">
                  小按钮
                </Button>
              </Space>

              <Space wrap>
                <Button 
                  type="primary" 
                  loading={loading}
                  onClick={() => handleTestAction('按钮点击')}
                >
                  测试加载状态
                </Button>
                <Button danger>
                  危险按钮
                </Button>
                <Button disabled>
                  禁用按钮
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 输入组件测试区域 */}
        <Col xs={24} md={12}>
          <Card title="📝 输入组件测试" className="test-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input placeholder="请输入玩家昵称" />
              <Input.Password placeholder="请输入密码" />
              <Select 
                placeholder="选择游戏难度" 
                style={{ width: '100%' }}
                defaultValue="normal"
              >
                <Option value="easy">简单</Option>
                <Option value="normal">普通</Option>
                <Option value="hard">困难</Option>
              </Select>
              
              <div>
                <Text>音效开关：</Text>
                <Switch 
                  checked={switchValue} 
                  onChange={setSwitchValue}
                  checkedChildren="开"
                  unCheckedChildren="关"
                />
              </div>

              <div>
                <Text>音量调节：{sliderValue}%</Text>
                <Slider 
                  value={sliderValue} 
                  onChange={setSliderValue}
                  marks={{
                    0: '静音',
                    50: '适中',
                    100: '最大'
                  }}
                />
              </div>
            </Space>
          </Card>
        </Col>

        {/* 进度和状态测试 */}
        <Col xs={24} md={12}>
          <Card title="📊 进度和状态测试" className="test-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>游戏进度：</Text>
                <Progress percent={progress} status="active" />
                <Button 
                  type="primary" 
                  size="small" 
                  onClick={handleProgressTest}
                  style={{ marginTop: 8 }}
                >
                  测试进度条
                </Button>
              </div>

              <Divider />

              <div>
                <Text>玩家状态标签：</Text>
                <div style={{ marginTop: 8 }}>
                  <Space wrap>
                    <Tag color="green">在线</Tag>
                    <Tag color="orange">游戏中</Tag>
                    <Tag color="red">离线</Tag>
                    <Tag color="blue">等待中</Tag>
                    <Tag color="purple">观战</Tag>
                  </Space>
                </div>
              </div>

              <Divider />

              <div>
                <Text>游戏评分：</Text>
                <div style={{ marginTop: 8 }}>
                  <Space>
                    <Button icon={<HeartOutlined />} size="small">
                      喜欢 (128)
                    </Button>
                    <Button icon={<StarOutlined />} size="small">
                      收藏 (56)
                    </Button>
                    <Button icon={<LikeOutlined />} size="small">
                      点赞 (89)
                    </Button>
                  </Space>
                </div>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 消息提示测试 */}
        <Col xs={24} md={12}>
          <Card title="💬 消息提示测试" className="test-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="游戏提示"
                description="这是一个信息提示，用于显示游戏相关信息。"
                type="info"
                showIcon
              />
              
              <Alert
                message="成功消息"
                description="恭喜您获得胜利！经验值 +100"
                type="success"
                showIcon
              />

              <Alert
                message="警告信息"
                description="您的连接不稳定，可能影响游戏体验。"
                type="warning"
                showIcon
              />

              <Space wrap>
                <Button onClick={() => message.success('操作成功！')}>
                  成功消息
                </Button>
                <Button onClick={() => message.error('操作失败！')}>
                  错误消息
                </Button>
                <Button onClick={() => message.warning('注意事项！')}>
                  警告消息
                </Button>
                <Button onClick={() => message.info('提示信息！')}>
                  信息消息
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 游戏功能测试 */}
        <Col xs={24}>
          <Card title="🎮 游戏功能测试" className="test-card">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Alert
                    message="游戏测试说明"
                    description="点击下方按钮测试斗地主游戏的核心功能。请按顺序操作：先初始化游戏，然后开始游戏。"
                    type="info"
                    showIcon
                  />

                  <Space wrap>
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={initializeGame}
                    >
                      初始化游戏
                    </Button>
                    <Button
                      type="default"
                      icon={<PlayCircleOutlined />}
                      onClick={startTestGame}
                    >
                      开始游戏
                    </Button>
                    <Button
                      type="dashed"
                      icon={<ReloadOutlined />}
                      onClick={destroyGame}
                    >
                      销毁游戏
                    </Button>
                  </Space>

                  <Divider />

                  <Alert
                    message="叫牌倒计时测试"
                    description="测试叫牌界面的15秒倒计时功能，超时会自动选择'不叫'。"
                    type="warning"
                    showIcon
                  />

                  <Space wrap>
                    <Button
                      type="primary"
                      onClick={showBiddingTest}
                    >
                      测试叫牌倒计时
                    </Button>
                    <Text>当前叫分：{currentBid === 0 ? '暂无' : `${currentBid}分`}</Text>
                  </Space>
                </Space>
              </Col>

              <Col xs={24} md={12}>
                <div
                  ref={gameContainerRef}
                  style={{
                    width: '100%',
                    height: '400px',
                    border: '2px solid #d9d9d9',
                    borderRadius: '6px',
                    background: '#000',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                    fontSize: '16px'
                  }}
                >
                  游戏画布区域 - 点击"初始化游戏"开始
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 游戏主题测试 */}
        <Col xs={24}>
          <Card title="🎨 游戏主题效果测试" className="test-card game-theme-card">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <div className="theme-demo theme-classic">
                  <Title level={4}>经典主题</Title>
                  <Paragraph>传统的斗地主界面风格</Paragraph>
                  <Button type="primary">选择主题</Button>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="theme-demo theme-modern">
                  <Title level={4}>现代主题</Title>
                  <Paragraph>简约现代的界面设计</Paragraph>
                  <Button type="primary">选择主题</Button>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="theme-demo theme-dark">
                  <Title level={4}>暗黑主题</Title>
                  <Paragraph>护眼的深色界面模式</Paragraph>
                  <Button type="primary">选择主题</Button>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <div className="test-footer">
        <Divider />
        <Text type="secondary">
          UI组件测试页面 - 斗地主游戏系统 v1.0
        </Text>
      </div>

      {/* 叫牌倒计时测试组件 */}
      <LandlordSelection
        visible={biddingVisible}
        bidValue={currentBid}
        onConfirm={handleBidConfirm}
        onPass={handleBidPass}
        countdown={15}
      />
    </div>
  )
}

export default UITest
