import React, { useState, useEffect, useCallback } from 'react'
import './BiddingPanel.css'

// 叫牌面板组件
const BiddingPanel = ({
  // 基础属性
  isMyTurn = false,           // 是否轮到当前玩家
  availableBids = [],         // 可用的叫牌选项
  timeLimit = 15,             // 叫牌时限（秒）
  
  // 状态信息
  currentBidder = null,       // 当前叫牌者
  bidHistory = [],            // 叫牌历史
  highestBid = null,          // 当前最高叫牌
  
  // 特殊提示
  hasSpecialCards = false,    // 是否有特殊牌型（双王或4个2）
  specialCardType = '',       // 特殊牌型类型
  
  // 回调函数
  onBid = () => {},           // 叫牌回调
  onTimeout = () => {},       // 超时回调
  
  // 显示控制
  visible = true,             // 是否显示面板
  disabled = false            // 是否禁用
}) => {
  const [timeLeft, setTimeLeft] = useState(timeLimit)
  const [isCountingDown, setIsCountingDown] = useState(false)

  // 叫牌选项配置
  const bidOptions = {
    no_bid: { label: '不叫', value: 'no_bid', color: '#6c757d' },
    one_point: { label: '1分', value: 'one_point', color: '#28a745' },
    two_point: { label: '2分', value: 'two_point', color: '#ffc107' },
    three_point: { label: '3分', value: 'three_point', color: '#dc3545' }
  }

  // 座位显示名称
  const seatNames = {
    west: '西家',
    south: '南家', 
    east: '东家'
  }

  // 倒计时逻辑
  useEffect(() => {
    if (isMyTurn && !disabled) {
      setIsCountingDown(true)
      setTimeLeft(timeLimit)
    } else {
      setIsCountingDown(false)
    }
  }, [isMyTurn, disabled, timeLimit])

  useEffect(() => {
    let timer = null
    if (isCountingDown && timeLeft > 0) {
      timer = setTimeout(() => {
        setTimeLeft(prev => prev - 1)
      }, 1000)
    } else if (isCountingDown && timeLeft === 0) {
      // 超时处理
      setIsCountingDown(false)
      onTimeout()
    }

    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isCountingDown, timeLeft, onTimeout])

  // 处理叫牌
  const handleBid = useCallback((bidValue) => {
    if (!isMyTurn || disabled) return
    
    setIsCountingDown(false)
    onBid(bidValue)
  }, [isMyTurn, disabled, onBid])

  // 获取倒计时颜色
  const getTimerColor = () => {
    if (timeLeft <= 5) return '#dc3545' // 红色警告
    if (timeLeft <= 10) return '#ffc107' // 黄色提醒
    return '#28a745' // 绿色正常
  }

  // 渲染叫牌按钮
  const renderBidButton = (bidKey) => {
    const option = bidOptions[bidKey]
    const isAvailable = availableBids.includes(bidKey)
    const isDisabled = !isMyTurn || disabled || !isAvailable

    return (
      <button
        key={bidKey}
        className={`bid-button ${isDisabled ? 'disabled' : ''} ${bidKey}`}
        style={{ 
          backgroundColor: isDisabled ? '#e9ecef' : option.color,
          color: isDisabled ? '#6c757d' : 'white'
        }}
        onClick={() => handleBid(bidKey)}
        disabled={isDisabled}
      >
        {option.label}
      </button>
    )
  }

  // 渲染叫牌历史
  const renderBidHistory = () => {
    if (bidHistory.length === 0) return null

    return (
      <div className="bid-history">
        <h4>叫牌记录：</h4>
        <div className="history-list">
          {bidHistory.map((bid, index) => (
            <span key={index} className="history-item">
              {seatNames[bid.player] || bid.player}: {bidOptions[bid.option]?.label || bid.option}
            </span>
          ))}
        </div>
      </div>
    )
  }

  // 渲染特殊牌型提示
  const renderSpecialCardTip = () => {
    if (!hasSpecialCards) return null

    return (
      <div className="special-tip">
        <div className="tip-icon">⚠️</div>
        <div className="tip-content">
          <strong>特殊牌型提示：</strong>
          <br />
          您持有{specialCardType}，可以选择不叫分
        </div>
      </div>
    )
  }

  if (!visible) return null

  return (
    <div className={`bidding-panel ${disabled ? 'disabled' : ''}`}>
      {/* 标题区域 */}
      <div className="panel-header">
        <h3>叫牌阶段</h3>
        {isMyTurn && (
          <div className="timer" style={{ color: getTimerColor() }}>
            <span className="timer-icon">⏰</span>
            <span className="timer-text">{timeLeft}秒</span>
          </div>
        )}
      </div>

      {/* 当前状态 */}
      <div className="current-status">
        {isMyTurn ? (
          <div className="my-turn">
            <span className="status-icon">👆</span>
            <span>轮到您叫牌了</span>
          </div>
        ) : (
          <div className="waiting">
            <span className="status-icon">⏳</span>
            <span>等待 {seatNames[currentBidder] || currentBidder} 叫牌...</span>
          </div>
        )}
      </div>

      {/* 当前最高叫牌 */}
      {highestBid && (
        <div className="highest-bid">
          <strong>当前最高：</strong>
          {seatNames[highestBid.player] || highestBid.player} - {bidOptions[highestBid.option]?.label}
        </div>
      )}

      {/* 特殊牌型提示 */}
      {renderSpecialCardTip()}

      {/* 叫牌按钮区域 */}
      <div className="bid-buttons">
        {Object.keys(bidOptions).map(renderBidButton)}
      </div>

      {/* 叫牌历史 */}
      {renderBidHistory()}

      {/* 规则提示 */}
      <div className="rules-tip">
        <small>
          💡 叫牌规则：后叫者只能叫比前面更高的分数，或选择不叫
        </small>
      </div>
    </div>
  )
}

export default BiddingPanel
