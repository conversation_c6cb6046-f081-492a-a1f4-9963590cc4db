/* 叫牌面板样式 */
.bidding-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
}

.bidding-panel.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 标题区域 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 12px;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.timer {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: bold;
  font-size: 16px;
  animation: pulse 1s infinite;
}

.timer-icon {
  font-size: 18px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 当前状态 */
.current-status {
  margin-bottom: 16px;
  text-align: center;
}

.my-turn {
  background: rgba(40, 167, 69, 0.3);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(40, 167, 69, 0.5);
}

.waiting {
  background: rgba(255, 193, 7, 0.3);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 193, 7, 0.5);
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 最高叫牌显示 */
.highest-bid {
  background: rgba(220, 53, 69, 0.3);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 16px;
  text-align: center;
  border: 1px solid rgba(220, 53, 69, 0.5);
}

/* 特殊牌型提示 */
.special-tip {
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  border: 1px solid #ffeaa7;
}

.tip-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

/* 叫牌按钮区域 */
.bid-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.bid-button {
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.bid-button:not(.disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.bid-button:not(.disabled):active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bid-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transform: none !important;
}

/* 特定按钮样式 */
.bid-button.no_bid {
  background: #6c757d;
}

.bid-button.one_point {
  background: #28a745;
}

.bid-button.two_point {
  background: #ffc107;
  color: #212529 !important;
}

.bid-button.three_point {
  background: #dc3545;
}

/* 按钮点击效果 */
.bid-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.bid-button:not(.disabled):active::before {
  width: 100%;
  height: 100%;
}

/* 叫牌历史 */
.bid-history {
  margin-bottom: 16px;
}

.bid-history h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-item {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 规则提示 */
.rules-tip {
  text-align: center;
  opacity: 0.8;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .bidding-panel {
    padding: 16px;
    margin: 0 10px;
  }
  
  .bid-buttons {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .bid-button {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .panel-header h3 {
    font-size: 16px;
  }
  
  .timer {
    font-size: 14px;
  }
}

/* 动画效果 */
.bidding-panel {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .bidding-panel {
    background: #000;
    border: 2px solid #fff;
  }
  
  .bid-button {
    border: 2px solid #fff;
  }
}
