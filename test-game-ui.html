<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏UI实际测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .controls button:hover {
            background-color: #66BB6A;
        }
        .status {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        #game {
            border: 2px solid #FFD700;
            border-radius: 8px;
            background: #1E3A8A;
        }
        .log {
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🎮 游戏UI实际测试</h1>
        <p>测试游戏UI是否能正常显示和交互</p>
        
        <div class="controls">
            <button onclick="startGame()">启动游戏</button>
            <button onclick="testBiddingUI()">测试叫牌UI</button>
            <button onclick="testPlayingUI()">测试出牌UI</button>
            <button onclick="destroyGame()">销毁游戏</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="status" id="gameStatus">
            游戏状态: 未启动
        </div>
        
        <div id="game"></div>
        
        <div class="log" id="testLog">
            等待测试开始...
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null

        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function updateStatus(status) {
            document.getElementById('gameStatus').textContent = `游戏状态: ${status}`
        }

        window.startGame = async function() {
            log('🎮 开始启动游戏')
            updateStatus('启动中...')
            
            try {
                // 销毁之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }

                // 导入GameScene
                const { GameScene } = await import('./src/game/GameScene.js')
                log('✅ GameScene导入成功')

                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 1200,
                    height: 800,
                    parent: 'game',
                    backgroundColor: '#1E3A8A',
                    scene: GameScene
                }

                // 创建游戏实例
                gameInstance = new Phaser.Game(config)
                log('✅ Phaser游戏实例创建成功')
                updateStatus('游戏已启动')

                // 等待场景创建完成
                setTimeout(() => {
                    gameScene = gameInstance.scene.getScene('GameScene')
                    if (gameScene) {
                        log('✅ 游戏场景获取成功')
                        updateStatus('游戏场景已就绪')
                    } else {
                        log('❌ 游戏场景获取失败')
                        updateStatus('游戏场景获取失败')
                    }
                }, 2000)

            } catch (error) {
                log(`❌ 游戏启动失败: ${error.message}`)
                updateStatus('启动失败')
            }
        }

        window.testBiddingUI = function() {
            log('🧪 开始测试叫牌UI')
            
            if (!gameScene) {
                log('❌ 游戏场景未就绪，请先启动游戏')
                return
            }

            try {
                // 测试叫牌UI显示
                if (typeof gameScene.showBiddingUIWithManager === 'function') {
                    log('✅ showBiddingUIWithManager方法存在')
                    
                    // 模拟叫牌数据
                    const mockBiddingData = {
                        isMyTurn: true,
                        availableBids: ['no_bid', 'one_point', 'two_point', 'three_point'],
                        biddingStatus: {
                            highestBid: null,
                            highestBidder: null
                        },
                        gameState: {
                            phase: 'bidding'
                        },
                        timeLimit: 20
                    }
                    
                    // 显示叫牌UI
                    gameScene.showBiddingUIWithManager(mockBiddingData)
                    log('✅ 叫牌UI显示命令已发送')
                    updateStatus('叫牌UI测试中')
                    
                } else {
                    log('❌ showBiddingUIWithManager方法不存在')
                }

                // 测试按钮显示更新
                if (typeof gameScene.updateButtonDisplay === 'function') {
                    log('✅ updateButtonDisplay方法存在')
                    
                    const mockGameState = {
                        phase: 'bidding',
                        isMyTurn: true
                    }
                    
                    gameScene.updateButtonDisplay(mockGameState)
                    log('✅ 按钮显示更新命令已发送')
                    
                } else {
                    log('❌ updateButtonDisplay方法不存在')
                }

            } catch (error) {
                log(`❌ 叫牌UI测试失败: ${error.message}`)
            }
        }

        window.testPlayingUI = function() {
            log('🧪 开始测试出牌UI')
            
            if (!gameScene) {
                log('❌ 游戏场景未就绪，请先启动游戏')
                return
            }

            try {
                // 测试出牌按钮显示
                if (typeof gameScene.showPlayingButtons === 'function') {
                    log('✅ showPlayingButtons方法存在')
                    gameScene.showPlayingButtons()
                    log('✅ 出牌按钮显示命令已发送')
                    updateStatus('出牌UI测试中')
                } else {
                    log('❌ showPlayingButtons方法不存在')
                }

                // 测试按钮显示更新
                if (typeof gameScene.updateButtonDisplay === 'function') {
                    const mockGameState = {
                        phase: 'playing',
                        isMyTurn: true
                    }
                    
                    gameScene.updateButtonDisplay(mockGameState)
                    log('✅ 出牌阶段按钮更新命令已发送')
                }

            } catch (error) {
                log(`❌ 出牌UI测试失败: ${error.message}`)
            }
        }

        window.destroyGame = function() {
            log('🗑️ 销毁游戏实例')
            
            if (gameInstance) {
                gameInstance.destroy(true)
                gameInstance = null
                gameScene = null
                log('✅ 游戏实例已销毁')
                updateStatus('游戏已销毁')
            } else {
                log('⚠️ 没有游戏实例需要销毁')
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 游戏UI实际测试页面已加载')
            log('点击"启动游戏"开始测试')
        })
    </script>
</body>
</html>
