<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phaser基础测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        #gameContainer {
            width: 800px;
            height: 600px;
            border: 2px solid #FFD700;
            margin: 20px auto;
            background: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Phaser基础功能测试</h1>
        <p>测试Phaser库和基本场景功能</p>
        
        <div class="test-section">
            <h3>测试1: Phaser库检查</h3>
            <button onclick="testPhaserLibrary()">测试Phaser库</button>
            <div id="phaserResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 基本场景创建</h3>
            <button onclick="testBasicScene()">测试基本场景</button>
            <div id="sceneResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: GameMainScene导入</h3>
            <button onclick="testGameMainSceneImport()">测试GameMainScene导入</button>
            <div id="gameMainSceneResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 完整游戏创建</h3>
            <button onclick="testFullGameCreation()">测试完整游戏创建</button>
            <div id="fullGameResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>游戏显示区域</h3>
            <div id="gameContainer"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null

        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.textContent = `${success ? '✅ 通过' : '❌ 失败'}: ${message}`
        }

        // 测试1: Phaser库检查
        window.testPhaserLibrary = function() {
            log('🧪 开始测试Phaser库')
            
            try {
                if (typeof Phaser !== 'undefined') {
                    log(`✅ Phaser库已加载，版本: ${Phaser.VERSION}`)
                    
                    // 检查关键类
                    const requiredClasses = ['Game', 'Scene', 'AUTO', 'Scale']
                    let missingClasses = []
                    
                    requiredClasses.forEach(className => {
                        if (Phaser[className] !== undefined) {
                            log(`✅ Phaser.${className} 存在`)
                        } else {
                            log(`❌ Phaser.${className} 缺失`)
                            missingClasses.push(className)
                        }
                    })
                    
                    if (missingClasses.length === 0) {
                        showResult('phaserResult', true, `Phaser库完整 (v${Phaser.VERSION})`)
                    } else {
                        showResult('phaserResult', false, `Phaser库不完整，缺少: ${missingClasses.join(', ')}`)
                    }
                } else {
                    log('❌ Phaser库未加载')
                    showResult('phaserResult', false, 'Phaser库未加载')
                }
            } catch (error) {
                log(`❌ Phaser库测试失败: ${error.message}`)
                showResult('phaserResult', false, `测试异常: ${error.message}`)
            }
        }

        // 测试2: 基本场景创建
        window.testBasicScene = function() {
            log('🧪 开始测试基本场景创建')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }
                
                // 创建简单场景
                class TestScene extends Phaser.Scene {
                    constructor() {
                        super({ key: 'TestScene' })
                    }
                    
                    preload() {
                        log('✅ TestScene preload 执行')
                    }
                    
                    create() {
                        log('✅ TestScene create 执行')
                        
                        // 创建简单图形
                        const graphics = this.add.graphics()
                        graphics.fillStyle(0x00ff00)
                        graphics.fillRect(100, 100, 200, 100)
                        
                        const text = this.add.text(200, 150, 'Phaser测试成功!', {
                            fontSize: '16px',
                            color: '#ffffff'
                        }).setOrigin(0.5)
                        
                        log('✅ 基本图形和文字创建成功')
                    }
                }
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: TestScene
                }
                
                gameInstance = new Phaser.Game(config)
                log('✅ 基本Phaser游戏实例创建成功')
                showResult('sceneResult', true, '基本场景创建成功')
                
            } catch (error) {
                log(`❌ 基本场景创建失败: ${error.message}`)
                showResult('sceneResult', false, `场景创建失败: ${error.message}`)
            }
        }

        // 测试3: GameMainScene导入
        window.testGameMainSceneImport = async function() {
            log('🧪 开始测试GameMainScene导入')
            
            try {
                const gameModule = await import('./src/game/GameScene.js')
                
                if (gameModule.GameMainScene) {
                    log('✅ GameMainScene类导入成功')
                    
                    // 检查是否是Phaser.Scene的子类
                    const scene = new gameModule.GameMainScene()
                    if (scene instanceof Phaser.Scene) {
                        log('✅ GameMainScene正确继承Phaser.Scene')
                        showResult('gameMainSceneResult', true, 'GameMainScene导入和继承正确')
                    } else {
                        log('❌ GameMainScene未正确继承Phaser.Scene')
                        showResult('gameMainSceneResult', false, 'GameMainScene继承问题')
                    }
                } else {
                    log('❌ GameMainScene类导入失败')
                    showResult('gameMainSceneResult', false, 'GameMainScene类不存在')
                }
                
            } catch (error) {
                log(`❌ GameMainScene导入测试失败: ${error.message}`)
                showResult('gameMainSceneResult', false, `导入失败: ${error.message}`)
            }
        }

        // 测试4: 完整游戏创建
        window.testFullGameCreation = async function() {
            log('🧪 开始测试完整游戏创建')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }
                
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene
                }
                
                gameInstance = new Phaser.Game(config)
                log('✅ 完整游戏实例创建成功')
                
                // 等待场景初始化
                setTimeout(() => {
                    const scene = gameInstance.scene.getScene('GameMainScene')
                    if (scene) {
                        log('✅ GameMainScene场景获取成功')
                        showResult('fullGameResult', true, '完整游戏创建成功')
                    } else {
                        log('❌ GameMainScene场景获取失败')
                        showResult('fullGameResult', false, '场景获取失败')
                    }
                }, 2000)
                
            } catch (error) {
                log(`❌ 完整游戏创建失败: ${error.message}`)
                showResult('fullGameResult', false, `游戏创建失败: ${error.message}`)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎮 Phaser基础测试页面已加载')
            log('请按顺序运行测试：1→2→3→4')
        })
    </script>
</body>
</html>
