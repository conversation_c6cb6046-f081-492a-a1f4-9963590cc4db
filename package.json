{"name": "dou-dizhu-preview", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@microsoft/signalr": "^7.0.0", "antd": "^5.0.0", "mobx": "^6.0.0", "mobx-react-lite": "^4.0.0", "phaser": "^3.60.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "less": "^4.3.0", "vite": "^4.4.0"}}