mv .gitignore.txt .gitignore# 1. 系统概述

本系统是一个集对战、参赛人员管理、比赛场次管理、数据统计等功能于一体的线上比赛选拔平台，聚焦斗地主扑克竞技场景。

## 1.1 技术架构

- ****后端API****：.NET Core

- ****管理后台****：React

- ****游戏前端框架****：Cocos Creator 3.1 和 JavaScript（不要用Typescript，要支持Web端，后期可快速转换为移动端的安卓和IOS APP）

- ****数据库****：MySQL

- ****缓存中间件****：Redis

- ****实时通信****：SignalR

- 前端对接后端提供的接口文档

## 1.2 系统特性

- ****跨平台支持****：手机Web端为主，电脑端采用横屏手机布局

- ****匿名化设计****：所有用户信息以匿名方式展示，用户头像用系统默认的一个头像

- ****扑克定制****：可更换扑克背面图案

## 1.3 核心业务流程

用户登录 → 用户报名 → 等待开始 → 分配房间 → 比赛开始 → 进入比赛 → 每轮切换 → 比赛结束 → 展示排名

# 2. 数据交互接口

## 2.1 与微信小程序对接

用户通过微信小程序报名参加比赛和查看自己的比赛成绩

### 2.1.1 报名资格验证接口

- ****用途****：验证用户是否有资格参加指定赛事

- ****调用时机****：用户点击报名时

### 2.1.2 比赛成绩回传接口

- ****用途****：将比赛结果推送至小程序

- ****数据内容****：成绩分值、排名等信息

- ****传输方式****：实时或异步推送

# 3. 比赛核心规则

## 3.1 轮流首叫规则（叫牌规则）

### 3.1.1 牌局初始化

- 每场比赛包含 `X` 轮，每轮包含 `Y` 局

- 每轮的第1局牌，所有桌都由"西家"玩家首叫

- 从第2局开始，每局首先叫牌的选手按出牌顺序（逆时针）轮流担任

### 3.1.2 叫牌流程

- ****叫牌顺序****：按逆时针顺序轮流进行

- ****叫牌次数****：每位玩家在轮到自己时，只能叫「1次」

- ****叫牌选项****：「1分」、「2分」、「3分」、「不叫」

- ****叫牌规则****：后叫牌者只能叫比前面选手高的分，或者选择「不叫」

- ****时间限制****：叫牌时限15秒（上一家叫牌结束时间为计时开始）

### 3.1.3 地主确定规则

- 如果某位玩家叫「3分」，则立即结束叫牌，该玩家成为地主

- 叫牌结束后，所叫分值最大的玩家成为地主

- 如果所有玩家都选择「不叫」，则本局牌作废，系统重新发牌，并由原定首叫选手重新首叫

### 3.1.4 特殊情况

- 手牌中包含「双王」或「4个2」的玩家，可以选择「不叫分」

- 没有强制叫牌的规则，即没有"必叫"

## 3.2 出牌规则

- ****出牌时限****：20秒（上一家出牌结束时间为计时开始）

- ****炸弹限制****：三炸封顶（地主最多±48分，第四炸及以上，该怎么出牌还怎么出牌，但分数就不再按炸计算了）

## 3.3 计分规则和按瑞士移位机制分配桌和座位

### 3.3.1 首轮比赛随机分配桌号和座位

## 3.3.2 场分计算

每轮 `Y` 局牌比赛结束后，根据当桌所有玩家的小分进行排名，并计算场分：

****标准场分分配规则****：

- 小分第一的选手：积 5 个场分

- 小分第二的选手：积 3 个场分

- 小分第三的选手：积 1 个场分

****特殊情况处理****：

- ****2人并列第一名****：并列第一的两人各积 4 个场分，另一人积 1 个场分

- ****2人并列第二名****：第一积 5 个场分，并列第二的两名选手各积 2 个场分

- ****3人都是0小分****：3人各积 3 个场分

- ****第三名小分低于31.5分****：第三名积 0 场分

### 3.3.3 最终排名确定

- 最终排名由每位选手的 `X` 轮总场分决定，总场分高者排名靠前

- 场分平分情况处理按优先级从高到低执行

### 3.3.4 自第二轮开始，每轮按瑞士移位赛规则，重新分配桌号和座位

### 3.3.5 轮空处理

****触发条件****：若参赛总人数非 3 的倍数，将产生轮空情况

****轮空选手积分****：

- 场分记为 4 分

- 小分为 13.5 分

****轮空分配****：

- ****首轮****：轮空机会赋予排位最后的选手

- ****后续轮次****：自第二轮起，轮空机会则给予当前排名末尾的选手

- ****限制****：每位选手在整个赛事中仅限轮空一次

## 3.4 托管机制

- 如果托管时是自己出牌，则出最小的一张牌

- 如果托管时是别人出牌，则轮到自己时就一直是不出牌

## 3.5 比赛房间的页面布局

****对战座位****：

- 左：西（3人中分数最高的）

- 下：南（3人中分数中间的）

- 右：东（3人中分数最低的）

- 上：比赛信息展示

# 4. 比赛配置与管理

## 4.1 比赛配置项

### 4.1.1 基础配置

- ****比赛名称****：纯文本

- ****报名时间****：开始时间 —— 截止时间（年月日时分，超过此时间，不可再报名）

- ****比赛签到时间****：开始时间 —— 截止时间（年月日时分，超过此时间，可进入比赛但已无法参赛）

- ****比赛开始时间****：年月日时分（超过此时间，不可进入比赛）

### 4.1.2 参赛人数配置

- ****报名最大人数****：非0正整数

- ****开赛最少人数****：⭕️3人 ⭕️6人 ⭕️9人 ⭕️12人 ⭕️15人

### 4.1.3 比赛规则配置

- ****可重复对垒****：⭕️允许 ⭕️禁止

- ****比赛轮数****：整数且＞3（最少3轮牌）

- ****每轮局数****：⭕️3局 ⭕️6局 ⭕️9局（先轮数，再局数）

- ****比赛竞技分****：默认为「无」，即本次比赛的所有小分都是按这个系数进行换算（如设置为100，就是初始分100，然后在此基础上进行加分和减分）

#### 4.1.4 其他配置

- ****比赛规程****：富文本

- ****扑克背面图案****：上传扑克背面图片

## 4.2 术语与定义

- ****本系统****：指「线上智力竞技比赛选拔系统」

- ****小程序****：指现有"金沙体育"微信小程序

- ****场分****：瑞士移位赛中根据每轮小分计算出的积分

- ****小分****：斗地主单局比赛的得分

- ****轮****：瑞士移位赛中的一个比赛阶段，包含多局牌

- ****局****：斗地主比赛的最小单位

# 5. 详细业务流程设计

## 5.1 用户端流程（Cocos游戏前端）

### 5.1.1 微信授权登录页

- 用户点击微信H5链接跳转至授权页

- 用户确认授权后系统获取openID

- 自动检测用户是否已绑定账号
  
   1. 新用户：点击授权头像昵称和unionid（为了和小程序用户信息连通）
  
   2. 老用户：直接进入赛事大厅

### 5.1.2 赛事大厅页

- 展示进行中/可报名的赛事卡片

- 点击赛事卡片显示详情：
  
   1. 赛事名称/时间/规则
  
   2. 当前报名人数/开赛条件

- 点击【立即报名】触发资格校验接口
  
   1. 校验通过：进入"准备就绪"状态
  
   2. 校验失败：显示失败原因，如已报满

### 5.1.3 比赛准备页

- 倒计时显示比赛开始时间

- 实时状态提示：
  
   1. "等待其他玩家加入（已入场人数：32，比赛报名总人数：36）"
  
   2. "比赛将在03:15后自动开始"

### 5.1.4 房间匹配页

- 比赛开始时显示匹配动画

- 成功匹配后显示：
  
   1. 对手匿名信息（玩家A/玩家B）
  
   2. 座位分配（根据历史积分自动排列）
  
   3. 30秒准备倒计时

- 超时未准备自动视为就绪

### 5.1.5 叫牌阶段页

- 动态显示当前叫牌顺序（高亮当前操作者，逆时针）

- 叫牌操作面板：
  
   1. 按钮：1分/2分/3分/不叫
  
   2. 15秒倒计时（每秒更新）

- 特殊状态提示：
  
   1. "您持有双王，可选择不叫分"
  
   2. "玩家C已叫3分，本局结束叫牌"

### 5.1.6 对局进行页

- 牌桌布局：
  
   1. 左（西）：当前最高分玩家
  
   2. 下（南）：中间分玩家
  
   3. 右（东）：最低分玩家

- 核心交互元素：
  
   1. 出牌区显示当前牌型
  
   2. 手牌操作区（20秒倒计时）
  
   3. 托管按钮（点击后按规则自动操作）

- 点炮计数提示：
  
   1. "当前已出现3点（封顶4点）"
  
   2. "第4点不再计分"

### 5.1.7 单局结算页

- 显示本局结果：
  
   1. 胜负标识（地主图标/农民图标）
  
   2. 小分变动明细（基础分+点炮加成）
  
   3. 三方手牌回顾（可滑动查看）

- 提供【准备下一局】按钮

### 5.1.8 轮次结算页

- 轮次结束时弹出：
  
   1. 当前轮次场分排名（匿名显示）
  
   2. 特殊状态提示：
   - "您与玩家B并列第一，各获4场分"
  
   - "玩家C因小分低于31.5，本轮积0分"

- 显示下一轮开始倒计时（默认3分钟）

### 5.1.9 最终排名页

- 总排名展示

- 数据详情：
  
   1. 总场分/小分走势图
  
   2. 最佳单局表现（如"最高单局得分：58分"）

- 提供【分享战绩】按钮

## 5.2 管理后台流程（React）

### 5.2.1 赛事配置页

- 基础配置表单：
  
   1. 比赛名称/时间窗口
  
   2. 人数限制（开赛最少人数设置）
  
   3. 瑞士移位轮数/每轮局数

- 高级规则配置：
  
   1. 允许重复对战开关
  
   2. 轮空分配策略选择

### 5.2.2 实时监控页

- 全局状态看板：
  
   1. 进行中房间数/异常房间报警
  
   2. 当前最高场分玩家（匿名显示）

- 房间详情获取：
  
   1. 查看任意房间的实时牌局
  
   2. 强制干预功能（如重置倒计时）

### 5.2.3 数据统计页

- 多维度分析模块：
  
   1. 玩家留存率（按轮次统计）
  
   2. 平均对局时长分布图

- 异常行为检测：
  
   1. 高弃权玩家列表
  
   2. 疑似作弊对局标记

## 5.3 关键状态流转逻辑

### 5.3.1 叫牌超时处理

- 超时自动选择"不叫"

- 记录超时次数，多次超时可能触发托管

### 5.3.2 瑞士移位匹配逻辑

- 根据当前积分进行蛇形分组

- 避免重复对战（如配置禁止）

- 确保每轮匹配的公平性

### 5.3.3 轮空分配逻辑

- 优先分配给当前排名最低的选手

- 确保每位选手最多轮空一次

- 轮空选手获得固定积分（4场分，13.5小分）

## 5.4 系统设计原则

该设计严格遵循PRD中的业务规则，确保：

1. ****匿名化展示****：符合隐私要求，所有用户信息匿名显示

2. ****瑞士移位算法****：准确实现蛇形分组与冲突规避

3. ****特殊计分场景****：并列名次等情况的界面明确提示

4. ****实时同步****：通过SignalR保证系统状态变更的低延迟同步

5. ****品牌定制****：支持甲方品牌和LOGO展示

6. ****跨平台适配****：确保在不同设备上的良好体验

## 5.5 重新进入场景处理

### 5.5.1 报名阶段中断的处理逻辑

- 用户重新进入时自动检查赛事状态：
  
   1. 若赛事未开始且未满员：保留原有报名席位
  
   2. 若赛事已满员：进入候补队列（需配置最大等待人数）

- 页面提示相应状态信息

### 5.5.2 比赛进行中中断，对局阶段恢复

- 重新连接时优先读取Redis房间状态：
  
   1. 恢复当前手牌信息、出牌顺序
  
   2. 同步其他玩家最新操作记录

- 倒计时补差机制：
  
   1. 若中断时间≤30秒：继续原有倒计时
  
   2. 若中断时间＞30秒：重置为10秒紧急操作时间

##### 轮次间隔恢复

- 显示中断期间错过的信息：
  
   1. 上轮场分变动明细
  
   2. 最新瑞士移位匹配结果

- 提供历史回放功能：
  
   1. 可查看已完成的局数详情
  
   2. 禁止操作过往牌局

# 6. 测试策略

- ****单元测试****：核心游戏逻辑和计分算法

- ****集成测试****：各模块间的数据交互

- ****压力测试****：高并发场景下的系统稳定性

- ****用户体验测试****：界面操作和游戏流程的流畅性
