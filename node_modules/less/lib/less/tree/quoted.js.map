{"version": 3, "file": "quoted.js", "sourceRoot": "", "sources": ["../../../src/less/tree/quoted.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAClC,gEAAkC;AAElC,IAAM,MAAM,GAAG,UAAS,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe;IACjE,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;IACxD,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,EAAE,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC;IACtC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACnC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AAC7B,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACzC,IAAI,EAAE,QAAQ;IAEd,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC5D;QACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;IACL,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAM,mBAAmB,GAAG,UAAU,CAAC,EAAE,KAAK,EAAE,KAAK;YACjD,IAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,WAAI,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,KAAK,CAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnG,OAAO,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACvD,CAAC,CAAC;QACF,IAAM,mBAAmB,GAAG,UAAU,CAAC,EAAE,KAAK,EAAE,KAAK;YACjD,IAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,WAAI,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,KAAK,CAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnG,OAAO,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACvD,CAAC,CAAC;QACF,SAAS,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc;YACnD,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,GAAG;gBACC,KAAK,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAClC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;aAC1D,QAAQ,KAAK,KAAK,cAAc,EAAE;YACnC,OAAO,cAAc,CAAC;QAC1B,CAAC;QACD,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;QACzE,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;QACrE,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9G,CAAC;IAED,OAAO,YAAC,KAAK;QACT,0DAA0D;QAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YAC5D,OAAO,cAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SACvD;aAAM;YACH,OAAO,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SACxE;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Property from './property';\n\nconst Quoted = function(str, content, escaped, index, currentFileInfo) {\n    this.escaped = (escaped === undefined) ? true : escaped;\n    this.value = content || '';\n    this.quote = str.charAt(0);\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.variableRegex = /@\\{([\\w-]+)\\}/g;\n    this.propRegex = /\\$\\{([\\w-]+)\\}/g;\n    this.allowRoot = escaped;\n};\n\nQuoted.prototype = Object.assign(new Node(), {\n    type: 'Quoted',\n\n    genCSS(context, output) {\n        if (!this.escaped) {\n            output.add(this.quote, this.fileInfo(), this.getIndex());\n        }\n        output.add(this.value);\n        if (!this.escaped) {\n            output.add(this.quote);\n        }\n    },\n\n    containsVariables() {\n        return this.value.match(this.variableRegex);\n    },\n\n    eval(context) {\n        const that = this;\n        let value = this.value;\n        const variableReplacement = function (_, name1, name2) {\n            const v = new Variable(`@${name1 ?? name2}`, that.getIndex(), that.fileInfo()).eval(context, true);\n            return (v instanceof Quoted) ? v.value : v.toCSS();\n        };\n        const propertyReplacement = function (_, name1, name2) {\n            const v = new Property(`$${name1 ?? name2}`, that.getIndex(), that.fileInfo()).eval(context, true);\n            return (v instanceof Quoted) ? v.value : v.toCSS();\n        };\n        function iterativeReplace(value, regexp, replacementFnc) {\n            let evaluatedValue = value;\n            do {\n                value = evaluatedValue.toString();\n                evaluatedValue = value.replace(regexp, replacementFnc);\n            } while (value !== evaluatedValue);\n            return evaluatedValue;\n        }\n        value = iterativeReplace(value, this.variableRegex, variableReplacement);\n        value = iterativeReplace(value, this.propRegex, propertyReplacement);\n        return new Quoted(this.quote + value + this.quote, value, this.escaped, this.getIndex(), this.fileInfo());\n    },\n\n    compare(other) {\n        // when comparing quoted strings allow the quote to differ\n        if (other.type === 'Quoted' && !this.escaped && !other.escaped) {\n            return Node.numericCompare(this.value, other.value);\n        } else {\n            return other.toCSS && this.toCSS() === other.toCSS() ? 0 : undefined;\n        }\n    }\n});\n\nexport default Quoted;\n"]}