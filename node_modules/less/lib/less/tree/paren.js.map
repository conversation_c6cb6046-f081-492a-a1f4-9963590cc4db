{"version": 3, "file": "paren.js", "sourceRoot": "", "sources": ["../../../src/less/tree/paren.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,KAAK,GAAG,UAAS,IAAI;IACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACxC,IAAI,EAAE,OAAO;IAEb,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/C,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,KAAK,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Paren = function(node) {\n    this.value = node;\n};\n\nParen.prototype = Object.assign(new Node(), {\n    type: 'Paren',\n\n    genCSS(context, output) {\n        output.add('(');\n        this.value.genCSS(context, output);\n        output.add(')');\n    },\n\n    eval(context) {\n        return new Paren(this.value.eval(context));\n    }\n});\n\nexport default Paren;\n"]}