{"version": 3, "file": "negative.js", "sourceRoot": "", "sources": ["../../../src/less/tree/negative.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,kEAAoC;AACpC,kEAAoC;AAEpC,IAAM,QAAQ,GAAG,UAAS,IAAI;IAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC3C,IAAI,EAAE,UAAU;IAEhB,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;YACpB,OAAO,CAAC,IAAI,mBAAS,CAAC,GAAG,EAAE,CAAC,IAAI,mBAAS,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9E;QACD,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAClD,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Operation from './operation';\nimport Dimension from './dimension';\n\nconst Negative = function(node) {\n    this.value = node;\n};\n\nNegative.prototype = Object.assign(new Node(), {\n    type: 'Negative',\n\n    genCSS(context, output) {\n        output.add('-');\n        this.value.genCSS(context, output);\n    },\n\n    eval(context) {\n        if (context.isMathOn()) {\n            return (new Operation('*', [new Dimension(-1), this.value])).eval(context);\n        }\n        return new Negative(this.value.eval(context));\n    }\n});\n\nexport default Negative;\n"]}