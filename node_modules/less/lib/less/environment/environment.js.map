{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../../src/less/environment/environment.js"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,6DAA+B;AAE/B;IACI,qBAAY,mBAAmB,EAAE,YAAY;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,EAAE,CAAC;QACvC,mBAAmB,GAAG,mBAAmB,IAAI,EAAE,CAAC;QAEhD,IAAM,iBAAiB,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,CAAC,CAAC;QACnG,IAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,IAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAE9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAM,eAAe,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAC9D;iBAAM,IAAI,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE;gBACrC,IAAI,CAAC,IAAI,CAAC,qDAA8C,QAAQ,CAAE,CAAC,CAAC;aACvE;SACJ;IACL,CAAC;IAED,oCAAc,GAAd,UAAe,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM;QAEnE,IAAI,CAAC,QAAQ,EAAE;YACX,gBAAM,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;SACjG;QACD,IAAI,gBAAgB,KAAK,SAAS,EAAE;YAChC,gBAAM,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;SACpG;QAED,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,YAAY,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC;SAC1F;QACD,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAG,CAAC,EAAE,EAAE;YAChD,IAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE;gBACrG,OAAO,WAAW,CAAC;aACtB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,oCAAc,GAAd,UAAe,WAAW;QACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IAED,uCAAiB,GAAjB;QACI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3B,CAAC;IACL,kBAAC;AAAD,CAAC,AAjDD,IAiDC;AAED,kBAAe,WAAW,CAAC", "sourcesContent": ["/**\n * @todo Document why this abstraction exists, and the relationship between\n *       environment, file managers, and plugin manager\n */\n\nimport logger from '../logger';\n\nclass Environment {\n    constructor(externalEnvironment, fileManagers) {\n        this.fileManagers = fileManagers || [];\n        externalEnvironment = externalEnvironment || {};\n\n        const optionalFunctions = ['encodeBase64', 'mimeLookup', 'charsetLookup', 'getSourceMapGenerator'];\n        const requiredFunctions = [];\n        const functions = requiredFunctions.concat(optionalFunctions);\n\n        for (let i = 0; i < functions.length; i++) {\n            const propName = functions[i];\n            const environmentFunc = externalEnvironment[propName];\n            if (environmentFunc) {\n                this[propName] = environmentFunc.bind(externalEnvironment);\n            } else if (i < requiredFunctions.length) {\n                this.warn(`missing required function in environment - ${propName}`);\n            }\n        }\n    }\n\n    getFileManager(filename, currentDirectory, options, environment, isSync) {\n\n        if (!filename) {\n            logger.warn('getFileManager called with no filename.. Please report this issue. continuing.');\n        }\n        if (currentDirectory === undefined) {\n            logger.warn('getFileManager called with null directory.. Please report this issue. continuing.');\n        }\n\n        let fileManagers = this.fileManagers;\n        if (options.pluginManager) {\n            fileManagers = [].concat(fileManagers).concat(options.pluginManager.getFileManagers());\n        }\n        for (let i = fileManagers.length - 1; i >= 0 ; i--) {\n            const fileManager = fileManagers[i];\n            if (fileManager[isSync ? 'supportsSync' : 'supports'](filename, currentDirectory, options, environment)) {\n                return fileManager;\n            }\n        }\n        return null;\n    }\n\n    addFileManager(fileManager) {\n        this.fileManagers.push(fileManager);\n    }\n\n    clearFileManagers() {\n        this.fileManagers = [];\n    }\n}\n\nexport default Environment;\n"]}