{"version": 3, "file": "abstract-file-manager.js", "sourceRoot": "", "sources": ["../../../src/less/environment/abstract-file-manager.js"], "names": [], "mappings": ";;AAAA;IAAA;IAyIA,CAAC;IAxIG,qCAAO,GAAP,UAAQ,QAAQ;QACZ,IAAI,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,EAAE;YACP,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnC;QACD,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,EAAE;YACP,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,GAAG,CAAC,EAAE;YACP,OAAO,EAAE,CAAC;SACb;QACD,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,gDAAkB,GAAlB,UAAmB,IAAI,EAAE,GAAG;QACxB,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;IAClE,CAAC;IAED,oDAAsB,GAAtB,UAAuB,IAAI;QACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,0CAAY,GAAZ;QACI,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,qDAAuB,GAAvB;QACI,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,4CAAc,GAAd,UAAe,QAAQ;QACnB,OAAO,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED,4BAA4B;IAC5B,kCAAI,GAAJ,UAAK,QAAQ,EAAE,SAAS;QACpB,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,SAAS,CAAC;SACpB;QACD,OAAO,QAAQ,GAAG,SAAS,CAAC;IAChC,CAAC;IAED,sCAAQ,GAAR,UAAS,GAAG,EAAE,OAAO;QACjB,mDAAmD;QAEnD,IAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,CAAC;QACN,IAAI,GAAG,CAAC;QACR,IAAI,cAAc,CAAC;QACnB,IAAI,kBAAkB,CAAC;QACvB,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,QAAQ,CAAC,QAAQ,KAAK,YAAY,CAAC,QAAQ,EAAE;YAC7C,OAAO,EAAE,CAAC;SACb;QACD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC7E,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACtB,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBAAE,MAAM;aAAE;SAC1E;QACD,kBAAkB,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvD,cAAc,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,IAAI,KAAK,CAAC;SACjB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,IAAI,UAAG,cAAc,CAAC,CAAC,CAAC,MAAG,CAAC;SACnC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,6CAAe,GAAf,UAAgB,GAAG,EAAE,OAAO;QACxB,0CAA0C;QAC1C,gDAAgD;QAChD,4BAA4B;QAC5B,yBAAyB;QACzB,2BAA2B;QAE3B,IAAM,aAAa,GAAG,wFAAwF,CAAC;QAE/G,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAC1C,IAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAM,WAAW,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,CAAC;QACN,IAAI,YAAY,CAAC;QAEjB,IAAI,CAAC,QAAQ,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,wCAAiC,GAAG,MAAG,CAAC,CAAC;SAC5D;QAED,sDAAsD;QACtD,IAAI,OAAO,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1C,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,YAAY,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,sCAA+B,OAAO,MAAG,CAAC,CAAC;aAC9D;YACD,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACd,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC/C;SACJ;QAED,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;YACb,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE5D,6BAA6B;YAC7B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAExC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBAC5B,WAAW,CAAC,GAAG,EAAE,CAAC;iBACrB;qBACI,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAChC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;iBACvC;aAEJ;SACJ;QAED,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;QACnC,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClE,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5D,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACvD,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACtD,OAAO,QAAQ,CAAC;IACpB,CAAC;IACL,0BAAC;AAAD,CAAC,AAzID,IAyIC;AAED,kBAAe,mBAAmB,CAAC", "sourcesContent": ["class AbstractFileManager {\n    getPath(filename) {\n        let j = filename.lastIndexOf('?');\n        if (j > 0) {\n            filename = filename.slice(0, j);\n        }\n        j = filename.lastIndexOf('/');\n        if (j < 0) {\n            j = filename.lastIndexOf('\\\\');\n        }\n        if (j < 0) {\n            return '';\n        }\n        return filename.slice(0, j + 1);\n    }\n\n    tryAppendExtension(path, ext) {\n        return /(\\.[a-z]*$)|([?;].*)$/.test(path) ? path : path + ext;\n    }\n\n    tryAppendLessExtension(path) {\n        return this.tryAppendExtension(path, '.less');\n    }\n\n    supportsSync() {\n        return false;\n    }\n\n    alwaysMakePathsAbsolute() {\n        return false;\n    }\n\n    isPathAbsolute(filename) {\n        return (/^(?:[a-z-]+:|\\/|\\\\|#)/i).test(filename);\n    }\n\n    // TODO: pull out / replace?\n    join(basePath, laterPath) {\n        if (!basePath) {\n            return laterPath;\n        }\n        return basePath + laterPath;\n    }\n\n    pathDiff(url, baseUrl) {\n        // diff between two paths to create a relative path\n\n        const urlParts = this.extractUrlParts(url);\n\n        const baseUrlParts = this.extractUrlParts(baseUrl);\n        let i;\n        let max;\n        let urlDirectories;\n        let baseUrlDirectories;\n        let diff = '';\n        if (urlParts.hostPart !== baseUrlParts.hostPart) {\n            return '';\n        }\n        max = Math.max(baseUrlParts.directories.length, urlParts.directories.length);\n        for (i = 0; i < max; i++) {\n            if (baseUrlParts.directories[i] !== urlParts.directories[i]) { break; }\n        }\n        baseUrlDirectories = baseUrlParts.directories.slice(i);\n        urlDirectories = urlParts.directories.slice(i);\n        for (i = 0; i < baseUrlDirectories.length - 1; i++) {\n            diff += '../';\n        }\n        for (i = 0; i < urlDirectories.length - 1; i++) {\n            diff += `${urlDirectories[i]}/`;\n        }\n        return diff;\n    }\n\n    /**\n     * Helper function, not part of API.\n     * This should be replaceable by newer Node / Browser APIs\n     * \n     * @param {string} url \n     * @param {string} baseUrl\n     */\n    extractUrlParts(url, baseUrl) {\n        // urlParts[1] = protocol://hostname/ OR /\n        // urlParts[2] = / if path relative to host base\n        // urlParts[3] = directories\n        // urlParts[4] = filename\n        // urlParts[5] = parameters\n\n        const urlPartsRegex = /^((?:[a-z-]+:)?\\/{2}(?:[^/?#]*\\/)|([/\\\\]))?((?:[^/\\\\?#]*[/\\\\])*)([^/\\\\?#]*)([#?].*)?$/i;\n\n        const urlParts = url.match(urlPartsRegex);\n        const returner = {};\n        let rawDirectories = [];\n        const directories = [];\n        let i;\n        let baseUrlParts;\n\n        if (!urlParts) {\n            throw new Error(`Could not parse sheet href - '${url}'`);\n        }\n\n        // Stylesheets in IE don't always return the full path\n        if (baseUrl && (!urlParts[1] || urlParts[2])) {\n            baseUrlParts = baseUrl.match(urlPartsRegex);\n            if (!baseUrlParts) {\n                throw new Error(`Could not parse page url - '${baseUrl}'`);\n            }\n            urlParts[1] = urlParts[1] || baseUrlParts[1] || '';\n            if (!urlParts[2]) {\n                urlParts[3] = baseUrlParts[3] + urlParts[3];\n            }\n        }\n\n        if (urlParts[3]) {\n            rawDirectories = urlParts[3].replace(/\\\\/g, '/').split('/');\n\n            // collapse '..' and skip '.'\n            for (i = 0; i < rawDirectories.length; i++) {\n\n                if (rawDirectories[i] === '..') {\n                    directories.pop();\n                }\n                else if (rawDirectories[i] !== '.') {\n                    directories.push(rawDirectories[i]);\n                }\n            \n            }\n        }\n\n        returner.hostPart = urlParts[1];\n        returner.directories = directories;\n        returner.rawPath = (urlParts[1] || '') + rawDirectories.join('/');\n        returner.path = (urlParts[1] || '') + directories.join('/');\n        returner.filename = urlParts[4];\n        returner.fileUrl = returner.path + (urlParts[4] || '');\n        returner.url = returner.fileUrl + (urlParts[5] || '');\n        return returner;\n    }\n}\n\nexport default AbstractFileManager;\n"]}