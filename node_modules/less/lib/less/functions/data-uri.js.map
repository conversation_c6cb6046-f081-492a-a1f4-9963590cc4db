{"version": 3, "file": "data-uri.js", "sourceRoot": "", "sources": ["../../../src/less/functions/data-uri.js"], "names": [], "mappings": ";;;AAAA,kEAAoC;AACpC,4DAA8B;AAC9B,sDAAkC;AAClC,6DAA+B;AAE/B,mBAAe,UAAA,WAAW;IAEtB,IAAM,QAAQ,GAAG,UAAC,YAAY,EAAE,IAAI,IAAK,OAAA,IAAI,aAAG,CAAC,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAA1F,CAA0F,CAAC;IAEpI,OAAO,EAAE,UAAU,EAAE,UAAS,YAAY,EAAE,YAAY;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,YAAY,GAAG,YAAY,CAAC;gBAC5B,YAAY,GAAG,IAAI,CAAC;aACvB;YAED,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,KAAK,CAAC;YAClD,IAAI,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;YAClC,IAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,IAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;gBAClD,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC;YAEjE,IAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;gBACtB,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBACzC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;aAC/C;YACD,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YAEzB,IAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAEvG,IAAI,CAAC,WAAW,EAAE;gBACd,OAAO,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aACvC;YAED,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,mCAAmC;YACnC,IAAI,CAAC,YAAY,EAAE;gBAEf,QAAQ,GAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAE5C,IAAI,QAAQ,KAAK,eAAe,EAAE;oBAC9B,SAAS,GAAG,KAAK,CAAC;iBACrB;qBAAM;oBACH,mDAAmD;oBACnD,IAAM,OAAO,GAAG,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACpD,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;iBAC1D;gBACD,IAAI,SAAS,EAAE;oBAAE,QAAQ,IAAI,SAAS,CAAC;iBAAE;aAC5C;iBACI;gBACD,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACzC;YAED,IAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YAC5F,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACpB,gBAAM,CAAC,IAAI,CAAC,wCAAiC,QAAQ,4BAAyB,CAAC,CAAC;gBAChF,OAAO,QAAQ,CAAC,IAAI,EAAE,YAAY,IAAI,YAAY,CAAC,CAAC;aACvD;YACD,IAAI,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAC5B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE;gBACxC,OAAO,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aACvC;YAED,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAE1E,IAAM,GAAG,GAAG,eAAQ,QAAQ,cAAI,GAAG,SAAG,QAAQ,CAAE,CAAC;YAEjD,OAAO,IAAI,aAAG,CAAC,IAAI,gBAAM,CAAC,YAAI,GAAG,OAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3H,CAAC,EAAC,CAAC;AACP,CAAC,EAAC", "sourcesContent": ["import Quoted from '../tree/quoted';\nimport URL from '../tree/url';\nimport * as utils from '../utils';\nimport logger from '../logger';\n\nexport default environment => {\n    \n    const fallback = (functionThis, node) => new URL(node, functionThis.index, functionThis.currentFileInfo).eval(functionThis.context);    \n\n    return { 'data-uri': function(mimetypeNode, filePathNode) {\n\n        if (!filePathNode) {\n            filePathNode = mimetypeNode;\n            mimetypeNode = null;\n        }\n\n        let mimetype = mimetypeNode && mimetypeNode.value;\n        let filePath = filePathNode.value;\n        const currentFileInfo = this.currentFileInfo;\n        const currentDirectory = currentFileInfo.rewriteUrls ?\n            currentFileInfo.currentDirectory : currentFileInfo.entryPath;\n\n        const fragmentStart = filePath.indexOf('#');\n        let fragment = '';\n        if (fragmentStart !== -1) {\n            fragment = filePath.slice(fragmentStart);\n            filePath = filePath.slice(0, fragmentStart);\n        }\n        const context = utils.clone(this.context);\n        context.rawBuffer = true;\n\n        const fileManager = environment.getFileManager(filePath, currentDirectory, context, environment, true);\n\n        if (!fileManager) {\n            return fallback(this, filePathNode);\n        }\n\n        let useBase64 = false;\n\n        // detect the mimetype if not given\n        if (!mimetypeNode) {\n\n            mimetype = environment.mimeLookup(filePath);\n\n            if (mimetype === 'image/svg+xml') {\n                useBase64 = false;\n            } else {\n                // use base 64 unless it's an ASCII or UTF-8 format\n                const charset = environment.charsetLookup(mimetype);\n                useBase64 = ['US-ASCII', 'UTF-8'].indexOf(charset) < 0;\n            }\n            if (useBase64) { mimetype += ';base64'; }\n        }\n        else {\n            useBase64 = /;base64$/.test(mimetype);\n        }\n\n        const fileSync = fileManager.loadFileSync(filePath, currentDirectory, context, environment);\n        if (!fileSync.contents) {\n            logger.warn(`Skipped data-uri embedding of ${filePath} because file not found`);\n            return fallback(this, filePathNode || mimetypeNode);\n        }\n        let buf = fileSync.contents;\n        if (useBase64 && !environment.encodeBase64) {\n            return fallback(this, filePathNode);\n        }\n\n        buf = useBase64 ? environment.encodeBase64(buf) : encodeURIComponent(buf);\n\n        const uri = `data:${mimetype},${buf}${fragment}`;\n\n        return new URL(new Quoted(`\"${uri}\"`, uri, false, this.index, this.currentFileInfo), this.index, this.currentFileInfo);\n    }};\n};\n"]}