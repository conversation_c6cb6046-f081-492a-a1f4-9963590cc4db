{"version": 3, "file": "list.js", "sourceRoot": "", "sources": ["../../../src/less/functions/list.js"], "names": [], "mappings": ";;;AAAA,oEAAsC;AACtC,8DAAgC;AAChC,wEAA0C;AAC1C,4EAA8C;AAC9C,0EAA4C;AAC5C,oEAAsC;AACtC,sEAAwC;AACxC,oEAAsC;AACtC,kEAAmC;AACnC,gEAAkC;AAElC,IAAM,gBAAgB,GAAG,UAAA,IAAI;IACzB,kDAAkD;IAClD,yCAAyC;IACzC,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE7B,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,kBAAe;IACX,KAAK,EAAE,UAAS,CAAC;QACb,OAAO,CAAC,CAAC;IACb,CAAC;IACD,GAAG,EAAE;QAAS,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACjB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;QACD,OAAO,IAAI,eAAK,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,EAAE,UAAS,MAAM,EAAE,KAAK;QAC3B,kBAAkB;QAClB,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAExB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IACD,MAAM,EAAE,UAAS,MAAM;QACnB,OAAO,IAAI,mBAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD;;;;;;;OAOG;IACH,KAAK,EAAE,UAAS,KAAK,EAAE,GAAG,EAAE,IAAI;QAC5B,IAAI,IAAI,CAAC;QACT,IAAI,EAAE,CAAC;QACP,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,GAAG,EAAE;YACL,EAAE,GAAG,GAAG,CAAC;YACT,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;YACnB,IAAI,IAAI,EAAE;gBACN,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;aAC1B;SACJ;aACI;YACD,IAAI,GAAG,CAAC,CAAC;YACT,EAAE,GAAG,KAAK,CAAC;SACd;QAED,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,SAAS,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;SACxC;QAED,OAAO,IAAI,oBAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IACD,IAAI,EAAE,UAAS,IAAI,EAAE,EAAE;QAAjB,iBAsFL;QArFG,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,CAAC;QACb,IAAI,QAAQ,CAAC;QAEb,IAAM,OAAO,GAAG,UAAA,GAAG;YACf,IAAI,GAAG,YAAY,cAAI,EAAE;gBACrB,OAAO,GAAG,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;aACjC;YACD,OAAO,GAAG,CAAC;QACf,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,YAAY,gBAAK,CAAC,EAAE;YACxC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC3B,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACtC;iBAAM;gBACH,QAAQ,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACpC;SACJ;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACrB,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;SAC1C;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACtC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC5B,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAChC;aAAM;YACH,QAAQ,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9B;QAED,IAAI,SAAS,GAAG,QAAQ,CAAC;QACzB,IAAI,OAAO,GAAG,MAAM,CAAC;QACrB,IAAI,SAAS,GAAG,QAAQ,CAAC;QAEzB,IAAI,EAAE,CAAC,MAAM,EAAE;YACX,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC9C,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC9C,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;SACjB;aAAM;YACH,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC;SACnB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,GAAG,SAAA,CAAC;YACR,IAAI,KAAK,SAAA,CAAC;YACV,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,YAAY,qBAAW,EAAE;gBAC7B,GAAG,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACrE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;aACtB;iBAAM;gBACH,GAAG,GAAG,IAAI,mBAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3B,KAAK,GAAG,IAAI,CAAC;aAChB;YAED,IAAI,IAAI,YAAY,iBAAO,EAAE;gBACzB,SAAS;aACZ;YAED,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,SAAS,EAAE;gBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,qBAAW,CAAC,SAAS,EACnC,KAAK,EACL,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;aACxD;YACD,IAAI,SAAS,EAAE;gBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,qBAAW,CAAC,SAAS,EACnC,IAAI,mBAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EACpB,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;aACxD;YACD,IAAI,OAAO,EAAE;gBACT,QAAQ,CAAC,IAAI,CAAC,IAAI,qBAAW,CAAC,OAAO,EACjC,GAAG,EACH,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;aACxD;YAED,KAAK,CAAC,IAAI,CAAC,IAAI,iBAAO,CAAC,CAAE,IAAG,CAAC,kBAAQ,CAAC,CAAC,CAAE,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAE,CAAC,CAAE,EAC9D,QAAQ,EACR,EAAE,CAAC,aAAa,EAChB,EAAE,CAAC,cAAc,EAAE,CACtB,CAAC,CAAC;SACN;QAED,OAAO,IAAI,iBAAO,CAAC,CAAE,IAAG,CAAC,kBAAQ,CAAC,CAAC,CAAE,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAE,CAAC,CAAE,EAC1D,KAAK,EACL,EAAE,CAAC,aAAa,EAChB,EAAE,CAAC,cAAc,EAAE,CACtB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;CACJ,CAAC", "sourcesContent": ["import Comment from '../tree/comment';\nimport Node from '../tree/node';\nimport Dimension from '../tree/dimension';\nimport Declaration from '../tree/declaration';\nimport Expression from '../tree/expression';\nimport Ruleset from '../tree/ruleset';\nimport Selector from '../tree/selector';\nimport Element from '../tree/element';\nimport Quote from '../tree/quoted';\nimport Value from '../tree/value';\n\nconst getItemsFromNode = node => {\n    // handle non-array values as an array of length 1\n    // return 'undefined' if index is invalid\n    const items = Array.isArray(node.value) ?\n        node.value : Array(node);\n\n    return items;\n};\n\nexport default {\n    _SELF: function(n) {\n        return n;\n    },\n    '~': function(...expr) {\n        if (expr.length === 1) {\n            return expr[0];\n        }\n        return new Value(expr);\n    },\n    extract: function(values, index) {\n        // (1-based index)\n        index = index.value - 1;\n\n        return getItemsFromNode(values)[index];\n    },\n    length: function(values) {\n        return new Dimension(getItemsFromNode(values).length);\n    },\n    /**\n     * Creates a Less list of incremental values.\n     * Modeled after <PERSON>das<PERSON>'s range function, also exists natively in PHP\n     * \n     * @param {Dimension} [start=1]\n     * @param {Dimension} end  - e.g. 10 or 10px - unit is added to output\n     * @param {Dimension} [step=1] \n     */\n    range: function(start, end, step) {\n        let from;\n        let to;\n        let stepValue = 1;\n        const list = [];\n        if (end) {\n            to = end;\n            from = start.value;\n            if (step) {\n                stepValue = step.value;\n            }\n        }\n        else {\n            from = 1;\n            to = start;\n        }\n\n        for (let i = from; i <= to.value; i += stepValue) {\n            list.push(new Dimension(i, to.unit));\n        }\n\n        return new Expression(list);\n    },\n    each: function(list, rs) {\n        const rules = [];\n        let newRules;\n        let iterator;\n\n        const tryEval = val => {\n            if (val instanceof Node) {\n                return val.eval(this.context);\n            }\n            return val;\n        };\n\n        if (list.value && !(list instanceof Quote)) {\n            if (Array.isArray(list.value)) {\n                iterator = list.value.map(tryEval);\n            } else {\n                iterator = [tryEval(list.value)];\n            }\n        } else if (list.ruleset) {\n            iterator = tryEval(list.ruleset).rules;\n        } else if (list.rules) {\n            iterator = list.rules.map(tryEval);\n        } else if (Array.isArray(list)) {\n            iterator = list.map(tryEval);\n        } else {\n            iterator = [tryEval(list)];\n        }\n\n        let valueName = '@value';\n        let keyName = '@key';\n        let indexName = '@index';\n\n        if (rs.params) {\n            valueName = rs.params[0] && rs.params[0].name;\n            keyName = rs.params[1] && rs.params[1].name;\n            indexName = rs.params[2] && rs.params[2].name;\n            rs = rs.rules;\n        } else {\n            rs = rs.ruleset;\n        }\n\n        for (let i = 0; i < iterator.length; i++) {\n            let key;\n            let value;\n            const item = iterator[i];\n            if (item instanceof Declaration) {\n                key = typeof item.name === 'string' ? item.name : item.name[0].value;\n                value = item.value;\n            } else {\n                key = new Dimension(i + 1);\n                value = item;\n            }\n\n            if (item instanceof Comment) {\n                continue;\n            }\n\n            newRules = rs.rules.slice(0);\n            if (valueName) {\n                newRules.push(new Declaration(valueName,\n                    value,\n                    false, false, this.index, this.currentFileInfo));\n            }\n            if (indexName) {\n                newRules.push(new Declaration(indexName,\n                    new Dimension(i + 1),\n                    false, false, this.index, this.currentFileInfo));\n            }\n            if (keyName) {\n                newRules.push(new Declaration(keyName,\n                    key,\n                    false, false, this.index, this.currentFileInfo));\n            }\n\n            rules.push(new Ruleset([ new(Selector)([ new Element('', '&') ]) ],\n                newRules,\n                rs.strictImports,\n                rs.visibilityInfo()\n            ));\n        }\n\n        return new Ruleset([ new(Selector)([ new Element('', '&') ]) ],\n            rules,\n            rs.strictImports,\n            rs.visibilityInfo()\n        ).eval(this.context);\n    }\n};\n"]}