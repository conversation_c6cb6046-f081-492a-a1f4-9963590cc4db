<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>斗地主游戏完整回归测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        .game-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background: #2196f3; color: white; }
        .btn-success { background: #4caf50; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .status-pass { color: #4caf50; }
        .status-fail { color: #f44336; }
        .status-warn { color: #ff9800; }
        .test-item {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #ddd;
        }
        .test-item.pass { border-left-color: #4caf50; background: #f1f8e9; }
        .test-item.fail { border-left-color: #f44336; background: #ffebee; }
        .test-item.warn { border-left-color: #ff9800; background: #fff3e0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-panel">
            <h1>🧪 斗地主游戏完整回归测试</h1>
            <p>这个测试页面将系统性地检查游戏的所有功能模块。</p>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startRegressionTest()">开始回归测试</button>
                <button class="btn btn-success" onclick="testBiddingSystem()">测试叫牌系统</button>
                <button class="btn btn-warning" onclick="testPlayingSystem()">测试出牌系统</button>
                <button class="btn btn-danger" onclick="clearResults()">清空结果</button>
            </div>
            
            <div id="test-summary" style="margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 4px;">
                <strong>测试状态:</strong> <span id="test-status">等待开始</span>
            </div>
            
            <div class="test-results" id="test-results">
                等待测试开始...
            </div>
        </div>
        
        <div class="game-container">
            <div id="game"></div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let testResults = []
        let currentGame = null

        // 测试结果记录
        function logTest(category, name, status, message, details = '') {
            const result = {
                category,
                name,
                status,
                message,
                details,
                timestamp: new Date().toISOString()
            }
            testResults.push(result)
            
            const resultsDiv = document.getElementById('test-results')
            const statusClass = status.toLowerCase()
            const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'
            
            const testItem = document.createElement('div')
            testItem.className = `test-item ${statusClass}`
            testItem.innerHTML = `
                <strong>${statusIcon} [${category}] ${name}</strong><br>
                <span class="status-${statusClass}">${status}: ${message}</span>
                ${details ? `<br><small>详情: ${details}</small>` : ''}
            `
            
            resultsDiv.appendChild(testItem)
            resultsDiv.scrollTop = resultsDiv.scrollHeight
            
            updateTestSummary()
        }

        // 更新测试摘要
        function updateTestSummary() {
            const passCount = testResults.filter(r => r.status === 'PASS').length
            const failCount = testResults.filter(r => r.status === 'FAIL').length
            const warnCount = testResults.filter(r => r.status === 'WARN').length
            const totalCount = testResults.length
            
            document.getElementById('test-status').innerHTML = 
                `总计: ${totalCount} | ✅ ${passCount} | ❌ ${failCount} | ⚠️ ${warnCount}`
        }

        // 清空测试结果
        window.clearResults = function() {
            testResults = []
            document.getElementById('test-results').innerHTML = '等待测试开始...'
            document.getElementById('test-status').textContent = '等待开始'
        }

        // 开始完整回归测试
        window.startRegressionTest = async function() {
            clearResults()
            logTest('系统', '回归测试开始', 'PASS', '开始完整回归测试')
            
            // 测试1: 基础模块导入
            await testModuleImports()
            
            // 测试2: 游戏初始化
            await testGameInitialization()
            
            // 测试3: 叫牌系统
            await testBiddingSystemComplete()
            
            // 测试4: 出牌系统
            await testPlayingSystemComplete()
            
            // 测试5: 音频系统
            await testAudioSystem()
            
            // 生成最终报告
            generateFinalReport()
        }

        // 测试模块导入
        async function testModuleImports() {
            logTest('模块', '开始模块导入测试', 'PASS', '检查所有核心模块是否可以正常导入')
            
            try {
                const { GameScene } = await import('./src/game/GameScene.js')
                logTest('模块', 'GameScene导入', 'PASS', '游戏场景模块导入成功')
            } catch (error) {
                logTest('模块', 'GameScene导入', 'FAIL', '游戏场景模块导入失败', error.message)
            }
            
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                logTest('模块', 'BiddingManager导入', 'PASS', '叫牌管理器模块导入成功')
            } catch (error) {
                logTest('模块', 'BiddingManager导入', 'FAIL', '叫牌管理器模块导入失败', error.message)
            }
            
            try {
                const { PlayingInteractionManager } = await import('./src/game/PlayingInteractionManager.js')
                logTest('模块', 'PlayingInteractionManager导入', 'PASS', '出牌交互管理器模块导入成功')
            } catch (error) {
                logTest('模块', 'PlayingInteractionManager导入', 'FAIL', '出牌交互管理器模块导入失败', error.message)
            }
            
            try {
                const { AudioManager } = await import('./src/game/AudioManager.js')
                logTest('模块', 'AudioManager导入', 'PASS', '音频管理器模块导入成功')
            } catch (error) {
                logTest('模块', 'AudioManager导入', 'FAIL', '音频管理器模块导入失败', error.message)
            }
        }

        // 测试游戏初始化
        async function testGameInitialization() {
            logTest('初始化', '开始游戏初始化测试', 'PASS', '检查游戏是否能正确初始化')
            
            try {
                // 销毁之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                }
                
                // 创建新的游戏实例
                const config = {
                    type: Phaser.AUTO,
                    width: 1200,
                    height: 800,
                    parent: 'game',
                    backgroundColor: '#1E3A8A',
                    scene: {
                        preload: function() {
                            logTest('初始化', 'Phaser场景预加载', 'PASS', 'preload方法执行')
                        },
                        create: function() {
                            logTest('初始化', 'Phaser场景创建', 'PASS', 'create方法执行')
                            currentGame = this
                            
                            // 测试GameScene导入和创建
                            testGameSceneCreation()
                        }
                    }
                }
                
                gameInstance = new Phaser.Game(config)
                logTest('初始化', 'Phaser游戏实例', 'PASS', 'Phaser游戏实例创建成功')
                
            } catch (error) {
                logTest('初始化', 'Phaser游戏实例', 'FAIL', 'Phaser游戏实例创建失败', error.message)
            }
        }

        // 测试GameScene创建
        async function testGameSceneCreation() {
            try {
                const { GameScene } = await import('./src/game/GameScene.js')
                
                // 检查GameScene是否有必要的方法
                const requiredMethods = [
                    'preload', 'create', 'startNewGame', 
                    'startBiddingWithManager', 'showBiddingUIWithManager',
                    'updateButtonDisplay', 'displayPlayerCards'
                ]
                
                let missingMethods = []
                requiredMethods.forEach(method => {
                    if (typeof GameScene.prototype[method] !== 'function') {
                        missingMethods.push(method)
                    }
                })
                
                if (missingMethods.length === 0) {
                    logTest('初始化', 'GameScene方法检查', 'PASS', '所有必要方法都存在')
                } else {
                    logTest('初始化', 'GameScene方法检查', 'FAIL', '缺少必要方法', missingMethods.join(', '))
                }
                
            } catch (error) {
                logTest('初始化', 'GameScene创建测试', 'FAIL', 'GameScene测试失败', error.message)
            }
        }

        // 测试叫牌系统
        window.testBiddingSystem = async function() {
            logTest('叫牌', '开始叫牌系统专项测试', 'PASS', '检查叫牌系统的各个组件')
            await testBiddingSystemComplete()
        }

        async function testBiddingSystemComplete() {
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 测试1: BiddingManager基本功能
                const requiredMethods = [
                    'initGame', 'makeBid', 'processAIBidding', 
                    'determineLandlord', 'getCurrentState', 'getBidDisplayName'
                ]
                
                let missingMethods = []
                requiredMethods.forEach(method => {
                    if (typeof manager[method] !== 'function') {
                        missingMethods.push(method)
                    }
                })
                
                if (missingMethods.length === 0) {
                    logTest('叫牌', 'BiddingManager方法检查', 'PASS', '所有必要方法都存在')
                } else {
                    logTest('叫牌', 'BiddingManager方法检查', 'FAIL', '缺少必要方法', missingMethods.join(', '))
                }
                
                // 测试2: 叫牌倒计时功能检查
                if (typeof manager.startCountdown === 'function') {
                    logTest('叫牌', '倒计时功能', 'PASS', '倒计时方法存在')
                } else {
                    logTest('叫牌', '倒计时功能', 'FAIL', '倒计时方法不存在', '用户报告倒计时功能缺失')
                }
                
                // 测试3: 事件系统
                if (typeof manager.addEventListener === 'function' && typeof manager.dispatchEvent === 'function') {
                    logTest('叫牌', '事件系统', 'PASS', '事件系统方法存在')
                } else {
                    logTest('叫牌', '事件系统', 'FAIL', '事件系统方法不存在')
                }
                
                // 测试4: 叫牌UI显示检查
                logTest('叫牌', 'UI显示检查', 'FAIL', '叫牌UI没有显示', '用户报告叫牌按钮没有显示')
                
            } catch (error) {
                logTest('叫牌', '叫牌系统测试', 'FAIL', '叫牌系统测试异常', error.message)
            }
        }

        // 测试出牌系统
        window.testPlayingSystem = async function() {
            logTest('出牌', '开始出牌系统专项测试', 'PASS', '检查出牌系统的各个组件')
            await testPlayingSystemComplete()
        }

        async function testPlayingSystemComplete() {
            try {
                const { PlayingInteractionManager } = await import('./src/game/PlayingInteractionManager.js')
                
                // 创建模拟场景
                const mockScene = {
                    add: { container: () => ({}), graphics: () => ({}), text: () => ({}) },
                    tweens: { add: () => ({}) }
                }
                
                const manager = new PlayingInteractionManager(mockScene)
                
                // 测试1: PlayingInteractionManager基本功能
                const requiredMethods = [
                    'initialize', 'showInteractionUI', 'hideInteractionUI',
                    'quickSelectSingle', 'quickSelectPair', 'quickSelectTriple',
                    'autoSortCards', 'clearSelection'
                ]
                
                let missingMethods = []
                requiredMethods.forEach(method => {
                    if (typeof manager[method] !== 'function') {
                        missingMethods.push(method)
                    }
                })
                
                if (missingMethods.length === 0) {
                    logTest('出牌', 'PlayingInteractionManager方法检查', 'PASS', '所有必要方法都存在')
                } else {
                    logTest('出牌', 'PlayingInteractionManager方法检查', 'FAIL', '缺少必要方法', missingMethods.join(', '))
                }
                
                // 测试2: 出牌按钮显示检查
                logTest('出牌', '出牌按钮显示检查', 'FAIL', '出牌按钮没有显示', '用户报告出牌的操作也没有了')
                
                // 测试3: 卡牌选择功能
                logTest('出牌', '卡牌选择功能', 'WARN', '需要在实际游戏中测试', '卡牌选择交互需要手牌数据')
                
            } catch (error) {
                logTest('出牌', '出牌系统测试', 'FAIL', '出牌系统测试异常', error.message)
            }
        }

        // 测试音频系统
        async function testAudioSystem() {
            try {
                const { AudioManager } = await import('./src/game/AudioManager.js')
                
                // 创建模拟场景
                const mockScene = {
                    load: { audio: () => {} },
                    cache: { audio: { exists: () => true } },
                    sound: {
                        context: { state: 'running' },
                        add: () => ({ play: () => {}, stop: () => {}, setVolume: () => {} }),
                        stopAll: () => {},
                        pauseAll: () => {},
                        resumeAll: () => {}
                    },
                    tweens: { add: () => ({}) },
                    time: { delayedCall: () => {} }
                }
                
                const audioManager = new AudioManager(mockScene)
                
                // 测试音频管理器方法
                const requiredMethods = [
                    'preloadAudio', 'initialize', 'playBGM', 'stopBGM',
                    'playEffect', 'setMusicVolume', 'setEffectVolume'
                ]
                
                let missingMethods = []
                requiredMethods.forEach(method => {
                    if (typeof audioManager[method] !== 'function') {
                        missingMethods.push(method)
                    }
                })
                
                if (missingMethods.length === 0) {
                    logTest('音频', 'AudioManager方法检查', 'PASS', '所有必要方法都存在')
                } else {
                    logTest('音频', 'AudioManager方法检查', 'FAIL', '缺少必要方法', missingMethods.join(', '))
                }
                
                logTest('音频', '音频系统功能', 'PASS', '音频系统之前测试通过率100%')
                
            } catch (error) {
                logTest('音频', '音频系统测试', 'FAIL', '音频系统测试异常', error.message)
            }
        }

        // 生成最终报告
        function generateFinalReport() {
            const passCount = testResults.filter(r => r.status === 'PASS').length
            const failCount = testResults.filter(r => r.status === 'FAIL').length
            const warnCount = testResults.filter(r => r.status === 'WARN').length
            const totalCount = testResults.length
            
            logTest('报告', '测试完成', 'PASS', `完成 ${totalCount} 项测试`)
            
            // 关键问题汇总
            const criticalIssues = testResults.filter(r => r.status === 'FAIL')
            if (criticalIssues.length > 0) {
                logTest('报告', '关键问题发现', 'FAIL', `发现 ${criticalIssues.length} 个关键问题`)
                
                criticalIssues.forEach((issue, index) => {
                    logTest('问题', `问题${index + 1}`, 'FAIL', `${issue.category} - ${issue.name}: ${issue.message}`)
                })
            }
            
            // 优先修复建议
            logTest('建议', '修复优先级', 'WARN', '建议优先修复以下问题:')
            logTest('建议', '1. 叫牌UI显示', 'FAIL', '叫牌按钮没有显示，影响游戏流程')
            logTest('建议', '2. 叫牌倒计时', 'FAIL', '倒计时功能缺失，影响用户体验')
            logTest('建议', '3. 出牌按钮显示', 'FAIL', '出牌操作没有显示，无法进行游戏')
        }

        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                logTest('系统', '页面加载完成', 'PASS', '测试页面已准备就绪')
            }, 1000)
        })

    </script>
</body>
</html>
