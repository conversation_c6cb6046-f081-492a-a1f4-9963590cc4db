<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warn {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 UI修复验证测试</h1>
        <p>专门验证UI相关的修复内容</p>

        <div class="status-summary" id="statusSummary">
            等待测试开始...
        </div>

        <div class="test-section">
            <h3>测试1: GameScene方法验证</h3>
            <button onclick="testGameSceneMethods()">测试GameScene方法</button>
            <div id="gameSceneMethodsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: BiddingManager完整性验证</h3>
            <button onclick="testBiddingManagerComplete()">测试BiddingManager完整性</button>
            <div id="biddingManagerCompleteResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: UI按钮显示功能验证</h3>
            <button onclick="testUIButtonDisplay()">测试UI按钮显示</button>
            <div id="uiButtonDisplayResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 倒计时系统验证</h3>
            <button onclick="testCountdownSystem()">测试倒计时系统</button>
            <div id="countdownSystemResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试5: 综合功能测试</h3>
            <button onclick="runAllUITests()">运行所有UI测试</button>
            <div id="allUITestsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script type="module">
        let testResults = []

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message, details = '') {
            const element = document.getElementById(elementId)
            element.className = `test-result ${success ? 'pass' : 'fail'}`
            element.innerHTML = `
                <strong>${success ? '✅ 通过' : '❌ 失败'}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
        }

        function updateStatusSummary() {
            const passCount = testResults.filter(r => r.result === 'PASS').length
            const totalCount = testResults.length
            const statusDiv = document.getElementById('statusSummary')
            
            if (totalCount === 0) {
                statusDiv.textContent = '等待测试开始...'
            } else {
                const passRate = Math.round((passCount / totalCount) * 100)
                statusDiv.innerHTML = `
                    测试状态: 总计 ${totalCount} | ✅ ${passCount} | ❌ ${totalCount - passCount} | 通过率 ${passRate}%
                `
            }
        }

        // 测试1: GameScene方法验证
        window.testGameSceneMethods = async function() {
            log('🧪 开始测试GameScene方法')
            try {
                const { GameScene } = await import('./src/game/GameScene.js')
                
                // 检查必要方法
                const requiredMethods = [
                    'preload', 'create', 'startNewGame', 
                    'startBiddingWithManager', 'showBiddingUIWithManager',
                    'updateButtonDisplay', 'displayPlayerCards'
                ]
                
                let missingMethods = []
                let presentMethods = []
                
                requiredMethods.forEach(method => {
                    if (typeof GameScene.prototype[method] === 'function') {
                        presentMethods.push(method)
                        log(`✅ 方法存在: ${method}`)
                    } else {
                        missingMethods.push(method)
                        log(`❌ 方法缺失: ${method}`)
                    }
                })
                
                if (missingMethods.length === 0) {
                    testResults.push({ test: 'GameScene方法', result: 'PASS' })
                    showResult('gameSceneMethodsResult', true, '所有必要方法都存在', `检查了${requiredMethods.length}个方法`)
                } else {
                    testResults.push({ test: 'GameScene方法', result: 'FAIL' })
                    showResult('gameSceneMethodsResult', false, '缺少必要方法', `缺少: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ GameScene方法测试失败: ${error.message}`)
                testResults.push({ test: 'GameScene方法', result: 'FAIL', error: error.message })
                showResult('gameSceneMethodsResult', false, 'GameScene方法测试失败', error.message)
            }
            updateStatusSummary()
        }

        // 测试2: BiddingManager完整性验证
        window.testBiddingManagerComplete = async function() {
            log('🧪 开始测试BiddingManager完整性')
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 检查所有必要方法
                const requiredMethods = [
                    'initGame', 'makeBid', 'processAIBidding', 
                    'determineLandlord', 'getCurrentState', 'getBidDisplayName',
                    'startCountdown', 'clearCountdown', 'getRemainingTime',
                    'addEventListener', 'removeEventListener', 'emit'
                ]
                
                let missingMethods = []
                let presentMethods = []
                
                requiredMethods.forEach(method => {
                    if (typeof manager[method] === 'function') {
                        presentMethods.push(method)
                        log(`✅ 方法存在: ${method}`)
                    } else {
                        missingMethods.push(method)
                        log(`❌ 方法缺失: ${method}`)
                    }
                })
                
                // 测试基本功能
                const players = {
                    west: { name: '西家', cards: [] },
                    south: { name: '南家', cards: [] },
                    east: { name: '东家', cards: [] }
                }
                
                const firstBidder = manager.initGame(players, 1, 1, 'south')
                log(`✅ 游戏初始化成功，首叫者: ${firstBidder}`)
                
                const state = manager.getCurrentState()
                log(`✅ 状态获取成功，阶段: ${state.gameState.phase}`)
                
                if (missingMethods.length === 0) {
                    testResults.push({ test: 'BiddingManager完整性', result: 'PASS' })
                    showResult('biddingManagerCompleteResult', true, 'BiddingManager功能完整', `检查了${requiredMethods.length}个方法`)
                } else {
                    testResults.push({ test: 'BiddingManager完整性', result: 'FAIL' })
                    showResult('biddingManagerCompleteResult', false, 'BiddingManager功能不完整', `缺少: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ BiddingManager完整性测试失败: ${error.message}`)
                testResults.push({ test: 'BiddingManager完整性', result: 'FAIL', error: error.message })
                showResult('biddingManagerCompleteResult', false, 'BiddingManager完整性测试失败', error.message)
            }
            updateStatusSummary()
        }

        // 测试3: UI按钮显示功能验证
        window.testUIButtonDisplay = async function() {
            log('🧪 开始测试UI按钮显示功能')
            try {
                const { GameScene } = await import('./src/game/GameScene.js')
                
                // 检查UI相关方法
                const uiMethods = [
                    'updateButtonDisplay', 'showBiddingUIWithManager',
                    'createBiddingButtons', 'updateBiddingButtons',
                    'showPlayingButtons', 'hidePlayingButtons'
                ]
                
                let missingMethods = []
                let presentMethods = []
                
                uiMethods.forEach(method => {
                    if (typeof GameScene.prototype[method] === 'function') {
                        presentMethods.push(method)
                        log(`✅ UI方法存在: ${method}`)
                    } else {
                        missingMethods.push(method)
                        log(`❌ UI方法缺失: ${method}`)
                    }
                })
                
                if (missingMethods.length === 0) {
                    testResults.push({ test: 'UI按钮显示', result: 'PASS' })
                    showResult('uiButtonDisplayResult', true, 'UI按钮显示功能完整', `检查了${uiMethods.length}个UI方法`)
                } else {
                    testResults.push({ test: 'UI按钮显示', result: 'FAIL' })
                    showResult('uiButtonDisplayResult', false, 'UI按钮显示功能不完整', `缺少: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ UI按钮显示测试失败: ${error.message}`)
                testResults.push({ test: 'UI按钮显示', result: 'FAIL', error: error.message })
                showResult('uiButtonDisplayResult', false, 'UI按钮显示测试失败', error.message)
            }
            updateStatusSummary()
        }

        // 测试4: 倒计时系统验证
        window.testCountdownSystem = async function() {
            log('🧪 开始测试倒计时系统')
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 测试倒计时功能
                if (typeof manager.startCountdown === 'function' &&
                    typeof manager.clearCountdown === 'function' &&
                    typeof manager.getRemainingTime === 'function') {
                    
                    log('✅ 倒计时方法存在')
                    
                    // 测试时间限制设置
                    manager.setTimeLimit(20)
                    log('✅ 时间限制设置为20秒')
                    
                    // 测试初始剩余时间
                    const initialTime = manager.getRemainingTime()
                    log(`📊 初始剩余时间: ${initialTime}`)
                    
                    // 测试事件监听
                    let countdownEventReceived = false
                    manager.addEventListener('countdownUpdate', (data) => {
                        countdownEventReceived = true
                        log(`⏰ 倒计时更新事件: ${data.remainingTime}秒`)
                    })
                    
                    testResults.push({ test: '倒计时系统', result: 'PASS' })
                    showResult('countdownSystemResult', true, '倒计时系统功能完整', '所有倒计时功能都正常')
                    
                } else {
                    log('❌ 倒计时方法缺失')
                    testResults.push({ test: '倒计时系统', result: 'FAIL' })
                    showResult('countdownSystemResult', false, '倒计时系统功能缺失')
                }
                
            } catch (error) {
                log(`❌ 倒计时系统测试失败: ${error.message}`)
                testResults.push({ test: '倒计时系统', result: 'FAIL', error: error.message })
                showResult('countdownSystemResult', false, '倒计时系统测试失败', error.message)
            }
            updateStatusSummary()
        }

        // 运行所有UI测试
        window.runAllUITests = async function() {
            log('🚀 开始运行所有UI修复验证测试')
            testResults = []
            
            await testGameSceneMethods()
            await new Promise(resolve => setTimeout(resolve, 200))
            
            await testBiddingManagerComplete()
            await new Promise(resolve => setTimeout(resolve, 200))
            
            await testUIButtonDisplay()
            await new Promise(resolve => setTimeout(resolve, 200))
            
            await testCountdownSystem()
            await new Promise(resolve => setTimeout(resolve, 200))
            
            // 汇总结果
            const passCount = testResults.filter(r => r.result === 'PASS').length
            const totalCount = testResults.length
            const passRate = Math.round((passCount / totalCount) * 100)
            
            log(`\n📊 UI修复验证测试结果汇总:`)
            log(`✅ 通过: ${passCount}/${totalCount} (${passRate}%)`)
            
            testResults.forEach((result, index) => {
                const status = result.result === 'PASS' ? '✅' : '❌'
                log(`${index + 1}. ${result.test}: ${status} ${result.result}`)
                if (result.error) {
                    log(`   错误: ${result.error}`)
                }
            })
            
            if (passCount === totalCount) {
                showResult('allUITestsResult', true, '所有UI修复验证测试通过', `${passCount}/${totalCount} 测试通过`)
                log('🎉 所有UI修复验证测试通过！')
            } else {
                showResult('allUITestsResult', false, '部分UI修复验证测试失败', `${passCount}/${totalCount} 测试通过`)
                log('⚠️ 仍有部分UI问题需要修复')
            }
            
            updateStatusSummary()
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = ''
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 UI修复验证测试页面已加载')
            log('点击按钮开始测试各项UI修复内容')
            updateStatusSummary()
        })
    </script>
</body>
</html>
