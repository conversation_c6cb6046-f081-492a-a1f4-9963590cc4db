<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎴 斗地主叫牌系统测试</h1>
    
    <div class="test-container">
        <h2>测试控制</h2>
        <button onclick="runBasicTests()">运行基础测试</button>
        <button onclick="runSpecialCaseTests()">运行特殊情况测试</button>
        <button onclick="runInteractiveDemo()">交互式演示</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div class="test-container">
        <h2>当前状态</h2>
        <div id="status-display" class="status-info">
            点击测试按钮开始...
        </div>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script type="module">
        // 叫牌系统类（内嵌版本）
        class LandlordSelection {
            constructor() {
                this.bidOptions = {
                    NO_BID: 'no_bid',
                    ONE_POINT: 'one_point',
                    TWO_POINT: 'two_point',
                    THREE_POINT: 'three_point'
                }

                this.seats = {
                    WEST: 'west',
                    SOUTH: 'south',
                    EAST: 'east'
                }

                this.bidState = {
                    WAITING: 'waiting',
                    IN_PROGRESS: 'in_progress',
                    COMPLETED: 'completed'
                }

                this.reset()
            }

            reset() {
                this.currentState = this.bidState.WAITING
                this.currentBidder = null
                this.bidHistory = []
                this.landlord = null
                this.highestBid = null
                this.gameRound = 1
                this.gameNumber = 1
                this.players = {}
                this.firstBidder = null
            }

            initGame(players, round = 1, gameNumber = 1) {
                this.reset()
                this.gameRound = round
                this.gameNumber = gameNumber
                this.players = { ...players }
                
                this.firstBidder = this.determineFirstBidder()
                this.currentBidder = this.firstBidder
                this.currentState = this.bidState.IN_PROGRESS
                
                return this.firstBidder
            }

            determineFirstBidder() {
                if (this.gameNumber === 1) {
                    return this.seats.WEST
                } else {
                    const seatOrder = [this.seats.WEST, this.seats.SOUTH, this.seats.EAST]
                    const firstBidderIndex = (this.gameNumber - 1) % 3
                    return seatOrder[firstBidderIndex]
                }
            }

            getNextBidder(currentSeat) {
                const seatOrder = [this.seats.WEST, this.seats.SOUTH, this.seats.EAST]
                const currentIndex = seatOrder.indexOf(currentSeat)
                const nextIndex = (currentIndex + 1) % 3
                return seatOrder[nextIndex]
            }

            validateBid(playerSeat, bidOption) {
                if (this.currentState !== this.bidState.IN_PROGRESS) {
                    return { valid: false, reason: '当前不在叫牌阶段' }
                }

                if (playerSeat !== this.currentBidder) {
                    return { valid: false, reason: '还没轮到您叫牌' }
                }

                if (!Object.values(this.bidOptions).includes(bidOption)) {
                    return { valid: false, reason: '无效的叫牌选项' }
                }

                if (bidOption !== this.bidOptions.NO_BID) {
                    const bidValue = this.getBidValue(bidOption)
                    const currentHighestValue = this.highestBid ? this.getBidValue(this.highestBid.option) : 0
                    
                    if (bidValue <= currentHighestValue) {
                        return { valid: false, reason: '叫牌分数必须高于当前最高分' }
                    }
                }

                return { valid: true }
            }

            makeBid(playerSeat, bidOption) {
                const validation = this.validateBid(playerSeat, bidOption)
                if (!validation.valid) {
                    return { success: false, reason: validation.reason }
                }

                const bidRecord = {
                    player: playerSeat,
                    option: bidOption,
                    value: this.getBidValue(bidOption),
                    timestamp: Date.now()
                }
                this.bidHistory.push(bidRecord)

                if (bidOption !== this.bidOptions.NO_BID) {
                    this.highestBid = bidRecord
                }

                if (bidOption === this.bidOptions.THREE_POINT) {
                    this.landlord = playerSeat
                    this.currentState = this.bidState.COMPLETED
                    return { 
                        success: true, 
                        completed: true, 
                        landlord: this.landlord
                    }
                }

                this.currentBidder = this.getNextBidder(this.currentBidder)

                if (this.currentBidder === this.firstBidder) {
                    return this.completeBidding()
                }

                return { 
                    success: true, 
                    completed: false, 
                    nextBidder: this.currentBidder
                }
            }

            completeBidding() {
                const allNoBid = this.bidHistory.every(bid => bid.option === this.bidOptions.NO_BID)
                
                if (allNoBid) {
                    return { 
                        success: true, 
                        completed: true, 
                        redeal: true, 
                        reason: '所有玩家都选择不叫，需要重新发牌' 
                    }
                }

                if (this.highestBid) {
                    this.landlord = this.highestBid.player
                    this.currentState = this.bidState.COMPLETED
                    return { 
                        success: true, 
                        completed: true, 
                        landlord: this.landlord 
                    }
                }

                return { success: false, reason: '叫牌状态异常' }
            }

            getBidValue(bidOption) {
                const values = {
                    [this.bidOptions.NO_BID]: 0,
                    [this.bidOptions.ONE_POINT]: 1,
                    [this.bidOptions.TWO_POINT]: 2,
                    [this.bidOptions.THREE_POINT]: 3
                }
                return values[bidOption] || 0
            }

            getBidDisplayName(bidOption) {
                const names = {
                    [this.bidOptions.NO_BID]: '不叫',
                    [this.bidOptions.ONE_POINT]: '1分',
                    [this.bidOptions.TWO_POINT]: '2分',
                    [this.bidOptions.THREE_POINT]: '3分'
                }
                return names[bidOption] || '未知'
            }

            getStatus() {
                return {
                    state: this.currentState,
                    currentBidder: this.currentBidder,
                    firstBidder: this.firstBidder,
                    bidHistory: [...this.bidHistory],
                    highestBid: this.highestBid,
                    landlord: this.landlord,
                    gameRound: this.gameRound,
                    gameNumber: this.gameNumber
                }
            }

            getAvailableBids() {
                const currentHighestValue = this.highestBid ? this.getBidValue(this.highestBid.option) : 0
                const available = [this.bidOptions.NO_BID]

                if (currentHighestValue < 1) available.push(this.bidOptions.ONE_POINT)
                if (currentHighestValue < 2) available.push(this.bidOptions.TWO_POINT)
                if (currentHighestValue < 3) available.push(this.bidOptions.THREE_POINT)

                return available
            }
        }

        // 全局变量
        window.landlordSelection = new LandlordSelection()

        // 测试函数
        function addResult(message, type = 'success') {
            const container = document.getElementById('test-results')
            const div = document.createElement('div')
            div.className = `test-result ${type}`
            div.textContent = message
            container.appendChild(div)
        }

        function updateStatus() {
            const status = window.landlordSelection.getStatus()
            const statusDiv = document.getElementById('status-display')
            statusDiv.innerHTML = `
                <strong>游戏状态:</strong> ${status.state}<br>
                <strong>当前叫牌者:</strong> ${status.currentBidder || '无'}<br>
                <strong>首叫者:</strong> ${status.firstBidder || '无'}<br>
                <strong>最高叫牌:</strong> ${status.highestBid ? `${status.highestBid.player} - ${window.landlordSelection.getBidDisplayName(status.highestBid.option)}` : '无'}<br>
                <strong>地主:</strong> ${status.landlord || '未确定'}<br>
                <strong>叫牌历史:</strong> ${status.bidHistory.map(bid => `${bid.player}:${window.landlordSelection.getBidDisplayName(bid.option)}`).join(', ') || '无'}
            `
        }

        // 基础测试
        window.runBasicTests = function() {
            addResult('=== 开始基础测试 ===', 'warning')
            
            const players = { west: 'Player1', south: 'Player2', east: 'Player3' }
            
            // 测试1：初始化游戏
            const firstBidder = window.landlordSelection.initGame(players, 1, 1)
            addResult(`✅ 游戏初始化成功，首叫者: ${firstBidder}`)
            updateStatus()
            
            // 测试2：正常叫牌流程
            let result = window.landlordSelection.makeBid('west', 'one_point')
            addResult(`✅ 西家叫1分: ${result.success ? '成功' : '失败'}`)
            
            result = window.landlordSelection.makeBid('south', 'two_point')
            addResult(`✅ 南家叫2分: ${result.success ? '成功' : '失败'}`)
            
            result = window.landlordSelection.makeBid('east', 'no_bid')
            addResult(`✅ 东家不叫: ${result.success ? '成功' : '失败'}`)
            
            if (result.completed) {
                addResult(`🎉 叫牌结束，地主: ${result.landlord}`)
            }
            
            updateStatus()
            addResult('=== 基础测试完成 ===', 'warning')
        }

        // 特殊情况测试
        window.runSpecialCaseTests = function() {
            addResult('=== 开始特殊情况测试 ===', 'warning')
            
            // 测试3分立即结束
            window.landlordSelection.reset()
            const players = { west: 'Player1', south: 'Player2', east: 'Player3' }
            window.landlordSelection.initGame(players, 1, 1)
            
            const result = window.landlordSelection.makeBid('west', 'three_point')
            addResult(`✅ 西家叫3分立即结束: ${result.completed ? '成功' : '失败'}`)
            addResult(`🎉 地主确定: ${result.landlord}`)
            
            // 测试所有人不叫
            window.landlordSelection.reset()
            window.landlordSelection.initGame(players, 1, 2)
            
            window.landlordSelection.makeBid('south', 'no_bid')
            window.landlordSelection.makeBid('east', 'no_bid')
            const finalResult = window.landlordSelection.makeBid('west', 'no_bid')
            
            addResult(`✅ 所有人不叫重新发牌: ${finalResult.redeal ? '成功' : '失败'}`)
            
            updateStatus()
            addResult('=== 特殊情况测试完成 ===', 'warning')
        }

        // 交互式演示
        window.runInteractiveDemo = function() {
            addResult('=== 交互式演示开始 ===', 'warning')
            
            window.landlordSelection.reset()
            const players = { west: 'Player1', south: 'Player2', east: 'Player3' }
            window.landlordSelection.initGame(players, 1, 1)
            
            addResult('🎮 游戏已初始化，请在控制台使用以下命令进行叫牌:')
            addResult('landlordSelection.makeBid("west", "one_point") // 西家叫1分')
            addResult('landlordSelection.makeBid("south", "two_point") // 南家叫2分')
            addResult('landlordSelection.makeBid("east", "no_bid") // 东家不叫')
            
            updateStatus()
        }

        window.clearResults = function() {
            document.getElementById('test-results').innerHTML = ''
            document.getElementById('status-display').innerHTML = '点击测试按钮开始...'
        }

        // 页面加载完成后初始化
        updateStatus()
    </script>
</body>
</html>
