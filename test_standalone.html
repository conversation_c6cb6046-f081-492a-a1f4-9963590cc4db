<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牌型识别测试 - 独立版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>斗地主牌型识别测试</h1>
    
    <div class="test-container">
        <h2>测试控制</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 卡牌类
        class Card {
            constructor(suit, rank) {
                this.suit = suit // 花色: 'spades', 'hearts', 'diamonds', 'clubs', 'joker'
                this.rank = rank // 点数: 3-10, 'J', 'Q', 'K', 'A', '2', 'small_joker', 'big_joker'
                this.id = this.generateId()
                this.value = this.calculateValue()
            }

            generateId() {
                if (this.suit === 'joker') {
                    return this.rank === 'small_joker' ? 'joker_small' : 'joker_big'
                }
                return `${this.suit}_${this.rank}`
            }

            calculateValue() {
                const rankValues = {
                    '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
                    'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
                    'small_joker': 16, 'big_joker': 17
                }
                return rankValues[this.rank] || 0
            }

            getDisplayName() {
                if (this.suit === 'joker') {
                    return this.rank === 'small_joker' ? '小王' : '大王'
                }
                
                const suitNames = {
                    'spades': '♠', 'hearts': '♥', 'diamonds': '♦', 'clubs': '♣'
                }
                return `${suitNames[this.suit]}${this.rank}`
            }
        }

        // 牌型识别类（简化版，只包含核心功能）
        class CardPattern {
            constructor() {
                this.patterns = {
                    SINGLE: 'single',
                    PAIR: 'pair',
                    TRIPLE: 'triple',
                    TRIPLE_WITH_SINGLE: 'triple_with_single',
                    TRIPLE_WITH_PAIR: 'triple_with_pair',
                    STRAIGHT: 'straight',
                    PAIR_STRAIGHT: 'pair_straight',
                    TRIPLE_STRAIGHT: 'triple_straight',
                    BOMB: 'bomb',
                    BOMB_WITH_SINGLES: 'bomb_with_singles',
                    BOMB_WITH_PAIRS: 'bomb_with_pairs',
                    ROCKET: 'rocket'
                }
            }

            identifyPattern(cards) {
                if (!cards || cards.length === 0) return null

                const groups = this.groupByRank(cards)
                const groupSizes = Object.values(groups).map(group => group.length).sort((a, b) => b - a)

                // 火箭（双王）
                if (cards.length === 2 && this.isRocket(cards)) {
                    return { type: this.patterns.ROCKET, cards: cards }
                }

                // 炸弹
                if (cards.length === 4 && groupSizes[0] === 4) {
                    return { type: this.patterns.BOMB, cards: cards }
                }

                // 四带二（单牌）
                if (cards.length === 6 && groupSizes[0] === 4 && groupSizes[1] === 1 && groupSizes[2] === 1) {
                    return { type: this.patterns.BOMB_WITH_SINGLES, cards: cards }
                }

                // 四带二（对子）
                if (cards.length === 6 && groupSizes[0] === 4 && groupSizes[1] === 2) {
                    return { type: this.patterns.BOMB_WITH_PAIRS, cards: cards }
                }

                // 四带二（两对）
                if (cards.length === 8 && groupSizes[0] === 4 && groupSizes[1] === 2 && groupSizes[2] === 2) {
                    return { type: this.patterns.BOMB_WITH_PAIRS, cards: cards }
                }

                // 单牌
                if (cards.length === 1) {
                    return { type: this.patterns.SINGLE, cards: cards }
                }

                // 对子
                if (cards.length === 2 && groupSizes[0] === 2) {
                    return { type: this.patterns.PAIR, cards: cards }
                }

                // 三张
                if (cards.length === 3 && groupSizes[0] === 3) {
                    return { type: this.patterns.TRIPLE, cards: cards }
                }

                // 三带一
                if (cards.length === 4 && groupSizes[0] === 3 && groupSizes[1] === 1) {
                    return { type: this.patterns.TRIPLE_WITH_SINGLE, cards: cards }
                }

                // 三带二
                if (cards.length === 5 && groupSizes[0] === 3 && groupSizes[1] === 2) {
                    return { type: this.patterns.TRIPLE_WITH_PAIR, cards: cards }
                }

                // 顺子
                if (cards.length >= 5 && this.isStraight(cards)) {
                    return { type: this.patterns.STRAIGHT, cards: cards }
                }

                // 连对
                if (cards.length >= 6 && cards.length % 2 === 0 && this.isPairStraight(cards)) {
                    return { type: this.patterns.PAIR_STRAIGHT, cards: cards }
                }

                // 飞机
                if (cards.length >= 6 && this.isTripleStraight(cards)) {
                    const subtype = this.analyzeTripleStraight(cards).subtype
                    return { type: this.patterns.TRIPLE_STRAIGHT, subtype: subtype, cards: cards }
                }

                return null
            }

            groupByRank(cards) {
                const groups = {}
                cards.forEach(card => {
                    if (!groups[card.rank]) groups[card.rank] = []
                    groups[card.rank].push(card)
                })
                return groups
            }

            isRocket(cards) {
                if (cards.length !== 2) return false
                const ranks = cards.map(card => card.rank).sort()
                return ranks[0] === 'big_joker' && ranks[1] === 'small_joker'
            }

            isStraight(cards) {
                if (cards.length < 5) return false
                if (cards.some(card => card.suit === 'joker')) return false
                const values = [...new Set(cards.map(card => card.value))].sort((a, b) => a - b)
                if (values.length !== cards.length) return false
                for (let i = 1; i < values.length; i++) {
                    if (values[i] !== values[i-1] + 1) return false
                }
                if (values.includes(15) || values[values.length - 1] > 14) return false
                return true
            }

            isPairStraight(cards) {
                if (cards.length < 6 || cards.length % 2 !== 0) return false
                const groups = this.groupByRank(cards)
                const ranks = Object.keys(groups)
                if (!ranks.every(rank => groups[rank].length === 2)) return false
                if (ranks.length < 3) return false
                const values = ranks.map(rank => groups[rank][0].value).sort((a, b) => a - b)
                if (values.includes(15) || values[values.length - 1] > 14) return false
                return this.isConsecutive(values)
            }

            isTripleStraight(cards) {
                const groups = this.groupByRank(cards)
                const triples = []
                const others = []
                Object.entries(groups).forEach(([rank, cardGroup]) => {
                    if (cardGroup.length >= 3) {
                        triples.push(rank)
                        if (cardGroup.length === 4) others.push(cardGroup[3])
                    } else {
                        others.push(...cardGroup)
                    }
                })
                if (triples.length < 2) return false
                const tripleValues = triples.map(rank => groups[rank][0].value).sort((a, b) => a - b)
                if (!this.isConsecutive(tripleValues)) return false
                if (tripleValues.includes(15) || tripleValues[tripleValues.length - 1] > 14) return false
                const expectedOthers = triples.length
                const expectedOthers2 = triples.length * 2
                if (others.length === 0 || others.length === expectedOthers) return true
                if (others.length === expectedOthers2) {
                    const otherGroups = this.groupByRank(others)
                    return Object.values(otherGroups).every(group => group.length === 2)
                }
                return false
            }

            analyzeTripleStraight(cards) {
                const groups = this.groupByRank(cards)
                const triples = []
                const others = []
                Object.entries(groups).forEach(([rank, cardGroup]) => {
                    if (cardGroup.length >= 3) {
                        triples.push(rank)
                        if (cardGroup.length === 4) others.push(cardGroup[3])
                    } else {
                        others.push(...cardGroup)
                    }
                })
                if (others.length === 0) return { subtype: 'pure' }
                if (others.length === triples.length) return { subtype: 'with_singles' }
                if (others.length === triples.length * 2) {
                    const otherGroups = this.groupByRank(others)
                    if (Object.values(otherGroups).every(group => group.length === 2)) {
                        return { subtype: 'with_pairs' }
                    }
                }
                return { subtype: 'invalid' }
            }

            isConsecutive(values) {
                for (let i = 1; i < values.length; i++) {
                    if (values[i] !== values[i-1] + 1) return false
                }
                return true
            }
        }

        // 测试函数
        function createTestCards(cardSpecs) {
            return cardSpecs.map(spec => {
                const [suit, rank] = spec.split('-');
                return new Card(suit, rank);
            });
        }

        function runTest(testName, cards, expectedType, expectedSubtype = null) {
            const pattern = new CardPattern();
            const result = pattern.identifyPattern(cards);
            
            const cardNames = cards.map(c => c.getDisplayName()).join(' ');
            let success = false;
            let message = '';

            if (!result && !expectedType) {
                success = true;
                message = `✅ ${testName}: 正确识别为无效牌型 (${cardNames})`;
            } else if (result && result.type === expectedType) {
                if (expectedSubtype && result.subtype !== expectedSubtype) {
                    message = `❌ ${testName}: 牌型正确但子类型错误 (${cardNames}) - 期望: ${expectedSubtype}, 实际: ${result.subtype}`;
                } else {
                    success = true;
                    const subtypeText = result.subtype ? ` (${result.subtype})` : '';
                    message = `✅ ${testName}: 正确识别为 ${result.type}${subtypeText} (${cardNames})`;
                }
            } else {
                const actualType = result ? result.type : 'null';
                message = `❌ ${testName}: 识别错误 (${cardNames}) - 期望: ${expectedType}, 实际: ${actualType}`;
            }

            return { success, message };
        }

        function runAllTests() {
            try {
                console.log('开始运行测试...');
                const results = [];

                // 基础牌型测试
                results.push(runTest('单牌', createTestCards(['hearts-3']), 'single'));
                results.push(runTest('对子', createTestCards(['hearts-5', 'spades-5']), 'pair'));
                results.push(runTest('三张', createTestCards(['hearts-7', 'spades-7', 'clubs-7']), 'triple'));
                
                // 三带牌型测试
                results.push(runTest('三带一', createTestCards(['hearts-8', 'spades-8', 'clubs-8', 'diamonds-4']), 'triple_with_single'));
                results.push(runTest('三带二', createTestCards(['hearts-9', 'spades-9', 'clubs-9', 'diamonds-6', 'hearts-6']), 'triple_with_pair'));

                // 四带二测试
                results.push(runTest('四带二(单牌)', createTestCards(['hearts-J', 'spades-J', 'clubs-J', 'diamonds-J', 'hearts-3', 'spades-5']), 'bomb_with_singles'));
                results.push(runTest('四带二(对子)', createTestCards(['hearts-Q', 'spades-Q', 'clubs-Q', 'diamonds-Q', 'hearts-4', 'spades-4', 'clubs-7', 'diamonds-7']), 'bomb_with_pairs'));
                
                // 顺子测试
                results.push(runTest('顺子5张', createTestCards(['hearts-3', 'spades-4', 'clubs-5', 'diamonds-6', 'hearts-7']), 'straight'));
                results.push(runTest('顺子6张', createTestCards(['hearts-5', 'spades-6', 'clubs-7', 'diamonds-8', 'hearts-9', 'spades-10']), 'straight'));
                
                // 连对测试
                results.push(runTest('连对3对', createTestCards(['hearts-3', 'spades-3', 'clubs-4', 'diamonds-4', 'hearts-5', 'spades-5']), 'pair_straight'));
                
                // 飞机测试
                results.push(runTest('飞机不带牌', createTestCards(['hearts-5', 'spades-5', 'clubs-5', 'diamonds-6', 'hearts-6', 'spades-6']), 'triple_straight', 'pure'));
                results.push(runTest('飞机带单牌', createTestCards(['hearts-7', 'spades-7', 'clubs-7', 'diamonds-8', 'hearts-8', 'spades-8', 'clubs-3', 'diamonds-4']), 'triple_straight', 'with_singles'));
                
                // 炸弹测试
                results.push(runTest('炸弹', createTestCards(['hearts-10', 'spades-10', 'clubs-10', 'diamonds-10']), 'bomb'));
                results.push(runTest('火箭', createTestCards(['joker-small_joker', 'joker-big_joker']), 'rocket'));

                // 无效牌型测试
                results.push(runTest('无效组合', createTestCards(['hearts-3', 'spades-5', 'clubs-7']), null));

                console.log('测试完成，显示结果...');
                displayResults(results);
            } catch (error) {
                console.error('测试运行出错:', error);
                const container = document.getElementById('test-results');
                container.innerHTML = `<div class="test-result error">测试运行出错: ${error.message}</div>`;
            }
        }

        function displayResults(results) {
            const container = document.getElementById('test-results');
            container.innerHTML = '';

            let passCount = 0;
            let totalCount = results.length;

            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.success ? 'success' : 'error'}`;
                div.textContent = result.message;
                container.appendChild(div);

                if (result.success) passCount++;
            });

            // 添加总结
            const summary = document.createElement('div');
            summary.className = 'test-result';
            summary.style.backgroundColor = passCount === totalCount ? '#d4edda' : '#fff3cd';
            summary.style.color = passCount === totalCount ? '#155724' : '#856404';
            summary.style.border = passCount === totalCount ? '1px solid #c3e6cb' : '1px solid #ffeaa7';
            summary.style.fontWeight = 'bold';
            summary.textContent = `测试完成: ${passCount}/${totalCount} 通过`;
            container.appendChild(summary);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
    </script>
</body>
</html>
