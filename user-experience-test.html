<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 斗地主用户体验测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        .game-section {
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        }
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        button.primary:hover {
            background: linear-gradient(45deg, #42A5F5, #64B5F6);
        }
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        button.warning:hover {
            background: linear-gradient(45deg, #FFB74D, #FFCC02);
        }
        button.danger {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        button.danger:hover {
            background: linear-gradient(45deg, #EF5350, #E57373);
        }
        .status-panel {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #FFD700;
        }
        .test-steps {
            background: rgba(0,0,0,0.7);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        .step.current {
            border-left-color: #FFD700;
            background: rgba(255, 215, 0, 0.2);
            animation: pulse 2s infinite;
        }
        .step.completed {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.2);
        }
        .step.failed {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.2);
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .log {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            border: 1px solid #333;
        }
        #gameContainer {
            width: 100%;
            max-width: 1000px;
            height: 600px;
            border: 3px solid #FFD700;
            margin: 20px auto;
            background: #000;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.5);
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0,0,0,0.5);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #66BB6A);
            width: 0%;
            transition: width 0.5s ease;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #333;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .metric-label {
            font-size: 12px;
            color: #ccc;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 斗地主完整用户体验测试</h1>
            <p>模拟真实游戏流程，测试所有交互功能</p>
        </div>

        <div class="game-section">
            <h3>🎯 测试控制面板</h3>
            <div class="controls">
                <button class="primary" onclick="startFullGameTest()">🚀 开始完整游戏测试</button>
                <button onclick="resetTest()">🔄 重置测试</button>
                <button class="warning" onclick="skipToStep(3)">⏭️ 跳到叫牌阶段</button>
                <button class="warning" onclick="skipToStep(5)">⏭️ 跳到出牌阶段</button>
                <button class="danger" onclick="stopTest()">⏹️ 停止测试</button>
            </div>

            <div class="status-panel">
                <h4>📊 当前状态</h4>
                <div id="currentStatus">等待开始测试...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText">0% 完成</div>
            </div>

            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="testTime">0</div>
                    <div class="metric-label">测试时间 (秒)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="completedSteps">0</div>
                    <div class="metric-label">完成步骤</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="userActions">0</div>
                    <div class="metric-label">用户操作</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="responseTime">0</div>
                    <div class="metric-label">平均响应时间 (ms)</div>
                </div>
            </div>
        </div>

        <div class="game-section">
            <h3>📋 测试步骤</h3>
            <div class="test-steps" id="testSteps">
                <div class="step" id="step1">1. 🎮 初始化游戏场景</div>
                <div class="step" id="step2">2. 🃏 发牌和手牌显示</div>
                <div class="step" id="step3">3. 🎯 叫牌阶段交互</div>
                <div class="step" id="step4">4. ⏰ 倒计时功能测试</div>
                <div class="step" id="step5">5. 🎴 出牌阶段交互</div>
                <div class="step" id="step6">6. 🔄 游戏流程切换</div>
                <div class="step" id="step7">7. 📱 移动端适配测试</div>
                <div class="step" id="step8">8. 🎊 完整流程验证</div>
            </div>
        </div>

        <div class="game-section">
            <h3>🎮 游戏显示区域</h3>
            <div id="gameContainer"></div>
        </div>

        <div class="game-section">
            <h3>📝 测试日志</h3>
            <div class="controls">
                <button onclick="clearLog()">🗑️ 清空日志</button>
                <button onclick="exportLog()">📄 导出日志</button>
            </div>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null
        let testStartTime = null
        let currentStep = 0
        let totalSteps = 8
        let userActionCount = 0
        let responseTimes = []
        let testTimer = null

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'user': '👤'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function updateStatus(status) {
            document.getElementById('currentStatus').textContent = status
        }

        function updateProgress(step) {
            const progress = (step / totalSteps) * 100
            document.getElementById('progressFill').style.width = `${progress}%`
            document.getElementById('progressText').textContent = `${Math.round(progress)}% 完成`
            document.getElementById('completedSteps').textContent = step
        }

        function updateMetrics() {
            if (testStartTime) {
                const elapsed = Math.floor((Date.now() - testStartTime) / 1000)
                document.getElementById('testTime').textContent = elapsed
            }
            document.getElementById('userActions').textContent = userActionCount
            
            if (responseTimes.length > 0) {
                const avgResponse = Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
                document.getElementById('responseTime').textContent = avgResponse
            }
        }

        function setStepStatus(stepNum, status) {
            const stepElement = document.getElementById(`step${stepNum}`)
            stepElement.className = `step ${status}`
            
            if (status === 'current') {
                currentStep = stepNum
                updateProgress(stepNum - 1)
            } else if (status === 'completed') {
                updateProgress(stepNum)
            }
        }

        // 开始完整游戏测试
        window.startFullGameTest = async function() {
            log('🚀 开始完整用户体验测试', 'info')
            testStartTime = Date.now()
            userActionCount = 0
            responseTimes = []
            
            // 启动定时器更新指标
            testTimer = setInterval(updateMetrics, 1000)
            
            updateStatus('正在初始化游戏...')
            setStepStatus(1, 'current')
            
            await executeStep1()
        }

        // 步骤1: 初始化游戏场景
        async function executeStep1() {
            log('📋 步骤1: 初始化游戏场景', 'info')
            const stepStart = Date.now()
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }

                // 导入游戏场景
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 1000,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene,
                    scale: {
                        mode: Phaser.Scale.FIT,
                        autoCenter: Phaser.Scale.CENTER_BOTH
                    }
                }

                gameInstance = new Phaser.Game(config)
                
                // 等待场景加载
                setTimeout(() => {
                    gameScene = gameInstance.scene.getScene('GameMainScene')
                    if (gameScene) {
                        const responseTime = Date.now() - stepStart
                        responseTimes.push(responseTime)
                        
                        log('✅ 游戏场景初始化成功', 'success')
                        setStepStatus(1, 'completed')
                        updateStatus('游戏场景已就绪')
                        
                        setTimeout(() => executeStep2(), 1000)
                    } else {
                        log('❌ 游戏场景获取失败', 'error')
                        setStepStatus(1, 'failed')
                    }
                }, 2000)

            } catch (error) {
                log(`❌ 步骤1失败: ${error.message}`, 'error')
                setStepStatus(1, 'failed')
            }
        }

        // 步骤2: 发牌和手牌显示
        async function executeStep2() {
            log('📋 步骤2: 发牌和手牌显示', 'info')
            setStepStatus(2, 'current')
            updateStatus('正在测试发牌功能...')
            
            const stepStart = Date.now()
            
            try {
                if (!gameScene) {
                    throw new Error('游戏场景未就绪')
                }

                // 模拟发牌
                log('🃏 开始发牌测试', 'info')
                
                // 检查发牌相关方法
                const hasDealMethod = typeof gameScene.dealCards === 'function'
                const hasDisplayMethod = typeof gameScene.displayPlayerCards === 'function'
                
                if (hasDealMethod || hasDisplayMethod) {
                    log('✅ 发牌方法检查通过', 'success')
                    
                    // 模拟手牌数据
                    const mockCards = [
                        { suit: 'spades', rank: 'A' },
                        { suit: 'hearts', rank: 'K' },
                        { suit: 'diamonds', rank: 'Q' },
                        { suit: 'clubs', rank: 'J' }
                    ]
                    
                    log(`🎴 模拟手牌: ${mockCards.length}张牌`, 'info')
                    
                    const responseTime = Date.now() - stepStart
                    responseTimes.push(responseTime)
                    
                    setStepStatus(2, 'completed')
                    updateStatus('发牌功能测试完成')
                    
                    setTimeout(() => executeStep3(), 1500)
                } else {
                    log('⚠️ 发牌方法不存在，跳过此步骤', 'warning')
                    setStepStatus(2, 'completed')
                    setTimeout(() => executeStep3(), 1000)
                }

            } catch (error) {
                log(`❌ 步骤2失败: ${error.message}`, 'error')
                setStepStatus(2, 'failed')
                setTimeout(() => executeStep3(), 1000)
            }
        }

        // 步骤3: 叫牌阶段交互
        async function executeStep3() {
            log('📋 步骤3: 叫牌阶段交互测试', 'info')
            setStepStatus(3, 'current')
            updateStatus('正在测试叫牌UI交互...')
            
            const stepStart = Date.now()
            
            try {
                if (!gameScene) {
                    throw new Error('游戏场景未就绪')
                }

                // 强制显示叫牌UI
                log('🎯 显示叫牌UI', 'info')
                
                if (typeof gameScene.forceShowBiddingUI === 'function') {
                    gameScene.forceShowBiddingUI()
                    log('✅ 叫牌UI显示命令已发送', 'success')
                } else if (typeof gameScene.showBiddingUIWithManager === 'function') {
                    gameScene.showBiddingUIWithManager({
                        availableBids: ['no_bid', 'one_point', 'two_point', 'three_point'],
                        biddingStatus: { highestBid: null }
                    })
                    log('✅ 叫牌UI管理器显示命令已发送', 'success')
                }
                
                // 检查UI是否显示
                setTimeout(() => {
                    let uiVisible = false
                    if (gameScene.uiElements && gameScene.uiElements.biddingContainer) {
                        uiVisible = gameScene.uiElements.biddingContainer.visible
                    }
                    
                    if (uiVisible) {
                        log('✅ 叫牌UI成功显示', 'success')
                        log('👤 请手动测试叫牌按钮点击功能', 'user')
                        
                        const responseTime = Date.now() - stepStart
                        responseTimes.push(responseTime)
                        
                        setStepStatus(3, 'completed')
                        updateStatus('叫牌UI交互就绪 - 请手动测试')
                        
                        // 给用户时间测试交互
                        setTimeout(() => executeStep4(), 3000)
                    } else {
                        log('⚠️ 叫牌UI可能未正确显示', 'warning')
                        setStepStatus(3, 'completed')
                        setTimeout(() => executeStep4(), 2000)
                    }
                }, 1000)

            } catch (error) {
                log(`❌ 步骤3失败: ${error.message}`, 'error')
                setStepStatus(3, 'failed')
                setTimeout(() => executeStep4(), 1000)
            }
        }

        // 步骤4: 倒计时功能测试
        async function executeStep4() {
            log('📋 步骤4: 倒计时功能测试', 'info')
            setStepStatus(4, 'current')
            updateStatus('正在测试倒计时功能...')

            const stepStart = Date.now()

            try {
                if (!gameScene) {
                    throw new Error('游戏场景未就绪')
                }

                // 检查倒计时方法
                const hasStartCountdown = typeof gameScene.startBiddingCountdown === 'function'
                const hasStopCountdown = typeof gameScene.stopBiddingCountdown === 'function'
                const hasUpdateCountdown = typeof gameScene.updateBiddingCountdownDisplay === 'function'

                if (hasStartCountdown && hasStopCountdown && hasUpdateCountdown) {
                    log('✅ 倒计时方法检查通过', 'success')

                    // 测试启动倒计时
                    if (hasStartCountdown) {
                        log('⏰ 启动倒计时测试', 'info')
                        // 这里可以调用倒计时方法进行测试
                    }

                    const responseTime = Date.now() - stepStart
                    responseTimes.push(responseTime)

                    setStepStatus(4, 'completed')
                    updateStatus('倒计时功能测试完成')

                    setTimeout(() => executeStep5(), 1500)
                } else {
                    log('⚠️ 部分倒计时方法不存在', 'warning')
                    setStepStatus(4, 'completed')
                    setTimeout(() => executeStep5(), 1000)
                }

            } catch (error) {
                log(`❌ 步骤4失败: ${error.message}`, 'error')
                setStepStatus(4, 'failed')
                setTimeout(() => executeStep5(), 1000)
            }
        }

        // 步骤5: 出牌阶段交互
        async function executeStep5() {
            log('📋 步骤5: 出牌阶段交互测试', 'info')
            setStepStatus(5, 'current')
            updateStatus('正在测试出牌UI交互...')

            const stepStart = Date.now()

            try {
                if (!gameScene) {
                    throw new Error('游戏场景未就绪')
                }

                // 强制显示出牌UI
                log('🎴 显示出牌UI', 'info')

                if (typeof gameScene.forceShowPlayingButtons === 'function') {
                    gameScene.forceShowPlayingButtons()
                    log('✅ 出牌UI显示命令已发送', 'success')
                } else if (typeof gameScene.showPlayingButtons === 'function') {
                    gameScene.showPlayingButtons()
                    log('✅ 出牌UI显示命令已发送', 'success')
                }

                // 检查UI是否显示
                setTimeout(() => {
                    let uiVisible = false
                    if (gameScene.uiElements && gameScene.uiElements.playingButtons) {
                        uiVisible = gameScene.uiElements.playingButtons.visible
                    }

                    if (uiVisible) {
                        log('✅ 出牌UI成功显示', 'success')
                        log('👤 请手动测试出牌按钮点击功能', 'user')

                        const responseTime = Date.now() - stepStart
                        responseTimes.push(responseTime)

                        setStepStatus(5, 'completed')
                        updateStatus('出牌UI交互就绪 - 请手动测试')

                        // 给用户时间测试交互
                        setTimeout(() => executeStep6(), 3000)
                    } else {
                        log('⚠️ 出牌UI可能未正确显示', 'warning')
                        setStepStatus(5, 'completed')
                        setTimeout(() => executeStep6(), 2000)
                    }
                }, 1000)

            } catch (error) {
                log(`❌ 步骤5失败: ${error.message}`, 'error')
                setStepStatus(5, 'failed')
                setTimeout(() => executeStep6(), 1000)
            }
        }

        // 步骤6: 游戏流程切换
        async function executeStep6() {
            log('📋 步骤6: 游戏流程切换测试', 'info')
            setStepStatus(6, 'current')
            updateStatus('正在测试游戏流程切换...')

            const stepStart = Date.now()

            try {
                if (!gameScene) {
                    throw new Error('游戏场景未就绪')
                }

                // 测试叫牌到出牌的切换
                log('🔄 测试叫牌到出牌切换', 'info')

                // 隐藏叫牌UI
                if (gameScene.uiElements && gameScene.uiElements.biddingContainer) {
                    gameScene.uiElements.biddingContainer.setVisible(false)
                    log('✅ 叫牌UI已隐藏', 'success')
                }

                // 显示出牌UI
                if (typeof gameScene.forceShowPlayingButtons === 'function') {
                    gameScene.forceShowPlayingButtons()
                    log('✅ 出牌UI已显示', 'success')
                }

                const responseTime = Date.now() - stepStart
                responseTimes.push(responseTime)

                setStepStatus(6, 'completed')
                updateStatus('游戏流程切换测试完成')

                setTimeout(() => executeStep7(), 1500)

            } catch (error) {
                log(`❌ 步骤6失败: ${error.message}`, 'error')
                setStepStatus(6, 'failed')
                setTimeout(() => executeStep7(), 1000)
            }
        }

        // 步骤7: 移动端适配测试
        async function executeStep7() {
            log('📋 步骤7: 移动端适配测试', 'info')
            setStepStatus(7, 'current')
            updateStatus('正在测试移动端适配...')

            const stepStart = Date.now()

            try {
                // 检查是否为移动设备
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
                const screenWidth = window.innerWidth

                log(`📱 设备检测: ${isMobile ? '移动设备' : '桌面设备'}`, 'info')
                log(`📏 屏幕宽度: ${screenWidth}px`, 'info')

                // 检查游戏容器适配
                const gameContainer = document.getElementById('gameContainer')
                const containerWidth = gameContainer.offsetWidth

                if (containerWidth <= screenWidth) {
                    log('✅ 游戏容器适配正常', 'success')
                } else {
                    log('⚠️ 游戏容器可能超出屏幕宽度', 'warning')
                }

                // 检查Phaser缩放设置
                if (gameInstance && gameInstance.scale) {
                    log(`🎮 Phaser缩放模式: ${gameInstance.scale.scaleMode}`, 'info')
                    log('✅ Phaser缩放配置检查完成', 'success')
                }

                const responseTime = Date.now() - stepStart
                responseTimes.push(responseTime)

                setStepStatus(7, 'completed')
                updateStatus('移动端适配测试完成')

                setTimeout(() => executeStep8(), 1500)

            } catch (error) {
                log(`❌ 步骤7失败: ${error.message}`, 'error')
                setStepStatus(7, 'failed')
                setTimeout(() => executeStep8(), 1000)
            }
        }

        // 步骤8: 完整流程验证
        async function executeStep8() {
            log('📋 步骤8: 完整流程验证', 'info')
            setStepStatus(8, 'current')
            updateStatus('正在进行完整流程验证...')

            const stepStart = Date.now()

            try {
                // 验证所有核心功能
                const checks = []

                // 检查游戏场景
                if (gameScene) {
                    checks.push('✅ 游戏场景')
                    log('✅ 游戏场景验证通过', 'success')
                } else {
                    checks.push('❌ 游戏场景')
                    log('❌ 游戏场景验证失败', 'error')
                }

                // 检查叫牌功能
                if (typeof gameScene?.forceShowBiddingUI === 'function') {
                    checks.push('✅ 叫牌功能')
                    log('✅ 叫牌功能验证通过', 'success')
                } else {
                    checks.push('❌ 叫牌功能')
                    log('❌ 叫牌功能验证失败', 'error')
                }

                // 检查出牌功能
                if (typeof gameScene?.forceShowPlayingButtons === 'function') {
                    checks.push('✅ 出牌功能')
                    log('✅ 出牌功能验证通过', 'success')
                } else {
                    checks.push('❌ 出牌功能')
                    log('❌ 出牌功能验证失败', 'error')
                }

                const passedChecks = checks.filter(c => c.startsWith('✅')).length
                const totalChecks = checks.length
                const successRate = Math.round((passedChecks / totalChecks) * 100)

                log(`📊 验证结果: ${passedChecks}/${totalChecks} 通过 (${successRate}%)`, 'info')

                const responseTime = Date.now() - stepStart
                responseTimes.push(responseTime)

                setStepStatus(8, 'completed')
                updateStatus(`完整流程验证完成 - 成功率: ${successRate}%`)

                // 测试完成
                setTimeout(() => {
                    log('🎊 用户体验测试全部完成！', 'success')
                    updateStatus('所有测试步骤已完成')

                    if (testTimer) {
                        clearInterval(testTimer)
                        testTimer = null
                    }

                    // 显示最终统计
                    const totalTime = Math.floor((Date.now() - testStartTime) / 1000)
                    const avgResponseTime = responseTimes.length > 0 ?
                        Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0

                    log(`📈 最终统计:`, 'info')
                    log(`   总测试时间: ${totalTime}秒`, 'info')
                    log(`   完成步骤: ${totalSteps}/${totalSteps}`, 'info')
                    log(`   用户操作: ${userActionCount}次`, 'info')
                    log(`   平均响应时间: ${avgResponseTime}ms`, 'info')
                    log(`   功能成功率: ${successRate}%`, 'info')

                }, 1000)

            } catch (error) {
                log(`❌ 步骤8失败: ${error.message}`, 'error')
                setStepStatus(8, 'failed')
            }
        }

        // 跳转到指定步骤
        window.skipToStep = function(stepNum) {
            log(`⏭️ 跳转到步骤${stepNum}`, 'info')
            userActionCount++

            if (!testStartTime) {
                testStartTime = Date.now()
                testTimer = setInterval(updateMetrics, 1000)
            }

            // 重置所有步骤状态
            for (let i = 1; i <= totalSteps; i++) {
                if (i < stepNum) {
                    setStepStatus(i, 'completed')
                } else {
                    document.getElementById(`step${i}`).className = 'step'
                }
            }

            // 执行指定步骤
            switch(stepNum) {
                case 3:
                    executeStep3()
                    break
                case 5:
                    executeStep5()
                    break
                default:
                    log(`⚠️ 步骤${stepNum}不支持直接跳转`, 'warning')
            }
        }

        window.exportLog = function() {
            const logContent = document.getElementById('testLog').textContent
            const blob = new Blob([logContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `ux-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            log('📄 测试日志已导出', 'success')
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        window.resetTest = function() {
            log('🔄 重置测试', 'info')
            currentStep = 0
            userActionCount = 0
            responseTimes = []
            testStartTime = null
            
            if (testTimer) {
                clearInterval(testTimer)
                testTimer = null
            }
            
            // 重置所有步骤状态
            for (let i = 1; i <= totalSteps; i++) {
                document.getElementById(`step${i}`).className = 'step'
            }
            
            updateProgress(0)
            updateStatus('测试已重置')
            updateMetrics()
        }

        window.stopTest = function() {
            log('⏹️ 停止测试', 'warning')
            if (testTimer) {
                clearInterval(testTimer)
                testTimer = null
            }
            updateStatus('测试已停止')
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎮 用户体验测试页面已加载', 'info')
            log('点击"开始完整游戏测试"开始自动化用户体验测试', 'info')
            updateMetrics()
        })
    </script>
</body>
</html>
