<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出牌界面交互测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .game-container {
            width: 100%;
            height: 600px;
            border: 2px solid #FFD700;
            border-radius: 10px;
            margin: 20px 0;
            background: #0f5132;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button.primary {
            background: #2196F3;
        }
        
        button.primary:hover {
            background: #1976D2;
        }
        
        button.warning {
            background: #FF9800;
        }
        
        button.warning:hover {
            background: #F57C00;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            min-height: 100px;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        h3 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .error {
            color: #f44336;
            font-weight: bold;
        }
        
        .info {
            color: #2196F3;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 出牌界面交互测试</h1>
        
        <div class="test-section">
            <h3>🎯 游戏控制</h3>
            <div class="controls">
                <button class="primary" onclick="initGame()">初始化游戏</button>
                <button onclick="startGame()">开始游戏</button>
                <button onclick="enterPlayingPhase()">进入出牌阶段</button>
                <button class="warning" onclick="resetGame()">重置游戏</button>
            </div>
        </div>

        <div class="game-container" id="gameContainer">
            <!-- Phaser游戏将在这里渲染 -->
        </div>

        <div class="test-section">
            <h3>🎮 交互测试</h3>
            <div class="controls">
                <button onclick="testQuickSelect()">测试快速选择</button>
                <button onclick="testCardSorting()">测试卡牌排序</button>
                <button onclick="testSuggestions()">测试出牌建议</button>
                <button onclick="testClearSelection()">测试清空选择</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 游戏状态</h3>
            <div id="gameStatus" class="status">等待初始化...</div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="testLog" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script type="module">
        import GameScene from './src/game/GameScene.js'
        
        let gameScene = null
        let gameMainScene = null
        
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog')
            const time = new Date().toLocaleTimeString()
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 'info'
            
            logElement.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`
            logElement.scrollTop = logElement.scrollHeight
            console.log(message)
        }
        
        function updateStatus(status) {
            document.getElementById('gameStatus').textContent = status
        }
        
        // 初始化游戏
        function init() {
            try {
                log('🎮 初始化出牌界面交互测试', 'info')
                
                // 创建游戏场景
                gameScene = new GameScene('gameContainer')
                
                // 等待游戏初始化完成
                setTimeout(() => {
                    // 获取Phaser场景实例
                    if (gameScene.game && gameScene.game.scene) {
                        gameMainScene = gameScene.game.scene.getScene('GameMainScene')
                        if (gameMainScene) {
                            log('✅ 获取到GameMainScene实例', 'success')
                            setupGameEvents()
                        } else {
                            log('❌ 无法获取GameMainScene实例', 'error')
                        }
                    }
                }, 1000)
                
                log('✅ 游戏场景初始化完成', 'success')
                updateStatus('游戏已初始化')
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error')
            }
        }
        
        // 设置游戏事件监听
        function setupGameEvents() {
            if (!gameMainScene) {
                log('❌ 游戏场景不存在', 'error')
                return
            }
            
            log('🎯 设置游戏事件监听', 'info')
            
            // 监听出牌交互管理器事件
            if (gameMainScene.playingInteractionManager) {
                log('✅ 出牌交互管理器已初始化', 'success')
                
                // 监听游戏阶段变化
                gameMainScene.playingInteractionManager.addEventListener('gamePhaseChanged', (phase) => {
                    log(`🎮 游戏阶段变化: ${phase}`, 'info')
                    updateStatus(`当前阶段: ${phase}`)
                })
                
                // 监听卡牌选择变化
                gameMainScene.playingInteractionManager.addEventListener('cardsSelected', (cards) => {
                    log(`🎯 卡牌选择变化: ${cards.length}张`, 'info')
                })
            } else {
                log('❌ 出牌交互管理器未初始化', 'error')
            }
        }
        
        // 全局函数
        window.initGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化', 'error')
                return
            }
            
            try {
                log('🎮 初始化游戏状态', 'info')
                gameMainScene.gameState.reset()
                updateStatus('游戏状态已重置')
            } catch (error) {
                log(`❌ 初始化游戏失败: ${error.message}`, 'error')
            }
        }
        
        window.startGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化', 'error')
                return
            }
            
            try {
                log('🎮 开始游戏', 'info')
                gameMainScene.startNewGame()
                updateStatus('游戏已开始')
            } catch (error) {
                log(`❌ 开始游戏失败: ${error.message}`, 'error')
            }
        }
        
        window.enterPlayingPhase = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化', 'error')
                return
            }
            
            try {
                log('🎮 进入出牌阶段', 'info')
                
                // 模拟进入出牌阶段
                gameMainScene.gameState.startPlaying()
                gameMainScene.updateUI()
                
                // 显示出牌交互界面
                if (gameMainScene.playingInteractionManager) {
                    gameMainScene.playingInteractionManager.showInteractionUI()
                }
                
                updateStatus('已进入出牌阶段')
                log('✅ 出牌阶段界面已显示', 'success')
            } catch (error) {
                log(`❌ 进入出牌阶段失败: ${error.message}`, 'error')
            }
        }
        
        window.resetGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化', 'error')
                return
            }
            
            try {
                log('🔄 重置游戏', 'info')
                gameMainScene.gameState.reset()
                
                // 隐藏出牌交互界面
                if (gameMainScene.playingInteractionManager) {
                    gameMainScene.playingInteractionManager.hideInteractionUI()
                }
                
                updateStatus('游戏已重置')
            } catch (error) {
                log(`❌ 重置游戏失败: ${error.message}`, 'error')
            }
        }
        
        window.testQuickSelect = function() {
            if (!gameMainScene || !gameMainScene.playingInteractionManager) {
                log('❌ 出牌交互管理器未初始化', 'error')
                return
            }
            
            try {
                log('🎯 测试快速选择功能', 'info')
                
                // 测试快速选择单牌
                setTimeout(() => {
                    gameMainScene.playingInteractionManager.quickSelectSingle()
                    log('✅ 快速选择单牌测试完成', 'success')
                }, 500)
                
                // 测试快速选择对子
                setTimeout(() => {
                    gameMainScene.playingInteractionManager.quickSelectPair()
                    log('✅ 快速选择对子测试完成', 'success')
                }, 1500)
                
            } catch (error) {
                log(`❌ 快速选择测试失败: ${error.message}`, 'error')
            }
        }
        
        window.testCardSorting = function() {
            if (!gameMainScene || !gameMainScene.playingInteractionManager) {
                log('❌ 出牌交互管理器未初始化', 'error')
                return
            }
            
            try {
                log('🔄 测试卡牌排序功能', 'info')
                gameMainScene.playingInteractionManager.autoSortCards()
                log('✅ 卡牌排序测试完成', 'success')
            } catch (error) {
                log(`❌ 卡牌排序测试失败: ${error.message}`, 'error')
            }
        }
        
        window.testSuggestions = function() {
            if (!gameMainScene || !gameMainScene.playingInteractionManager) {
                log('❌ 出牌交互管理器未初始化', 'error')
                return
            }
            
            try {
                log('💡 测试出牌建议功能', 'info')
                gameMainScene.playingInteractionManager.generatePlaySuggestions()
                log('✅ 出牌建议测试完成', 'success')
            } catch (error) {
                log(`❌ 出牌建议测试失败: ${error.message}`, 'error')
            }
        }
        
        window.testClearSelection = function() {
            if (!gameMainScene || !gameMainScene.playingInteractionManager) {
                log('❌ 出牌交互管理器未初始化', 'error')
                return
            }
            
            try {
                log('🧹 测试清空选择功能', 'info')
                gameMainScene.playingInteractionManager.clearSelection()
                log('✅ 清空选择测试完成', 'success')
            } catch (error) {
                log(`❌ 清空选择测试失败: ${error.message}`, 'error')
            }
        }
        
        window.clearLog = function() {
            document.getElementById('testLog').innerHTML = ''
            updateStatus('日志已清空')
        }
        
        // 页面加载完成后自动初始化
        log('🎮 出牌界面交互测试页面加载完成', 'info')
        updateStatus('准备就绪，点击按钮开始测试')
        
        // 自动初始化
        setTimeout(() => {
            init()
        }, 500)
    </script>
</body>
</html>
