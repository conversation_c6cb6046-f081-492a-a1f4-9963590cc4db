<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌界面演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .demo-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .demo-controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            color: white;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group select,
        .control-group input {
            width: 100%;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            background: #28a745;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .control-btn:hover {
            background: #218838;
        }

        .control-btn.secondary {
            background: #6c757d;
        }

        .control-btn.secondary:hover {
            background: #5a6268;
        }

        /* 内嵌叫牌面板样式 */
        .bidding-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            margin: 0 auto;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 12px;
        }

        .panel-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }

        .timer {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: bold;
            font-size: 16px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .current-status {
            margin-bottom: 16px;
            text-align: center;
        }

        .my-turn {
            background: rgba(40, 167, 69, 0.3);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(40, 167, 69, 0.5);
        }

        .waiting {
            background: rgba(255, 193, 7, 0.3);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(255, 193, 7, 0.5);
        }

        .highest-bid {
            background: rgba(220, 53, 69, 0.3);
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 16px;
            text-align: center;
            border: 1px solid rgba(220, 53, 69, 0.5);
        }

        .special-tip {
            background: rgba(255, 193, 7, 0.9);
            color: #856404;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            border: 1px solid #ffeaa7;
        }

        .bid-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .bid-button {
            padding: 14px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .bid-button:not(.disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .bid-button.disabled {
            cursor: not-allowed;
            opacity: 0.5;
            background: #e9ecef !important;
            color: #6c757d !important;
        }

        .bid-button.no_bid { background: #6c757d; color: white; }
        .bid-button.one_point { background: #28a745; color: white; }
        .bid-button.two_point { background: #ffc107; color: #212529; }
        .bid-button.three_point { background: #dc3545; color: white; }

        .bid-history {
            margin-bottom: 16px;
        }

        .bid-history h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .history-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .rules-tip {
            text-align: center;
            opacity: 0.8;
            font-size: 12px;
            line-height: 1.4;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎴 斗地主叫牌界面演示</h1>
            <p>体验完整的叫牌界面交互效果</p>
        </div>

        <div class="demo-controls">
            <div class="control-group">
                <label>当前场景：</label>
                <select id="scenario">
                    <option value="my_turn">轮到我叫牌</option>
                    <option value="waiting">等待他人叫牌</option>
                    <option value="special_cards">持有特殊牌型</option>
                    <option value="high_bid">已有高叫牌</option>
                </select>
            </div>

            <div class="control-group">
                <label>倒计时（秒）：</label>
                <input type="number" id="timeLimit" value="15" min="1" max="60">
            </div>

            <div class="control-buttons">
                <button class="control-btn" onclick="applyScenario()">应用场景</button>
                <button class="control-btn secondary" onclick="resetDemo()">重置演示</button>
                <button class="control-btn secondary" onclick="simulateTimeout()">模拟超时</button>
            </div>
        </div>

        <div id="biddingPanel" class="bidding-panel">
            <!-- 动态生成内容 -->
        </div>
    </div>

    <script>
        // 演示状态
        let demoState = {
            isMyTurn: true,
            currentBidder: 'south',
            bidHistory: [],
            highestBid: null,
            hasSpecialCards: false,
            specialCardType: '',
            timeLimit: 15,
            timeLeft: 15,
            isCountingDown: false
        }

        let timer = null

        // 叫牌选项配置
        const bidOptions = {
            no_bid: { label: '不叫', value: 'no_bid', color: '#6c757d' },
            one_point: { label: '1分', value: 'one_point', color: '#28a745' },
            two_point: { label: '2分', value: 'two_point', color: '#ffc107' },
            three_point: { label: '3分', value: 'three_point', color: '#dc3545' }
        }

        const seatNames = {
            west: '西家',
            south: '南家', 
            east: '东家'
        }

        // 获取可用叫牌选项
        function getAvailableBids() {
            const currentHighestValue = demoState.highestBid ? getBidValue(demoState.highestBid.option) : 0
            const available = ['no_bid']

            if (currentHighestValue < 1) available.push('one_point')
            if (currentHighestValue < 2) available.push('two_point')
            if (currentHighestValue < 3) available.push('three_point')

            return available
        }

        function getBidValue(bidOption) {
            const values = { no_bid: 0, one_point: 1, two_point: 2, three_point: 3 }
            return values[bidOption] || 0
        }

        // 开始倒计时
        function startCountdown() {
            if (timer) clearInterval(timer)
            
            if (demoState.isMyTurn) {
                demoState.isCountingDown = true
                demoState.timeLeft = demoState.timeLimit
                
                timer = setInterval(() => {
                    demoState.timeLeft--
                    updatePanel()
                    
                    if (demoState.timeLeft <= 0) {
                        clearInterval(timer)
                        handleTimeout()
                    }
                }, 1000)
            }
        }

        // 处理超时
        function handleTimeout() {
            alert('⏰ 叫牌超时，自动选择不叫')
            handleBid('no_bid')
        }

        // 处理叫牌
        function handleBid(bidValue) {
            if (timer) clearInterval(timer)
            
            const bidRecord = {
                player: 'south',
                option: bidValue,
                value: getBidValue(bidValue)
            }
            
            demoState.bidHistory.push(bidRecord)
            
            if (bidValue !== 'no_bid') {
                demoState.highestBid = bidRecord
            }
            
            // 模拟切换到下一个玩家
            demoState.isMyTurn = false
            demoState.currentBidder = 'east'
            demoState.isCountingDown = false
            
            updatePanel()
            
            alert(`您选择了：${bidOptions[bidValue].label}`)
        }

        // 更新面板
        function updatePanel() {
            const panel = document.getElementById('biddingPanel')
            const availableBids = getAvailableBids()
            
            panel.innerHTML = `
                <div class="panel-header">
                    <h3>叫牌阶段</h3>
                    ${demoState.isMyTurn && demoState.isCountingDown ? `
                        <div class="timer" style="color: ${getTimerColor()}">
                            <span>⏰</span>
                            <span>${demoState.timeLeft}秒</span>
                        </div>
                    ` : ''}
                </div>

                <div class="current-status">
                    ${demoState.isMyTurn ? `
                        <div class="my-turn">
                            <span>👆</span>
                            <span>轮到您叫牌了</span>
                        </div>
                    ` : `
                        <div class="waiting">
                            <span>⏳</span>
                            <span>等待 ${seatNames[demoState.currentBidder]} 叫牌...</span>
                        </div>
                    `}
                </div>

                ${demoState.highestBid ? `
                    <div class="highest-bid">
                        <strong>当前最高：</strong>
                        ${seatNames[demoState.highestBid.player]} - ${bidOptions[demoState.highestBid.option].label}
                    </div>
                ` : ''}

                ${demoState.hasSpecialCards ? `
                    <div class="special-tip">
                        <div>⚠️</div>
                        <div>
                            <strong>特殊牌型提示：</strong><br>
                            您持有${demoState.specialCardType}，可以选择不叫分
                        </div>
                    </div>
                ` : ''}

                <div class="bid-buttons">
                    ${Object.keys(bidOptions).map(bidKey => {
                        const option = bidOptions[bidKey]
                        const isAvailable = availableBids.includes(bidKey)
                        const isDisabled = !demoState.isMyTurn || !isAvailable
                        
                        return `
                            <button 
                                class="bid-button ${bidKey} ${isDisabled ? 'disabled' : ''}"
                                onclick="${isDisabled ? '' : `handleBid('${bidKey}')`}"
                                ${isDisabled ? 'disabled' : ''}
                            >
                                ${option.label}
                            </button>
                        `
                    }).join('')}
                </div>

                ${demoState.bidHistory.length > 0 ? `
                    <div class="bid-history">
                        <h4>叫牌记录：</h4>
                        <div class="history-list">
                            ${demoState.bidHistory.map(bid => `
                                <span class="history-item">
                                    ${seatNames[bid.player]}: ${bidOptions[bid.option].label}
                                </span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="rules-tip">
                    <small>💡 叫牌规则：后叫者只能叫比前面更高的分数，或选择不叫</small>
                </div>
            `
        }

        function getTimerColor() {
            if (demoState.timeLeft <= 5) return '#dc3545'
            if (demoState.timeLeft <= 10) return '#ffc107'
            return '#28a745'
        }

        // 应用场景
        function applyScenario() {
            const scenario = document.getElementById('scenario').value
            const timeLimit = parseInt(document.getElementById('timeLimit').value)
            
            demoState.timeLimit = timeLimit
            
            switch (scenario) {
                case 'my_turn':
                    demoState.isMyTurn = true
                    demoState.currentBidder = 'south'
                    demoState.hasSpecialCards = false
                    break
                case 'waiting':
                    demoState.isMyTurn = false
                    demoState.currentBidder = 'west'
                    demoState.hasSpecialCards = false
                    break
                case 'special_cards':
                    demoState.isMyTurn = true
                    demoState.currentBidder = 'south'
                    demoState.hasSpecialCards = true
                    demoState.specialCardType = '双王'
                    break
                case 'high_bid':
                    demoState.isMyTurn = true
                    demoState.currentBidder = 'south'
                    demoState.highestBid = { player: 'west', option: 'two_point' }
                    demoState.bidHistory = [
                        { player: 'west', option: 'one_point' },
                        { player: 'east', option: 'two_point' }
                    ]
                    break
            }
            
            updatePanel()
            if (demoState.isMyTurn) {
                startCountdown()
            }
        }

        function resetDemo() {
            if (timer) clearInterval(timer)
            demoState = {
                isMyTurn: true,
                currentBidder: 'south',
                bidHistory: [],
                highestBid: null,
                hasSpecialCards: false,
                specialCardType: '',
                timeLimit: 15,
                timeLeft: 15,
                isCountingDown: false
            }
            updatePanel()
            startCountdown()
        }

        function simulateTimeout() {
            if (demoState.isMyTurn && demoState.isCountingDown) {
                demoState.timeLeft = 1
                updatePanel()
            }
        }

        // 初始化
        updatePanel()
        startCountdown()
    </script>
</body>
</html>
