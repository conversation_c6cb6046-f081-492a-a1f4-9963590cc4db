<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌逻辑修复测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .game-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin: 10px 0;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="info">
            <h1>🎮 叫牌逻辑修复测试</h1>
            <p>这个测试页面用于验证叫牌逻辑是否正确工作。</p>
            <div id="status" class="status">
                ⏳ 正在初始化游戏...
            </div>
        </div>
        
        <div class="game-container">
            <div id="game"></div>
        </div>
    </div>

    <script type="module">
        import { GameScene } from './src/game/GameScene.js'
        import { BiddingManager } from './src/game/BiddingManager.js'
        import { GameState } from './src/game/GameState.js'

        // 更新状态显示
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status')
            statusEl.textContent = message
            statusEl.className = `status ${type}`
            console.log(`[${type.toUpperCase()}] ${message}`)
        }

        // Phaser游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 1200,
            height: 800,
            parent: 'game',
            backgroundColor: '#1E3A8A',
            scene: {
                preload: preload,
                create: create
            }
        }

        let gameScene
        let biddingManager
        let gameState

        function preload() {
            updateStatus('🎮 游戏场景加载中...', 'info')
            // 简化预加载，只加载必需资源
        }

        function create() {
            updateStatus('🎯 创建游戏场景...', 'info')
            
            try {
                // 创建游戏状态
                gameState = new GameState()
                
                // 创建叫牌管理器
                biddingManager = new BiddingManager()
                
                // 设置事件监听
                setupBiddingEvents()
                
                // 创建简单UI
                createSimpleUI.call(this)
                
                // 开始游戏
                startTestGame()
                
                updateStatus('✅ 游戏初始化完成，点击开始游戏按钮', 'success')
                
            } catch (error) {
                updateStatus(`❌ 游戏初始化失败: ${error.message}`, 'error')
                console.error('游戏初始化错误:', error)
            }
        }

        function setupBiddingEvents() {
            updateStatus('🎯 设置叫牌事件监听器...', 'info')
            
            biddingManager.addEventListener('gameStarted', (data) => {
                updateStatus(`🎮 叫牌游戏开始，首叫者: ${data.firstBidder}`, 'success')
                console.log('叫牌游戏开始:', data)
            })

            biddingManager.addEventListener('stateUpdate', (data) => {
                updateStatus(`📊 叫牌状态更新，当前叫牌者: ${data.biddingStatus.currentBidder}`, 'info')
                console.log('叫牌状态更新:', data)
                
                // 如果轮到玩家叫牌，显示叫牌按钮
                if (data.isMyTurn && data.gameState.phase === 'bidding') {
                    showBiddingButtons(data.availableBids)
                } else {
                    hideBiddingButtons()
                }
            })

            biddingManager.addEventListener('bidMade', (data) => {
                updateStatus(`🎯 ${data.player} 叫牌: ${biddingManager.getBidDisplayName(data.bidOption)}`, 'success')
                console.log('叫牌完成:', data)
            })

            biddingManager.addEventListener('landlordSelected', (data) => {
                updateStatus(`🎉 ${data.landlord} 成为地主！`, 'success')
                console.log('地主选定:', data)
            })

            biddingManager.addEventListener('redealRequired', (data) => {
                updateStatus(`🔄 需要重新发牌: ${data.reason}`, 'error')
                console.log('重新发牌:', data)
            })
        }

        function createSimpleUI() {
            // 创建标题
            this.add.text(600, 50, '叫牌逻辑测试', {
                fontSize: '32px',
                color: '#FFFFFF',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5)

            // 创建开始游戏按钮
            const startButton = this.add.rectangle(600, 150, 200, 60, 0x4CAF50)
            startButton.setStrokeStyle(2, 0xFFFFFF)
            startButton.setInteractive({ useHandCursor: true })

            const startText = this.add.text(600, 150, '开始游戏', {
                fontSize: '18px',
                color: '#FFFFFF',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5)

            startButton.on('pointerdown', () => {
                updateStatus('🎯 开始新游戏...', 'info')
                startTestGame()
            })

            // 创建叫牌按钮容器（初始隐藏）
            window.biddingContainer = this.add.container(600, 400)
            window.biddingContainer.setVisible(false)

            // 创建叫牌按钮
            const buttonData = [
                { text: '不叫', option: 'no_bid', x: -150 },
                { text: '1分', option: 'one_point', x: -50 },
                { text: '2分', option: 'two_point', x: 50 },
                { text: '3分', option: 'three_point', x: 150 }
            ]

            window.biddingButtons = {}

            buttonData.forEach(data => {
                const button = this.add.rectangle(data.x, 0, 80, 40, 0x2196F3)
                button.setStrokeStyle(2, 0xFFFFFF)
                button.setInteractive({ useHandCursor: true })

                const buttonText = this.add.text(data.x, 0, data.text, {
                    fontSize: '16px',
                    color: '#FFFFFF',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5)

                button.on('pointerdown', () => {
                    updateStatus(`🎯 点击叫牌: ${data.text}`, 'info')
                    makeBid(data.option)
                })

                window.biddingContainer.add(button)
                window.biddingContainer.add(buttonText)
                window.biddingButtons[data.option] = { button, text: buttonText }
            })

            // 添加叫牌提示文字
            window.biddingTitle = this.add.text(0, -50, '请选择叫牌', {
                fontSize: '20px',
                color: '#FFD700',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5)
            window.biddingContainer.add(window.biddingTitle)
        }

        function startTestGame() {
            try {
                updateStatus('🎮 启动测试游戏...', 'info')
                
                // 初始化游戏状态
                gameState.startGame()
                
                // 准备玩家数据
                const players = {
                    west: { name: '西家玩家', cards: [] },
                    south: { name: '南家玩家', cards: [] },
                    east: { name: '东家玩家', cards: [] }
                }

                // 启动叫牌管理器
                const firstBidder = biddingManager.initGame(players, 1, 1, 'south')
                
                updateStatus(`🎯 叫牌开始，首叫者: ${firstBidder}`, 'success')
                
            } catch (error) {
                updateStatus(`❌ 启动游戏失败: ${error.message}`, 'error')
                console.error('启动游戏错误:', error)
            }
        }

        function showBiddingButtons(availableBids) {
            updateStatus('🎯 显示叫牌按钮', 'info')
            window.biddingContainer.setVisible(true)
            
            // 更新按钮状态
            Object.entries(window.biddingButtons).forEach(([option, elements]) => {
                const isAvailable = availableBids.includes(option)
                elements.button.setFillStyle(isAvailable ? 0x2196F3 : 0x666666)
                elements.button.setInteractive(isAvailable)
            })
        }

        function hideBiddingButtons() {
            updateStatus('🙈 隐藏叫牌按钮', 'info')
            if (window.biddingContainer) {
                window.biddingContainer.setVisible(false)
            }
        }

        function makeBid(option) {
            try {
                updateStatus(`🎯 执行叫牌: ${option}`, 'info')
                const result = biddingManager.makeBid(option)
                
                if (result.success) {
                    updateStatus(`✅ 叫牌成功: ${biddingManager.getBidDisplayName(option)}`, 'success')
                } else {
                    updateStatus(`❌ 叫牌失败: ${result.reason}`, 'error')
                }
                
            } catch (error) {
                updateStatus(`❌ 叫牌异常: ${error.message}`, 'error')
                console.error('叫牌错误:', error)
            }
        }

        // 启动游戏
        updateStatus('🚀 启动Phaser游戏引擎...', 'info')
        const game = new Phaser.Game(config)

    </script>
</body>
</html>
