// 测试叫牌系统集成逻辑（不依赖Phaser）
import { BiddingManager } from './src/game/BiddingManager.js'

console.log('🎮 开始叫牌系统集成逻辑测试')

// 模拟GameScene中的集成逻辑
class MockGameScene {
  constructor() {
    this.biddingManager = new BiddingManager()
    this.gameState = {
      phase: 'bidding',
      currentPlayer: 'south',
      players: {
        west: { name: '西家玩家', cards: [] },
        south: { name: '南家玩家', cards: [] },
        east: { name: '东家玩家', cards: [] }
      }
    }
    
    this.setupBiddingEvents()
    console.log('✅ 模拟游戏场景初始化完成')
  }
  
  setupBiddingEvents() {
    console.log('🎯 设置叫牌事件监听器')
    
    this.biddingManager.addEventListener('gameStarted', (data) => {
      console.log('🎮 叫牌游戏开始:', data)
      this.onBiddingGameStarted(data)
    })
    
    this.biddingManager.addEventListener('stateUpdate', (data) => {
      console.log('📊 叫牌状态更新:', data.biddingStatus.currentBidder)
      this.onBiddingStateUpdate(data)
    })
    
    this.biddingManager.addEventListener('bidMade', (data) => {
      console.log('🎯 叫牌完成:', data)
      this.onBidMade(data)
    })
    
    this.biddingManager.addEventListener('landlordSelected', (data) => {
      console.log('🎉 地主选定:', data)
      this.onLandlordSelected(data)
    })
    
    this.biddingManager.addEventListener('redealRequired', (data) => {
      console.log('🔄 需要重新发牌:', data)
      this.onRedealRequired(data)
    })
    
    console.log('✅ 叫牌事件监听器设置完成')
  }
  
  // 模拟GameScene中的事件处理方法
  onBiddingGameStarted(data) {
    console.log('🎮 处理叫牌游戏开始事件')
    this.updateBiddingUI(data)
  }
  
  onBiddingStateUpdate(data) {
    console.log('📊 处理叫牌状态更新事件')
    this.updateBiddingUI(data)
  }
  
  onBidMade(data) {
    console.log('🎯 处理叫牌完成事件')
    this.showBidNotification(data.player, data.bidOption)
  }
  
  onLandlordSelected(data) {
    console.log('🎉 处理地主选定事件')
    this.gameState.landlord = this.getSeatToPlayerId(data.landlord)
    this.gameState.phase = 'playing'
    console.log(`✅ 游戏状态更新: 地主=${this.gameState.landlord}, 阶段=${this.gameState.phase}`)
  }
  
  onRedealRequired(data) {
    console.log('🔄 处理重新发牌事件')
    this.gameState.phase = 'waiting'
    console.log('✅ 游戏状态重置为等待阶段')
  }
  
  // 辅助方法
  getSeatToPlayerId(seat) {
    const seatToPlayer = {
      west: 2,
      south: 1,
      east: 3
    }
    return seatToPlayer[seat] || 1
  }
  
  getSeatDisplayName(seat) {
    const seatNames = {
      west: '西家',
      south: '南家',
      east: '东家'
    }
    return seatNames[seat] || seat
  }
  
  updateBiddingUI(data) {
    console.log('🔄 更新叫牌UI')
    if (data.isMyTurn) {
      console.log('🎯 轮到我叫牌，显示叫牌UI')
    } else {
      console.log('🤖 轮到AI叫牌，隐藏叫牌UI')
    }
  }
  
  showBidNotification(player, bidOption) {
    const playerName = this.getSeatDisplayName(player)
    const bidName = this.biddingManager.getBidDisplayName(bidOption)
    console.log(`📢 叫牌通知: ${playerName} ${bidName}`)
  }
  
  // 模拟开始游戏
  startBiddingWithManager() {
    console.log('🎯 启动叫牌管理器')
    
    const players = {
      west: { name: '西家玩家', cards: [] },
      south: { name: '南家玩家', cards: [] },
      east: { name: '东家玩家', cards: [] }
    }
    
    this.biddingManager.initGame(players, 1, 1, 'south')
  }
  
  // 模拟叫牌
  makeBidWithManager(option) {
    console.log(`🎯 通过管理器叫牌: ${option}`)
    const result = this.biddingManager.makeBid(option)
    if (!result.success) {
      console.error('❌ 叫牌失败:', result.reason)
    }
    return result
  }
  
  // 模拟其他玩家叫牌
  simulateOtherPlayerBid(player, option) {
    console.log(`🤖 模拟 ${player} 叫牌: ${option}`)
    const result = this.biddingManager.simulateOtherPlayerBid(player, option)
    if (!result.success) {
      console.error('❌ 模拟叫牌失败:', result.reason)
    }
    return result
  }
}

// 运行测试
async function runIntegrationTest() {
  console.log('🎯 开始集成测试')
  
  // 创建模拟游戏场景
  const mockScene = new MockGameScene()
  
  // 测试1: 启动叫牌
  console.log('\n=== 测试1: 启动叫牌 ===')
  mockScene.startBiddingWithManager()
  
  // 等待一下让事件处理完成
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 测试2: 模拟完整叫牌流程
  console.log('\n=== 测试2: 模拟完整叫牌流程 ===')
  
  // 西家不叫
  await new Promise(resolve => setTimeout(resolve, 200))
  mockScene.simulateOtherPlayerBid('west', 'no_bid')
  
  // 南家叫1分
  await new Promise(resolve => setTimeout(resolve, 200))
  mockScene.makeBidWithManager('one_point')
  
  // 东家叫2分
  await new Promise(resolve => setTimeout(resolve, 200))
  mockScene.simulateOtherPlayerBid('east', 'two_point')
  
  // 西家不叫
  await new Promise(resolve => setTimeout(resolve, 200))
  mockScene.simulateOtherPlayerBid('west', 'no_bid')
  
  // 南家不叫
  await new Promise(resolve => setTimeout(resolve, 200))
  mockScene.makeBidWithManager('no_bid')
  
  // 等待所有事件处理完成
  await new Promise(resolve => setTimeout(resolve, 500))
  
  console.log('\n=== 测试完成 ===')
  console.log('✅ 叫牌系统集成逻辑测试完成')
  console.log('✅ 如果看到地主选定事件，说明集成成功')
}

// 运行测试
runIntegrationTest().catch(error => {
  console.error('❌ 测试失败:', error)
})
