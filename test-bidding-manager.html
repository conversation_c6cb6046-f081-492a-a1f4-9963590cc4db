<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌状态管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .bid-button {
            background: #2196F3;
        }
        
        .bid-button:hover {
            background: #1976D2;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .current-player {
            font-weight: bold;
            color: #FFD700;
        }
        
        .timeout-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .timeout-progress {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
            transition: width 0.1s linear;
        }
        
        .player-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 10px 0;
        }
        
        .player-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .player-card.current {
            background: rgba(255, 215, 0, 0.3);
            border: 2px solid #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 叫牌状态管理器测试</h1>
        
        <div class="section">
            <h3>游戏控制</h3>
            <div class="controls">
                <button onclick="initGame()">开始新游戏</button>
                <button onclick="resetGame()">重置游戏</button>
                <button onclick="simulateGame()">模拟完整游戏</button>
            </div>
            
            <div>
                时间限制: 
                <select id="timeLimit" onchange="updateTimeLimit()">
                    <option value="5">5秒</option>
                    <option value="10">10秒</option>
                    <option value="15" selected>15秒</option>
                    <option value="20">20秒</option>
                    <option value="30">30秒</option>
                </select>
            </div>
        </div>

        <div class="section">
            <h3>当前状态</h3>
            <div id="gameStatus" class="status">等待开始游戏...</div>
            
            <div class="timeout-bar">
                <div id="timeoutProgress" class="timeout-progress" style="width: 100%"></div>
            </div>
            
            <div id="playerInfo" class="player-info"></div>
        </div>

        <div class="section">
            <h3>叫牌操作</h3>
            <div id="biddingControls" class="controls">
                <button class="bid-button" onclick="makeBid('pass')" disabled>不叫</button>
                <button class="bid-button" onclick="makeBid('1')" disabled>1分</button>
                <button class="bid-button" onclick="makeBid('2')" disabled>2分</button>
                <button class="bid-button" onclick="makeBid('3')" disabled>3分</button>
            </div>
        </div>

        <div class="section">
            <h3>其他玩家模拟</h3>
            <div class="controls">
                <button onclick="simulatePlayerBid('west', 'pass')">西家不叫</button>
                <button onclick="simulatePlayerBid('west', '1')">西家叫1分</button>
                <button onclick="simulatePlayerBid('east', 'pass')">东家不叫</button>
                <button onclick="simulatePlayerBid('east', '2')">东家叫2分</button>
            </div>
        </div>

        <div class="section">
            <h3>事件日志</h3>
            <div id="eventLog" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script type="module">
        // 检查模块加载
        console.log('开始加载模块...')

        try {
            const { BiddingManager } = await import('./src/game/BiddingManager.js')
            console.log('BiddingManager 加载成功:', BiddingManager)

            let biddingManager = null
            let timeoutInterval = null
            let remainingTime = 0
        
            // 初始化
            function init() {
                biddingManager = new BiddingManager()

                // 监听所有事件
                biddingManager.addEventListener('gameStarted', onGameStarted)
                biddingManager.addEventListener('stateUpdate', onStateUpdate)
                biddingManager.addEventListener('bidMade', onBidMade)
                biddingManager.addEventListener('bidTimeout', onBidTimeout)
                biddingManager.addEventListener('bidError', onBidError)
                biddingManager.addEventListener('landlordSelected', onLandlordSelected)
                biddingManager.addEventListener('redealRequired', onRedealRequired)
                biddingManager.addEventListener('reset', onReset)

                log('✅ 叫牌管理器初始化完成')
            }
        
        // 事件处理器
        function onGameStarted(data) {
            log(`🎮 游戏开始 - 首叫者: ${data.firstBidder}`)
            updateUI()
        }
        
        function onStateUpdate(state) {
            updateGameStatus(state)
            updateBiddingControls(state)
            updatePlayerInfo(state)
            updateTimeout(state)
        }
        
        function onBidMade(data) {
            const simulated = data.simulated ? ' (模拟)' : ''
            log(`🎯 ${data.player} 叫牌: ${biddingManager.getBidDisplayName(data.bidOption)}${simulated}`)
        }
        
        function onBidTimeout(data) {
            log(`⏰ ${data.player} 超时，自动不叫`)
        }
        
        function onBidError(data) {
            log(`❌ 叫牌错误: ${data.reason}`)
        }
        
        function onLandlordSelected(data) {
            log(`🎉 叫牌结束！${data.landlord} 成为地主`)
            stopTimeout()
        }
        
        function onRedealRequired(data) {
            log(`🔄 ${data.reason}，需要重新发牌`)
            stopTimeout()
        }
        
        function onReset(data) {
            log('🔄 游戏重置')
            stopTimeout()
        }
        
        // UI更新函数
        function updateGameStatus(state) {
            const status = state.biddingStatus
            const gameState = state.gameState
            
            let statusText = `游戏阶段: ${gameState.phase}\n`
            statusText += `轮次: ${gameState.round}-${gameState.gameNumber}\n`
            statusText += `当前叫牌者: ${status.currentBidder || '无'}\n`
            statusText += `是否轮到我: ${state.isMyTurn ? '是' : '否'}\n`
            statusText += `最高叫牌: ${status.highestBid || '无'}\n`
            statusText += `最高叫牌者: ${status.highestBidder || '无'}\n`
            statusText += `叫牌轮次: ${status.round}\n`
            
            if (status.biddingHistory.length > 0) {
                statusText += `\n叫牌历史:\n`
                status.biddingHistory.forEach((bid, index) => {
                    statusText += `${index + 1}. ${bid.player}: ${biddingManager.getBidDisplayName(bid.bidOption)}\n`
                })
            }
            
            document.getElementById('gameStatus').textContent = statusText
        }
        
        function updateBiddingControls(state) {
            const buttons = document.querySelectorAll('.bid-button')
            const isMyTurn = state.isMyTurn && state.gameState.phase === 'bidding'
            const availableBids = state.availableBids || []
            
            buttons.forEach(button => {
                const bidOption = button.onclick.toString().match(/'([^']+)'/)?.[1]
                button.disabled = !isMyTurn || !availableBids.includes(bidOption)
            })
        }
        
        function updatePlayerInfo(state) {
            const playerInfo = document.getElementById('playerInfo')
            const players = ['west', 'south', 'east']
            const status = state.biddingStatus
            
            playerInfo.innerHTML = players.map(seat => {
                const isCurrent = status.currentBidder === seat
                const isMe = seat === state.currentPlayer
                const lastBid = status.biddingHistory
                    .filter(bid => bid.player === seat)
                    .pop()
                
                return `
                    <div class="player-card ${isCurrent ? 'current' : ''}">
                        <div><strong>${getSeatName(seat)}${isMe ? ' (我)' : ''}</strong></div>
                        <div>最后叫牌: ${lastBid ? biddingManager.getBidDisplayName(lastBid.bidOption) : '未叫'}</div>
                        ${isCurrent ? '<div style="color: #FFD700;">⏰ 叫牌中...</div>' : ''}
                    </div>
                `
            }).join('')
        }
        
        function updateTimeout(state) {
            if (state.isMyTurn && state.gameState.phase === 'bidding') {
                startTimeout(state.timeLimit)
            } else {
                stopTimeout()
            }
        }
        
        function startTimeout(timeLimit) {
            stopTimeout()
            remainingTime = timeLimit
            
            timeoutInterval = setInterval(() => {
                remainingTime -= 0.1
                const progress = Math.max(0, (remainingTime / timeLimit) * 100)
                document.getElementById('timeoutProgress').style.width = progress + '%'
                
                if (remainingTime <= 0) {
                    stopTimeout()
                }
            }, 100)
        }
        
        function stopTimeout() {
            if (timeoutInterval) {
                clearInterval(timeoutInterval)
                timeoutInterval = null
            }
            document.getElementById('timeoutProgress').style.width = '100%'
        }
        
        // 全局函数
        window.initGame = function() {
            const players = {
                west: { name: '西家玩家', cards: [] },
                south: { name: '南家玩家', cards: [] },
                east: { name: '东家玩家', cards: [] }
            }
            
            biddingManager.initGame(players, 1, 1, 'south')
            log('🎮 开始新游戏')
        }
        
        window.resetGame = function() {
            biddingManager.reset()
            updateUI()
        }
        
        window.makeBid = function(bidOption) {
            const result = biddingManager.makeBid(bidOption)
            if (!result.success) {
                log(`❌ 叫牌失败: ${result.reason}`)
            }
        }
        
        window.simulatePlayerBid = function(player, bidOption) {
            const result = biddingManager.simulateOtherPlayerBid(player, bidOption)
            if (!result.success) {
                log(`❌ 模拟叫牌失败: ${result.reason}`)
            }
        }
        
        window.simulateGame = function() {
            initGame()
            
            // 模拟一个完整的叫牌过程
            setTimeout(() => simulatePlayerBid('west', 'pass'), 1000)
            setTimeout(() => makeBid('1'), 2000)
            setTimeout(() => simulatePlayerBid('east', '2'), 3000)
            setTimeout(() => makeBid('pass'), 4000)
            setTimeout(() => simulatePlayerBid('west', 'pass'), 5000)
        }
        
        window.updateTimeLimit = function() {
            const timeLimit = parseInt(document.getElementById('timeLimit').value)
            biddingManager.setTimeLimit(timeLimit)
            log(`⏰ 时间限制设置为 ${timeLimit} 秒`)
        }
        
        window.clearLog = function() {
            document.getElementById('eventLog').innerHTML = ''
        }
        
        function updateUI() {
            if (biddingManager) {
                const state = biddingManager.getCurrentState()
                onStateUpdate(state)
            }
        }
        
        function log(message) {
            const logElement = document.getElementById('eventLog')
            const time = new Date().toLocaleTimeString()
            logElement.innerHTML += `[${time}] ${message}\n`
            logElement.scrollTop = logElement.scrollHeight
        }
        
        function getSeatName(seat) {
            const names = { west: '西家', south: '南家', east: '东家' }
            return names[seat] || seat
        }
        
            // 初始化
            init()

        } catch (error) {
            console.error('模块加载失败:', error)
            document.getElementById('eventLog').innerHTML = `
                <div style="color: #ff6b6b;">
                    ❌ 模块加载失败: ${error.message}<br>
                    请确保所有文件都在正确的位置，并且服务器支持ES模块。
                </div>
            `
        }
    </script>
</body>
</html>
