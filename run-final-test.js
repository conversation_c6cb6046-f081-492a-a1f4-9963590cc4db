// 叫牌系统最终测试套件
import { BiddingManager } from './src/game/BiddingManager.js'

console.log('🎮 叫牌系统最终测试套件')
console.log('='.repeat(50))

let testResults = []

// 辅助函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
}

function logTest(message, type = 'info') {
    const symbols = {
        info: '🔵',
        success: '✅',
        error: '❌',
        warning: '⚠️'
    }
    console.log(`${symbols[type]} ${message}`)
}

// 测试1: 基础功能完整性测试
async function testBasicFunctionality() {
    logTest('测试1: 基础功能完整性', 'info')
    
    try {
        const biddingManager = new BiddingManager()
        let events = []
        
        // 监听所有事件
        const eventTypes = ['gameStarted', 'stateUpdate', 'bidMade', 'bidTimeout', 'landlordSelected', 'redealRequired']
        eventTypes.forEach(eventType => {
            biddingManager.addEventListener(eventType, (data) => {
                events.push({ type: eventType, data })
            })
        })
        
        // 初始化游戏
        const players = {
            west: { name: '西家', cards: [] },
            south: { name: '南家', cards: [] },
            east: { name: '东家', cards: [] }
        }
        
        const firstBidder = biddingManager.initGame(players, 1, 1, 'south')
        await sleep(100)
        
        // 检查初始化
        if (!firstBidder) {
            throw new Error('游戏初始化失败')
        }
        
        // 模拟完整叫牌流程
        biddingManager.simulateOtherPlayerBid('west', 'no_bid')
        await sleep(100)
        
        biddingManager.makeBid('one_point')
        await sleep(100)
        
        biddingManager.simulateOtherPlayerBid('east', 'two_point')
        await sleep(100)
        
        // 验证事件
        const gameStartedEvents = events.filter(e => e.type === 'gameStarted')
        const bidMadeEvents = events.filter(e => e.type === 'bidMade')
        const landlordSelectedEvents = events.filter(e => e.type === 'landlordSelected')
        
        if (gameStartedEvents.length === 0) {
            throw new Error('gameStarted事件未触发')
        }
        
        if (bidMadeEvents.length < 3) {
            throw new Error(`叫牌事件数量不足: ${bidMadeEvents.length}/3`)
        }
        
        if (landlordSelectedEvents.length === 0) {
            throw new Error('landlordSelected事件未触发')
        }
        
        // 验证最终状态
        const finalState = biddingManager.getCurrentState()
        if (finalState.gameState.phase !== 'completed') {
            throw new Error(`游戏阶段错误: ${finalState.gameState.phase}`)
        }
        
        biddingManager.destroy()
        
        logTest('基础功能测试通过', 'success')
        testResults.push({ test: '基础功能', result: 'PASS' })
        
    } catch (error) {
        logTest(`基础功能测试失败: ${error.message}`, 'error')
        testResults.push({ test: '基础功能', result: 'FAIL', error: error.message })
    }
}

// 测试2: 超时处理测试
async function testTimeoutHandling() {
    logTest('测试2: 超时处理', 'info')
    
    try {
        const biddingManager = new BiddingManager()
        let timeoutTriggered = false
        
        biddingManager.addEventListener('bidTimeout', (data) => {
            timeoutTriggered = true
            logTest(`超时事件触发: ${data.player}`, 'success')
        })
        
        // 设置短超时时间
        biddingManager.setTimeLimit(1)
        
        const players = {
            west: { name: '西家', cards: [] },
            south: { name: '南家', cards: [] },
            east: { name: '东家', cards: [] }
        }
        
        biddingManager.initGame(players, 1, 1, 'south')
        
        // 等待超时
        await sleep(1500)
        
        if (!timeoutTriggered) {
            throw new Error('超时事件未触发')
        }
        
        biddingManager.destroy()
        
        logTest('超时处理测试通过', 'success')
        testResults.push({ test: '超时处理', result: 'PASS' })
        
    } catch (error) {
        logTest(`超时处理测试失败: ${error.message}`, 'error')
        testResults.push({ test: '超时处理', result: 'FAIL', error: error.message })
    }
}

// 测试3: 特殊规则测试
async function testSpecialRules() {
    logTest('测试3: 特殊规则', 'info')
    
    try {
        const biddingManager = new BiddingManager()
        let landlordSelected = false
        let landlordData = null
        
        biddingManager.addEventListener('landlordSelected', (data) => {
            landlordSelected = true
            landlordData = data
        })
        
        const players = {
            west: { name: '西家', cards: [] },
            south: { name: '南家', cards: [] },
            east: { name: '东家', cards: [] }
        }
        
        biddingManager.initGame(players, 1, 1, 'south')
        await sleep(100)
        
        // 西家不叫
        biddingManager.simulateOtherPlayerBid('west', 'no_bid')
        await sleep(100)
        
        // 南家叫3分（应该立即结束）
        biddingManager.makeBid('three_point')
        await sleep(100)
        
        if (!landlordSelected) {
            throw new Error('3分叫牌未立即结束游戏')
        }
        
        if (landlordData.landlord !== 'south') {
            throw new Error(`地主错误: 期望south，实际${landlordData.landlord}`)
        }
        
        biddingManager.destroy()
        
        logTest('特殊规则测试通过', 'success')
        testResults.push({ test: '特殊规则', result: 'PASS' })
        
    } catch (error) {
        logTest(`特殊规则测试失败: ${error.message}`, 'error')
        testResults.push({ test: '特殊规则', result: 'FAIL', error: error.message })
    }
}

// 测试4: 重新发牌测试
async function testRedealScenario() {
    logTest('测试4: 重新发牌场景', 'info')
    
    try {
        const biddingManager = new BiddingManager()
        let redealRequired = false
        
        biddingManager.addEventListener('redealRequired', (data) => {
            redealRequired = true
            logTest(`重新发牌事件: ${data.reason}`, 'success')
        })
        
        const players = {
            west: { name: '西家', cards: [] },
            south: { name: '南家', cards: [] },
            east: { name: '东家', cards: [] }
        }
        
        biddingManager.initGame(players, 1, 1, 'south')
        await sleep(100)
        
        // 所有人都不叫
        biddingManager.simulateOtherPlayerBid('west', 'no_bid')
        await sleep(100)
        
        biddingManager.makeBid('no_bid')
        await sleep(100)
        
        biddingManager.simulateOtherPlayerBid('east', 'no_bid')
        await sleep(100)
        
        if (!redealRequired) {
            throw new Error('重新发牌事件未触发')
        }
        
        biddingManager.destroy()
        
        logTest('重新发牌测试通过', 'success')
        testResults.push({ test: '重新发牌', result: 'PASS' })
        
    } catch (error) {
        logTest(`重新发牌测试失败: ${error.message}`, 'error')
        testResults.push({ test: '重新发牌', result: 'FAIL', error: error.message })
    }
}

// 测试5: 状态一致性测试
async function testStateConsistency() {
    logTest('测试5: 状态一致性', 'info')
    
    try {
        const biddingManager = new BiddingManager()
        
        const players = {
            west: { name: '西家', cards: [] },
            south: { name: '南家', cards: [] },
            east: { name: '东家', cards: [] }
        }
        
        biddingManager.initGame(players, 1, 1, 'south')
        
        // 检查初始状态
        let state = biddingManager.getCurrentState()
        if (state.gameState.phase !== 'bidding') {
            throw new Error(`初始阶段错误: ${state.gameState.phase}`)
        }
        
        if (!state.biddingStatus.currentBidder) {
            throw new Error('当前叫牌者未设置')
        }
        
        // 进行叫牌并检查状态变化
        biddingManager.simulateOtherPlayerBid('west', 'one_point')
        await sleep(200)

        state = biddingManager.getCurrentState()
        console.log('状态检查:', {
            phase: state.gameState.phase,
            currentBidder: state.biddingStatus.currentBidder,
            historyLength: state.biddingStatus.biddingHistory ? state.biddingStatus.biddingHistory.length : 'undefined'
        })

        const history = state.biddingStatus.biddingHistory
        if (!history || history.length === 0) {
            throw new Error('叫牌历史未更新')
        }
        
        if (!state.biddingStatus.highestBid) {
            throw new Error('最高叫牌未更新')
        }
        
        biddingManager.destroy()
        
        logTest('状态一致性测试通过', 'success')
        testResults.push({ test: '状态一致性', result: 'PASS' })
        
    } catch (error) {
        logTest(`状态一致性测试失败: ${error.message}`, 'error')
        testResults.push({ test: '状态一致性', result: 'FAIL', error: error.message })
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('开始运行叫牌系统完整测试套件...\n')
    
    await testBasicFunctionality()
    await sleep(200)
    
    await testTimeoutHandling()
    await sleep(200)
    
    await testSpecialRules()
    await sleep(200)
    
    await testRedealScenario()
    await sleep(200)
    
    await testStateConsistency()
    await sleep(200)
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50))
    console.log('🎯 测试结果汇总')
    console.log('='.repeat(50))
    
    const passCount = testResults.filter(r => r.result === 'PASS').length
    const totalCount = testResults.length
    
    testResults.forEach((result, index) => {
        const status = result.result === 'PASS' ? '✅' : '❌'
        console.log(`${index + 1}. ${result.test}: ${status} ${result.result}`)
        if (result.error) {
            console.log(`   错误: ${result.error}`)
        }
    })
    
    const passRate = Math.round(passCount / totalCount * 100)
    console.log(`\n📊 测试通过率: ${passCount}/${totalCount} (${passRate}%)`)
    
    if (passRate === 100) {
        console.log('🎉 所有测试通过！叫牌系统集成完全成功！')
    } else if (passRate >= 80) {
        console.log('⚠️ 大部分测试通过，但仍有问题需要解决')
    } else {
        console.log('❌ 测试失败较多，需要重点检查问题')
    }
    
    console.log('\n✅ 叫牌系统最终测试完成')
}

// 运行测试
runAllTests().catch(error => {
    console.error('❌ 测试套件执行失败:', error)
})
