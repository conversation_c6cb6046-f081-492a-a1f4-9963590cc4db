{"frameRate": 30, "armature": [{"ik": [], "defaultActions": [{"gotoAndPlay": "win"}], "type": "MovieClip", "frameRate": 30, "skin": [], "bone": [{"name": "root", "transform": {}}], "animation": [{"ik": [], "frame": [], "playTimes": 0, "bone": [{"frame": [{"duration": 0, "transform": {}}], "name": "root"}], "duration": 0, "slot": [], "name": "win", "ffd": []}, {"ik": [], "frame": [], "playTimes": 0, "bone": [{"frame": [{"duration": 0, "transform": {}}], "name": "root"}], "duration": 0, "slot": [], "name": "lose", "ffd": []}], "slot": [], "name": "MovieClip", "aabb": {"width": 0, "y": 0, "height": 0, "x": 0}}, {"ik": [], "defaultActions": [{"gotoAndPlay": "win_1"}], "type": "Armature", "frameRate": 30, "skin": [{"slot": [{"display": [{"path": "win/shen", "type": "image", "name": "win/shen", "transform": {"y": -2.6419, "skX": 90.5796, "skY": 90.5796, "x": 25.52805}}], "name": "shen"}, {"display": [{"path": "win/win_light_effect0007", "type": "image", "name": "win/win_light_effect0007", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0007"}, {"display": [{"path": "win/win_light_effect0008", "type": "image", "name": "win/win_light_effect0008", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0008"}, {"display": [{"path": "win/win_light_effect0000", "type": "image", "name": "win/win_light_effect0000", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0000"}, {"display": [{"path": "win/win_light_effect0010", "type": "image", "name": "win/win_light_effect0010", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0010"}, {"display": [{"path": "win/win_light_effect0017", "type": "image", "name": "win/win_light_effect0017", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0017"}, {"display": [{"path": "win/win_light_effect0003", "type": "image", "name": "win/win_light_effect0003", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0003"}, {"display": [{"path": "win/win_light_effect0011", "type": "image", "name": "win/win_light_effect0011", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0011"}, {"display": [{"path": "win/win_light_effect0012", "type": "image", "name": "win/win_light_effect0012", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0012"}, {"display": [{"path": "win/win_light_effect0005", "type": "image", "name": "win/win_light_effect0005", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0005"}, {"display": [{"path": "win/win_light_effect0001", "type": "image", "name": "win/win_light_effect0001", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0001"}, {"display": [{"path": "win/li", "type": "image", "name": "win/li", "transform": {"y": -2.05, "skX": 90, "skY": 90, "x": 24.95}}], "name": "li"}, {"display": [{"path": "win/win_light_effect_last", "type": "image", "name": "win/win_light_effect_last", "transform": {"skX": -90, "skY": -90}}], "name": "win_light_effect_last"}, {"display": [{"path": "win/win_light_effect0014", "type": "image", "name": "win/win_light_effect0014", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0014"}, {"display": [{"path": "win/win_light_effect0015", "type": "image", "name": "win/win_light_effect0015", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0015"}, {"display": [{"path": "win/win_light_effect0009", "type": "image", "name": "win/win_light_effect0009", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0009"}, {"display": [{"path": "win/win_light_effect0002", "type": "image", "name": "win/win_light_effect0002", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0002"}, {"display": [{"path": "win/win_light_effect0013", "type": "image", "name": "win/win_light_effect0013", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0013"}, {"display": [{"path": "win/win_light_effect0004", "type": "image", "name": "win/win_light_effect0004", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0004"}, {"display": [{"path": "win/caidai", "type": "image", "name": "win/caidai", "transform": {"y": 10.425, "x": 172.67395}}], "name": "caidai"}, {"display": [{"path": "win/win_light_effect0016", "type": "image", "name": "win/win_light_effect0016", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0016"}, {"display": [{"path": "win/win_light_effect0006", "type": "image", "name": "win/win_light_effect0006", "transform": {"y": 1.1991, "skX": 88.829, "skY": 88.829, "scX": 1.85, "scY": 1.85, "x": -90.4694}}], "name": "win_light_effect0006"}], "name": ""}], "bone": [{"name": "root", "transform": {}}, {"length": 63.5, "transform": {"y": -90.475, "skX": -88.829, "skY": -88.829, "x": 0.65}, "parent": "root", "name": "bone"}, {"length": 130.5, "transform": {"skX": 90, "skY": 90}, "parent": "root", "name": "win_light_effect_last"}, {"length": 51.5, "transform": {"y": -23.27315, "skX": -1.7505, "skY": -1.7505, "x": -96.97085}, "parent": "bone", "name": "shen"}, {"length": 51.5, "transform": {"y": 31.3542, "skX": -1.171, "skY": -1.171, "x": -95.30415}, "parent": "bone", "name": "li"}, {"length": 348.5, "transform": {"y": -170.5249, "skX": 88.829, "skY": 88.829, "x": -136.3133}, "parent": "bone", "name": "bone1"}], "canvas": {"width": 1218, "y": 0, "height": 562.5, "color": 4276545, "x": 0}, "animation": [{"ik": [], "frame": [], "playTimes": 0, "bone": [{"frame": [{"duration": 60, "transform": {}}], "name": "root"}, {"frame": [{"duration": 15, "curve": [0.43202416918429004, 0.0071599045346062064, 1, 0.3842482100238663], "transform": {"y": -37.06445}}, {"tweenEasing": 0, "duration": 45, "transform": {}}, {"duration": 0, "transform": {}}], "name": "bone"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 3, "transform": {"skX": 8.001, "skY": 8.001, "x": 13.9515}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {"skX": -3.999, "skY": -3.999, "x": 3.81015}}, {"duration": 36, "transform": {}}], "name": "li"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 3, "transform": {"skX": -5.9995, "skY": -5.9995, "x": 13.79675}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {"skX": 2.0005, "skY": 2.0005, "x": 2.2855}}, {"duration": 36, "transform": {}}], "name": "shen"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 5, "transform": {}}, {"tweenEasing": 0, "duration": 5, "transform": {"scY": 1.2, "x": -5.1974}}, {"tweenEasing": 0, "duration": 5, "transform": {"scY": 0.85, "x": 4.0897}}, {"duration": 30, "transform": {}}], "name": "bone1"}, {"frame": [{"duration": 15, "curve": [0.5015105740181269, 0.007159904534606206, 0.9939577039274925, 0.3985680190930787], "transform": {"y": 40.98195}}, {"duration": 45, "transform": {"skX": 22.5, "skY": 22.5}}, {"duration": 0, "transform": {"skX": -270, "skY": -270}}], "name": "win_light_effect_last"}], "duration": 60, "slot": [{"frame": [{"tweenEasing": 0, "duration": 15, "color": {"aM": 0}}, {"tweenEasing": null, "duration": 45}], "name": "li"}, {"frame": [{"tweenEasing": 0, "duration": 15, "color": {"aM": 0}}, {"tweenEasing": null, "duration": 45}], "name": "caidai"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0000"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0017"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0016"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0015"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0014"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0013"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0012"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0011"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0010"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0009"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0008"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0007"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0006"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0005"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0004"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0003"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0002"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "win_light_effect0001"}, {"frame": [{"duration": 14, "curve": [0.4954682779456194, 0.007159904534606206, 0.9939577039274925, 0.40572792362768495], "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": null, "duration": 45, "color": {"aM": 0}}], "name": "win_light_effect_last"}, {"frame": [{"tweenEasing": 0, "duration": 15, "color": {"aM": 0}}, {"tweenEasing": null, "duration": 45}], "name": "shen"}], "name": "win_1", "ffd": []}, {"ik": [], "frame": [], "playTimes": 0, "bone": [{"frame": [{"duration": 60, "transform": {}}], "name": "root"}, {"frame": [{"duration": 15, "curve": [0.43202416918429004, 0.0071599045346062064, 1, 0.3842482100238663], "transform": {"y": -37.06445}}, {"tweenEasing": 0, "duration": 45, "transform": {}}, {"duration": 0, "transform": {}}], "name": "bone"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 3, "transform": {"skX": 8.001, "skY": 8.001, "x": 13.9515}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {"skX": -3.999, "skY": -3.999, "x": 3.81015}}, {"duration": 36, "transform": {}}], "name": "li"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 3, "transform": {"skX": -5.9995, "skY": -5.9995, "x": 13.79675}}, {"tweenEasing": 0, "duration": 2, "transform": {}}, {"tweenEasing": 0, "duration": 2, "transform": {"skX": 2.0005, "skY": 2.0005, "x": 2.2855}}, {"duration": 36, "transform": {}}], "name": "shen"}, {"frame": [{"duration": 15, "transform": {}}, {"tweenEasing": 0, "duration": 5, "transform": {}}, {"tweenEasing": 0, "duration": 5, "transform": {"scY": 1.2, "x": -5.1974}}, {"tweenEasing": 0, "duration": 5, "transform": {"scY": 0.85, "x": 4.0897}}, {"duration": 30, "transform": {}}], "name": "bone1"}, {"frame": [{"tweenEasing": 0, "duration": 15, "transform": {"skX": 22.5, "skY": 22.5}}, {"tweenEasing": 0, "duration": 45, "transform": {"skX": 22.5, "skY": 22.5}}, {"duration": 0, "transform": {"skX": -270, "skY": -270}}], "name": "win_light_effect_last"}], "duration": 60, "slot": [{"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "li"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "caidai"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 34, "color": {"aM": 0}}], "name": "win_light_effect0000"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 34, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 0, "color": {"aM": 0}}], "name": "win_light_effect0017"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 32, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 2, "color": {"aM": 0}}], "name": "win_light_effect0016"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 30, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 4, "color": {"aM": 0}}], "name": "win_light_effect0015"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 28, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 6, "color": {"aM": 0}}], "name": "win_light_effect0014"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 26, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 8, "color": {"aM": 0}}], "name": "win_light_effect0013"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 24, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 10, "color": {"aM": 0}}], "name": "win_light_effect0012"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 22, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 12, "color": {"aM": 0}}], "name": "win_light_effect0011"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 14, "color": {"aM": 0}}], "name": "win_light_effect0010"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 18, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 16, "color": {"aM": 0}}], "name": "win_light_effect0009"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 16, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 18, "color": {"aM": 0}}], "name": "win_light_effect0008"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 14, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 20, "color": {"aM": 0}}], "name": "win_light_effect0007"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 12, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 22, "color": {"aM": 0}}], "name": "win_light_effect0006"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 10, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 24, "color": {"aM": 0}}], "name": "win_light_effect0005"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 8, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 26, "color": {"aM": 0}}], "name": "win_light_effect0004"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 6, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 28, "color": {"aM": 0}}], "name": "win_light_effect0003"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 4, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 30, "color": {"aM": 0}}], "name": "win_light_effect0002"}, {"frame": [{"tweenEasing": 0, "duration": 20, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2, "color": {"aM": 0}}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 32, "color": {"aM": 0}}], "name": "win_light_effect0001"}, {"frame": [{"tweenEasing": null, "duration": 60}], "name": "win_light_effect_last"}, {"frame": [{"tweenEasing": null, "duration": 60, "color": {"aM": 0}}], "name": "shen"}], "name": "win_2", "ffd": []}], "slot": [{"color": {}, "name": "win_light_effect_last", "parent": "win_light_effect_last"}, {"z": 1, "color": {}, "name": "win_light_effect0017", "parent": "bone"}, {"z": 2, "color": {}, "name": "win_light_effect0000", "parent": "bone"}, {"z": 3, "color": {}, "name": "win_light_effect0016", "parent": "bone"}, {"z": 4, "color": {}, "name": "win_light_effect0015", "parent": "bone"}, {"z": 5, "color": {}, "name": "win_light_effect0014", "parent": "bone"}, {"z": 6, "color": {}, "name": "win_light_effect0013", "parent": "bone"}, {"z": 7, "color": {}, "name": "win_light_effect0012", "parent": "bone"}, {"z": 8, "color": {}, "name": "win_light_effect0011", "parent": "bone"}, {"z": 9, "color": {}, "name": "win_light_effect0010", "parent": "bone"}, {"z": 10, "color": {}, "name": "win_light_effect0009", "parent": "bone"}, {"z": 11, "color": {}, "name": "win_light_effect0008", "parent": "bone"}, {"z": 12, "color": {}, "name": "win_light_effect0007", "parent": "bone"}, {"z": 13, "color": {}, "name": "win_light_effect0006", "parent": "bone"}, {"z": 14, "color": {}, "name": "win_light_effect0005", "parent": "bone"}, {"z": 15, "color": {}, "name": "win_light_effect0004", "parent": "bone"}, {"z": 16, "color": {}, "name": "win_light_effect0003", "parent": "bone"}, {"z": 17, "color": {}, "name": "win_light_effect0002", "parent": "bone"}, {"z": 18, "color": {}, "name": "win_light_effect0001", "parent": "bone"}, {"z": 19, "color": {}, "name": "caidai", "parent": "bone1"}, {"z": 20, "color": {}, "name": "shen", "parent": "shen"}, {"z": 21, "color": {}, "name": "li", "parent": "li"}], "name": "Armaturewin", "aabb": {"width": 345.5, "y": -137.75, "height": 275.5, "x": -172.70107278672296}}], "isGlobal": 0, "name": "you_win", "version": "4.5"}