// 增强出牌规则验证测试
import { EnhancedPlayValidator } from './src/game/EnhancedPlayValidator.js'
import { CardPattern } from './src/game/CardPattern.js'
import { Card } from './src/game/Card.js'

console.log('🛡️ 开始增强出牌规则验证测试')

// 创建测试用的卡牌
function createCard(suit, rank, value) {
  return new Card(suit, rank, value)
}

// 测试用例数据
const testCases = {
  // 有效牌型测试
  validPlays: [
    {
      name: '单牌',
      cards: [createCard('hearts', '7', 7)],
      lastPattern: null,
      expectedValid: true
    },
    {
      name: '对子',
      cards: [
        createCard('hearts', '8', 8),
        createCard('spades', '8', 8)
      ],
      lastPattern: null,
      expectedValid: true
    },
    {
      name: '三张',
      cards: [
        createCard('hearts', '9', 9),
        createCard('spades', '9', 9),
        createCard('clubs', '9', 9)
      ],
      lastPattern: null,
      expectedValid: true
    },
    {
      name: '炸弹',
      cards: [
        createCard('hearts', '10', 10),
        createCard('spades', '10', 10),
        createCard('clubs', '10', 10),
        createCard('diamonds', '10', 10)
      ],
      lastPattern: null,
      expectedValid: true
    },
    {
      name: '火箭',
      cards: [
        createCard('joker', 'small_joker', 16),
        createCard('joker', 'big_joker', 17)
      ],
      lastPattern: null,
      expectedValid: true
    }
  ],
  
  // 无效牌型测试
  invalidPlays: [
    {
      name: '空牌',
      cards: [],
      lastPattern: null,
      expectedValid: false,
      expectedError: 'invalid_pattern'
    },
    {
      name: '无效对子',
      cards: [
        createCard('hearts', '7', 7),
        createCard('spades', '8', 8)
      ],
      lastPattern: null,
      expectedValid: false,
      expectedError: 'invalid_pattern'
    },
    {
      name: '无效三张',
      cards: [
        createCard('hearts', '7', 7),
        createCard('spades', '8', 8),
        createCard('clubs', '9', 9)
      ],
      lastPattern: null,
      expectedValid: false,
      expectedError: 'invalid_pattern'
    }
  ],
  
  // 跟牌测试
  followPlays: [
    {
      name: '单牌压单牌（成功）',
      cards: [createCard('hearts', '9', 9)],
      lastPattern: {
        type: 'single',
        cards: [createCard('spades', '7', 7)],
        weight: 1007
      },
      expectedValid: true
    },
    {
      name: '单牌压单牌（失败）',
      cards: [createCard('hearts', '6', 6)],
      lastPattern: {
        type: 'single',
        cards: [createCard('spades', '7', 7)],
        weight: 1007
      },
      expectedValid: false,
      expectedError: 'insufficient_weight'
    },
    {
      name: '炸弹压单牌',
      cards: [
        createCard('hearts', '5', 5),
        createCard('spades', '5', 5),
        createCard('clubs', '5', 5),
        createCard('diamonds', '5', 5)
      ],
      lastPattern: {
        type: 'single',
        cards: [createCard('spades', 'A', 14)],
        weight: 1014
      },
      expectedValid: true
    },
    {
      name: '火箭压炸弹',
      cards: [
        createCard('joker', 'small_joker', 16),
        createCard('joker', 'big_joker', 17)
      ],
      lastPattern: {
        type: 'bomb',
        cards: [
          createCard('hearts', 'K', 13),
          createCard('spades', 'K', 13),
          createCard('clubs', 'K', 13),
          createCard('diamonds', 'K', 13)
        ],
        weight: 9013
      },
      expectedValid: true
    },
    {
      name: '牌数不匹配',
      cards: [
        createCard('hearts', '9', 9),
        createCard('spades', '9', 9)
      ],
      lastPattern: {
        type: 'single',
        cards: [createCard('spades', '7', 7)],
        weight: 1007
      },
      expectedValid: false,
      expectedError: 'card_count_mismatch'
    }
  ]
}

// 运行测试
async function runValidationTests() {
  console.log('🎯 开始运行增强验证测试')
  
  const cardPattern = new CardPattern()
  const validator = new EnhancedPlayValidator(cardPattern)
  
  let totalTests = 0
  let passedTests = 0
  
  // 测试有效牌型
  console.log('\n=== 测试有效牌型 ===')
  for (const testCase of testCases.validPlays) {
    totalTests++
    console.log(`\n🧪 测试: ${testCase.name}`)
    
    const result = validator.validatePlay(testCase.cards, testCase.lastPattern)
    
    if (result.valid === testCase.expectedValid) {
      console.log('✅ 测试通过')
      passedTests++
    } else {
      console.log('❌ 测试失败')
      console.log(`   期望: ${testCase.expectedValid}, 实际: ${result.valid}`)
      console.log(`   消息: ${result.message}`)
    }
  }
  
  // 测试无效牌型
  console.log('\n=== 测试无效牌型 ===')
  for (const testCase of testCases.invalidPlays) {
    totalTests++
    console.log(`\n🧪 测试: ${testCase.name}`)
    
    const result = validator.validatePlay(testCase.cards, testCase.lastPattern)
    
    const validMatch = result.valid === testCase.expectedValid
    const errorMatch = !testCase.expectedError || result.errorType === testCase.expectedError
    
    if (validMatch && errorMatch) {
      console.log('✅ 测试通过')
      console.log(`   错误类型: ${result.errorType}`)
      console.log(`   错误消息: ${result.message}`)
      passedTests++
    } else {
      console.log('❌ 测试失败')
      console.log(`   期望有效性: ${testCase.expectedValid}, 实际: ${result.valid}`)
      console.log(`   期望错误: ${testCase.expectedError}, 实际: ${result.errorType}`)
    }
  }
  
  // 测试跟牌规则
  console.log('\n=== 测试跟牌规则 ===')
  for (const testCase of testCases.followPlays) {
    totalTests++
    console.log(`\n🧪 测试: ${testCase.name}`)
    
    const result = validator.validatePlay(testCase.cards, testCase.lastPattern)
    
    const validMatch = result.valid === testCase.expectedValid
    const errorMatch = !testCase.expectedError || result.errorType === testCase.expectedError
    
    if (validMatch && errorMatch) {
      console.log('✅ 测试通过')
      if (result.valid) {
        console.log(`   成功消息: ${result.message}`)
      } else {
        console.log(`   错误类型: ${result.errorType}`)
        console.log(`   错误消息: ${result.message}`)
      }
      passedTests++
    } else {
      console.log('❌ 测试失败')
      console.log(`   期望有效性: ${testCase.expectedValid}, 实际: ${result.valid}`)
      if (testCase.expectedError) {
        console.log(`   期望错误: ${testCase.expectedError}, 实际: ${result.errorType}`)
      }
    }
  }
  
  // 测试详细错误信息
  console.log('\n=== 测试详细错误信息 ===')
  totalTests++
  
  const invalidCards = [
    createCard('hearts', '3', 3),
    createCard('spades', '5', 5),
    createCard('clubs', '7', 7)
  ]
  
  const detailResult = validator.validatePlay(invalidCards, null)
  
  if (!detailResult.valid && detailResult.details && detailResult.details.length > 0) {
    console.log('✅ 详细错误信息测试通过')
    console.log(`   详细信息: ${detailResult.details}`)
    passedTests++
  } else {
    console.log('❌ 详细错误信息测试失败')
    console.log(`   结果: ${JSON.stringify(detailResult, null, 2)}`)
  }
  
  // 测试特殊情况处理
  console.log('\n=== 测试特殊情况处理 ===')
  totalTests++
  
  // 测试null输入
  const nullResult = validator.validatePlay(null, null)
  if (!nullResult.valid && nullResult.errorType === 'invalid_pattern') {
    console.log('✅ null输入处理测试通过')
    passedTests++
  } else {
    console.log('❌ null输入处理测试失败')
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50))
  console.log('🎯 增强验证测试结果汇总')
  console.log('='.repeat(50))
  
  const passRate = Math.round(passedTests / totalTests * 100)
  console.log(`📊 测试通过率: ${passedTests}/${totalTests} (${passRate}%)`)
  
  if (passRate === 100) {
    console.log('🎉 所有增强验证测试通过！')
  } else if (passRate >= 80) {
    console.log('⚠️ 大部分测试通过，但仍有问题需要解决')
  } else {
    console.log('❌ 测试失败较多，需要重点检查问题')
  }
  
  console.log('\n✅ 增强出牌规则验证测试完成')
  
  // 测试性能
  console.log('\n=== 性能测试 ===')
  const performanceTestCards = [createCard('hearts', '7', 7)]
  const iterations = 10000
  
  const startTime = performance.now()
  for (let i = 0; i < iterations; i++) {
    validator.validatePlay(performanceTestCards, null)
  }
  const endTime = performance.now()
  
  const avgTime = (endTime - startTime) / iterations
  console.log(`📊 平均验证时间: ${avgTime.toFixed(4)}ms/次`)
  console.log(`🚀 总验证时间: ${(endTime - startTime).toFixed(2)}ms (${iterations}次)`)
}

// 运行测试
runValidationTests().catch(error => {
  console.error('❌ 测试执行失败:', error)
})
