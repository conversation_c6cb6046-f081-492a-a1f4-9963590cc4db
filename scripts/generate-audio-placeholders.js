// 音频占位文件生成器
// 使用Web Audio API生成简单的音频文件用于测试

class AudioPlaceholderGenerator {
  constructor() {
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
    this.sampleRate = 44100
  }

  // 生成正弦波音频
  generateSineWave(frequency, duration, volume = 0.3) {
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      data[i] = Math.sin(2 * Math.PI * frequency * i / this.sampleRate) * volume
    }

    return buffer
  }

  // 生成和弦音频
  generateChord(frequencies, duration, volume = 0.2) {
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      let sample = 0
      frequencies.forEach(freq => {
        sample += Math.sin(2 * Math.PI * freq * i / this.sampleRate)
      })
      data[i] = (sample / frequencies.length) * volume
    }

    return buffer
  }

  // 生成噪音音效
  generateNoise(duration, volume = 0.1) {
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      data[i] = (Math.random() * 2 - 1) * volume
    }

    return buffer
  }

  // 生成点击音效
  generateClick() {
    const duration = 0.1
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      const t = i / this.sampleRate
      const envelope = Math.exp(-t * 20) // 快速衰减
      data[i] = Math.sin(2 * Math.PI * 800 * t) * envelope * 0.3
    }

    return buffer
  }

  // 生成卡牌音效
  generateCardSound() {
    const duration = 0.2
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      const t = i / this.sampleRate
      const envelope = Math.exp(-t * 10)
      const freq = 400 + Math.sin(t * 50) * 100 // 频率调制
      data[i] = Math.sin(2 * Math.PI * freq * t) * envelope * 0.2
    }

    return buffer
  }

  // 生成胜利音效
  generateVictory() {
    const duration = 2.0
    const length = this.sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, this.sampleRate)
    const data = buffer.getChannelData(0)

    // 胜利旋律：C-E-G-C
    const melody = [261.63, 329.63, 392.00, 523.25]
    const noteLength = length / melody.length

    for (let i = 0; i < length; i++) {
      const noteIndex = Math.floor(i / noteLength)
      const freq = melody[noteIndex] || melody[melody.length - 1]
      const t = (i % noteLength) / this.sampleRate
      const envelope = Math.max(0, 1 - t * 2) // 每个音符的包络
      data[i] = Math.sin(2 * Math.PI * freq * t) * envelope * 0.3
    }

    return buffer
  }

  // 将AudioBuffer转换为WAV格式的Blob
  audioBufferToWav(buffer) {
    const length = buffer.length
    const arrayBuffer = new ArrayBuffer(44 + length * 2)
    const view = new DataView(arrayBuffer)

    // WAV文件头
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }

    writeString(0, 'RIFF')
    view.setUint32(4, 36 + length * 2, true)
    writeString(8, 'WAVE')
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true)
    view.setUint16(20, 1, true)
    view.setUint16(22, 1, true)
    view.setUint32(24, this.sampleRate, true)
    view.setUint32(28, this.sampleRate * 2, true)
    view.setUint16(32, 2, true)
    view.setUint16(34, 16, true)
    writeString(36, 'data')
    view.setUint32(40, length * 2, true)

    // 音频数据
    const data = buffer.getChannelData(0)
    let offset = 44
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, data[i]))
      view.setInt16(offset, sample * 0x7FFF, true)
      offset += 2
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' })
  }

  // 下载音频文件
  downloadAudio(blob, filename) {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 生成所有占位音频
  generateAllPlaceholders() {
    console.log('🎵 开始生成音频占位文件')

    // 背景音乐
    const mainTheme = this.generateChord([261.63, 329.63, 392.00], 10) // C大调和弦，10秒循环
    const gameMusic = this.generateChord([220.00, 277.18, 329.63], 8) // A小调和弦，8秒循环
    const victory = this.generateVictory()

    // 音效
    const cardDeal = this.generateCardSound()
    const cardPlay = this.generateCardSound()
    const cardShuffle = this.generateNoise(0.5, 0.1)
    const buttonClick = this.generateClick()
    const bidding = this.generateSineWave(600, 0.3)
    const pass = this.generateSineWave(300, 0.2)
    const bomb = this.generateNoise(1.0, 0.4)
    const rocket = this.generateSineWave(1000, 1.0, 0.5)
    const straight = this.generateChord([400, 500, 600], 0.8)
    const win = this.generateVictory()
    const lose = this.generateSineWave(200, 1.0, 0.3)
    const warning = this.generateSineWave(800, 0.1)
    const notification = this.generateSineWave(500, 0.2)

    // 转换为WAV并下载
    const audioFiles = [
      { buffer: mainTheme, filename: 'main_theme.wav' },
      { buffer: gameMusic, filename: 'game_music.wav' },
      { buffer: victory, filename: 'victory.wav' },
      { buffer: cardDeal, filename: 'card_deal.wav' },
      { buffer: cardPlay, filename: 'card_play.wav' },
      { buffer: cardShuffle, filename: 'card_shuffle.wav' },
      { buffer: buttonClick, filename: 'button_click.wav' },
      { buffer: bidding, filename: 'bidding.wav' },
      { buffer: pass, filename: 'pass.wav' },
      { buffer: bomb, filename: 'bomb.wav' },
      { buffer: rocket, filename: 'rocket.wav' },
      { buffer: straight, filename: 'straight.wav' },
      { buffer: win, filename: 'win.wav' },
      { buffer: lose, filename: 'lose.wav' },
      { buffer: warning, filename: 'warning.wav' },
      { buffer: notification, filename: 'notification.wav' }
    ]

    audioFiles.forEach(({ buffer, filename }) => {
      const blob = this.audioBufferToWav(buffer)
      this.downloadAudio(blob, filename)
    })

    console.log('✅ 音频占位文件生成完成')
  }
}

// 创建生成器实例
const generator = new AudioPlaceholderGenerator()

// 导出生成器
window.AudioPlaceholderGenerator = generator

console.log('🎵 音频占位文件生成器已加载')
console.log('使用 AudioPlaceholderGenerator.generateAllPlaceholders() 生成音频文件')
