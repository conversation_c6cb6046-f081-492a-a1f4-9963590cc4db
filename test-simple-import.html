<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单导入测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简单导入测试</h1>
        <p>测试最基本的模块导入，不使用Phaser</p>
        
        <div class="test-section">
            <h3>测试: 直接导入Card模块</h3>
            <button onclick="testDirectImport()">测试直接导入</button>
            <div id="directResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message, details = '') {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.innerHTML = `
                <strong>${success ? '✅ 通过' : '❌ 失败'}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
        }

        // 测试直接导入
        window.testDirectImport = async function() {
            log('🧪 开始测试直接导入Card模块')
            
            try {
                log('📦 尝试导入 ./src/game/Card.js')
                const cardModule = await import('./src/game/Card.js')
                log('✅ Card模块导入成功')
                
                if (cardModule.Card) {
                    log('✅ Card类存在')
                    
                    const card = new cardModule.Card('spades', 'A')
                    log(`✅ Card实例创建成功: ${card.getDisplayName()}`)
                    
                    showResult('directResult', true, 'Card模块导入和使用成功')
                } else {
                    log('❌ Card类不存在')
                    showResult('directResult', false, 'Card类不存在')
                }
                
            } catch (error) {
                log(`❌ 导入失败: ${error.message}`)
                log(`❌ 错误堆栈: ${error.stack}`)
                showResult('directResult', false, '导入失败', error.message)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 简单导入测试页面已加载')
            log('这个测试不使用Phaser，只测试基本的ES6模块导入')
        })
    </script>
</body>
</html>
