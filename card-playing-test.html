<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎴 出牌操作测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        button.danger {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .status-panel {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #FFD700;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .pass {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .fail {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .warn {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            color: #FF9800;
        }
        
        #gameContainer {
            width: 100%;
            max-width: 1000px;
            height: 600px;
            border: 3px solid #FFD700;
            margin: 20px auto;
            background: #000;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.5);
        }
        
        .instructions {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #FFD700;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            color: #FFD700;
            margin-top: 0;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .log {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            border: 1px solid #333;
        }
        
        .card-selection-info {
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #4CAF50;
        }
        
        .selected-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .card-chip {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎴 出牌操作完整测试</h1>
            <p>测试手牌选择、出牌按钮、牌型验证等完整出牌流程</p>
        </div>

        <div class="instructions">
            <h4>📋 测试说明</h4>
            <ol>
                <li><strong>启动游戏</strong> - 点击"启动游戏"创建游戏场景</li>
                <li><strong>发牌测试</strong> - 点击"模拟发牌"显示手牌</li>
                <li><strong>选择卡牌</strong> - 直接点击手牌中的卡牌进行选择</li>
                <li><strong>出牌操作</strong> - 选择卡牌后点击"出牌"按钮</li>
                <li><strong>功能测试</strong> - 测试"不要"、"提示"等按钮功能</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎮 游戏控制</h3>
            <div class="controls">
                <button class="primary" onclick="startGame()">启动游戏</button>
                <button onclick="simulateDealing()">模拟发牌</button>
                <button onclick="showPlayingUI()">显示出牌UI</button>
                <button class="danger" onclick="destroyGame()">销毁游戏</button>
            </div>
            <div class="status-panel">
                <div id="gameStatus">游戏状态: 未启动</div>
            </div>
            <div id="gameResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 出牌功能测试</h3>
            <div class="controls">
                <button onclick="testCardSelection()">测试卡牌选择</button>
                <button onclick="testPlayingButtons()">测试出牌按钮</button>
                <button onclick="testCardValidation()">测试牌型验证</button>
                <button onclick="clearSelection()">清空选择</button>
            </div>
            <div class="card-selection-info">
                <h4>🎴 当前选中的卡牌</h4>
                <div id="selectedCardsDisplay">暂无选中卡牌</div>
                <div class="selected-cards" id="selectedCardsChips"></div>
            </div>
            <div id="playingResults"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏显示区域</h3>
            <div id="gameContainer"></div>
        </div>

        <div class="test-section">
            <h3>🔧 快速测试</h3>
            <div class="controls">
                <button onclick="quickTestSingleCard()">快速测试单牌</button>
                <button onclick="quickTestPair()">快速测试对子</button>
                <button onclick="quickTestTriple()">快速测试三张</button>
                <button onclick="runFullPlayingTest()">完整出牌流程测试</button>
            </div>
            <div class="controls">
                <button class="primary" onclick="testPlayingFeature()">一键测试出牌功能</button>
                <button onclick="quickSelectCards(1)">快速选择1张牌</button>
                <button onclick="quickSelectCards(2)">快速选择2张牌</button>
                <button onclick="quickSelectCards(3)">快速选择3张牌</button>
            </div>
            <div id="quickTestResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="controls">
                <button onclick="clearLog()">清空日志</button>
                <button onclick="exportTestLog()">导出日志</button>
            </div>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null
        let selectedCardsMonitor = null

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'card': '🎴'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(containerId, testName, success, message, details = '') {
            const container = document.getElementById(containerId)
            const resultDiv = document.createElement('div')
            resultDiv.className = `test-result ${success ? 'pass' : 'fail'}`
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${testName}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
            container.appendChild(resultDiv)
        }

        function updateGameStatus(status) {
            document.getElementById('gameStatus').textContent = `游戏状态: ${status}`
        }

        function updateSelectedCardsDisplay() {
            if (!gameScene || !gameScene.selectedCards) {
                document.getElementById('selectedCardsDisplay').textContent = '暂无选中卡牌'
                document.getElementById('selectedCardsChips').innerHTML = ''
                return
            }

            const selectedCards = gameScene.selectedCards
            if (selectedCards.length === 0) {
                document.getElementById('selectedCardsDisplay').textContent = '暂无选中卡牌'
                document.getElementById('selectedCardsChips').innerHTML = ''
            } else {
                document.getElementById('selectedCardsDisplay').textContent = 
                    `已选中 ${selectedCards.length} 张牌: ${selectedCards.map(card => card.getDisplayName()).join(', ')}`
                
                // 显示卡牌芯片
                const chipsContainer = document.getElementById('selectedCardsChips')
                chipsContainer.innerHTML = ''
                selectedCards.forEach(card => {
                    const chip = document.createElement('div')
                    chip.className = 'card-chip'
                    chip.textContent = card.getDisplayName()
                    chipsContainer.appendChild(chip)
                })
            }
        }

        // 启动游戏
        window.startGame = async function() {
            log('🎮 启动出牌操作测试游戏', 'info')
            updateGameStatus('启动中...')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }

                // 导入游戏场景
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 1000,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene,
                    scale: {
                        mode: Phaser.Scale.FIT,
                        autoCenter: Phaser.Scale.CENTER_BOTH
                    }
                }

                gameInstance = new Phaser.Game(config)
                
                // 等待场景加载
                setTimeout(() => {
                    gameScene = gameInstance.scene.getScene('GameMainScene')
                    if (gameScene) {
                        showResult('gameResults', '游戏启动', true, '游戏场景创建成功')
                        log('✅ 游戏场景创建成功', 'success')
                        updateGameStatus('游戏已启动')
                        
                        // 开始监控选中卡牌
                        startSelectedCardsMonitoring()
                    } else {
                        showResult('gameResults', '游戏启动', false, '游戏场景获取失败')
                        log('❌ 游戏场景获取失败', 'error')
                        updateGameStatus('启动失败')
                    }
                }, 2000)

            } catch (error) {
                showResult('gameResults', '游戏启动', false, '启动失败', error.message)
                log(`❌ 游戏启动失败: ${error.message}`, 'error')
                updateGameStatus('启动失败')
            }
        }

        // 开始监控选中卡牌
        function startSelectedCardsMonitoring() {
            if (selectedCardsMonitor) {
                clearInterval(selectedCardsMonitor)
            }
            
            selectedCardsMonitor = setInterval(() => {
                updateSelectedCardsDisplay()
            }, 500)
            
            log('🔍 开始监控选中卡牌状态', 'info')
        }

        // 模拟发牌
        window.simulateDealing = function() {
            log('🎴 开始模拟发牌', 'info')
            
            if (!gameScene) {
                showResult('gameResults', '模拟发牌', false, '游戏场景未就绪')
                return
            }

            try {
                // 检查是否有发牌方法
                if (typeof gameScene.displayPlayerCards === 'function') {
                    gameScene.displayPlayerCards()
                    showResult('gameResults', '模拟发牌', true, '手牌显示成功')
                    log('✅ 手牌显示成功', 'success')
                    updateGameStatus('手牌已发放')
                } else {
                    // 如果没有发牌方法，尝试其他方式
                    log('⚠️ 未找到发牌方法，尝试其他方式', 'warning')
                    showResult('gameResults', '模拟发牌', false, '发牌方法不存在')
                }
            } catch (error) {
                showResult('gameResults', '模拟发牌', false, '发牌失败', error.message)
                log(`❌ 模拟发牌失败: ${error.message}`, 'error')
            }
        }

        // 显示出牌UI
        window.showPlayingUI = function() {
            log('🎯 显示出牌UI', 'info')
            
            if (!gameScene) {
                showResult('gameResults', '显示出牌UI', false, '游戏场景未就绪')
                return
            }

            try {
                // 尝试多种方法显示出牌UI
                if (typeof gameScene.forceShowPlayingButtons === 'function') {
                    gameScene.forceShowPlayingButtons()
                    log('✅ 强制显示出牌按钮', 'success')
                }
                
                if (typeof gameScene.showPlayingButtons === 'function') {
                    gameScene.showPlayingButtons()
                    log('✅ 显示出牌按钮', 'success')
                }
                
                if (typeof gameScene.showPlayingUI === 'function') {
                    gameScene.showPlayingUI()
                    log('✅ 显示出牌UI', 'success')
                }
                
                showResult('gameResults', '显示出牌UI', true, '出牌UI显示命令已发送')
                updateGameStatus('出牌UI已显示')
                
            } catch (error) {
                showResult('gameResults', '显示出牌UI', false, '显示失败', error.message)
                log(`❌ 显示出牌UI失败: ${error.message}`, 'error')
            }
        }

        // 销毁游戏
        window.destroyGame = function() {
            log('🗑️ 销毁游戏实例', 'info')
            
            if (selectedCardsMonitor) {
                clearInterval(selectedCardsMonitor)
                selectedCardsMonitor = null
            }
            
            if (gameInstance) {
                gameInstance.destroy(true)
                gameInstance = null
                gameScene = null
                log('✅ 游戏实例已销毁', 'success')
                updateGameStatus('游戏已销毁')
                updateSelectedCardsDisplay()
            } else {
                log('⚠️ 没有游戏实例需要销毁', 'warning')
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 测试卡牌选择
        window.testCardSelection = function() {
            log('🎯 开始测试卡牌选择功能', 'info')

            if (!gameScene) {
                showResult('playingResults', '卡牌选择测试', false, '游戏场景未就绪')
                return
            }

            try {
                // 检查是否有手牌
                if (gameScene.playerHandAreas && gameScene.playerHandAreas.bottom && gameScene.playerHandAreas.bottom.cards) {
                    const handCards = gameScene.playerHandAreas.bottom.cards

                    if (handCards.length > 0) {
                        showResult('playingResults', '手牌检查', true, `发现${handCards.length}张手牌`)
                        log(`✅ 发现${handCards.length}张手牌`, 'success')

                        // 检查卡牌是否可交互
                        const firstCard = handCards[0]
                        if (firstCard.input && firstCard.input.enabled) {
                            showResult('playingResults', '卡牌交互', true, '卡牌支持点击交互')
                            log('✅ 卡牌支持点击交互', 'success')
                        } else {
                            showResult('playingResults', '卡牌交互', false, '卡牌不支持交互')
                            log('❌ 卡牌不支持交互', 'error')
                        }

                        // 检查选择方法
                        if (typeof gameScene.selectCard === 'function') {
                            showResult('playingResults', '选择方法', true, 'selectCard方法存在')
                            log('✅ selectCard方法存在', 'success')
                        } else {
                            showResult('playingResults', '选择方法', false, 'selectCard方法不存在')
                            log('❌ selectCard方法不存在', 'error')
                        }

                    } else {
                        showResult('playingResults', '手牌检查', false, '没有发现手牌')
                        log('❌ 没有发现手牌', 'error')
                    }
                } else {
                    showResult('playingResults', '手牌检查', false, '手牌区域不存在')
                    log('❌ 手牌区域不存在', 'error')
                }

            } catch (error) {
                showResult('playingResults', '卡牌选择测试', false, '测试失败', error.message)
                log(`❌ 卡牌选择测试失败: ${error.message}`, 'error')
            }
        }

        // 测试出牌按钮
        window.testPlayingButtons = function() {
            log('🎯 开始测试出牌按钮功能', 'info')

            if (!gameScene) {
                showResult('playingResults', '出牌按钮测试', false, '游戏场景未就绪')
                return
            }

            try {
                const buttonTests = []

                // 检查出牌按钮
                if (gameScene.uiElements && gameScene.uiElements.playCardsButton) {
                    buttonTests.push({
                        name: '出牌按钮',
                        exists: true,
                        visible: gameScene.uiElements.playCardsButton.visible
                    })
                } else if (gameScene.uiElements && gameScene.uiElements.playingButtons) {
                    buttonTests.push({
                        name: '出牌按钮容器',
                        exists: true,
                        visible: gameScene.uiElements.playingButtons.visible
                    })
                } else {
                    buttonTests.push({
                        name: '出牌按钮',
                        exists: false,
                        visible: false
                    })
                }

                // 检查过牌按钮
                if (gameScene.uiElements && gameScene.uiElements.passButton) {
                    buttonTests.push({
                        name: '过牌按钮',
                        exists: true,
                        visible: gameScene.uiElements.passButton.visible
                    })
                } else {
                    buttonTests.push({
                        name: '过牌按钮',
                        exists: false,
                        visible: false
                    })
                }

                // 检查提示按钮
                if (gameScene.uiElements && gameScene.uiElements.hintButton) {
                    buttonTests.push({
                        name: '提示按钮',
                        exists: true,
                        visible: gameScene.uiElements.hintButton.visible
                    })
                } else {
                    buttonTests.push({
                        name: '提示按钮',
                        exists: false,
                        visible: false
                    })
                }

                // 显示测试结果
                buttonTests.forEach(test => {
                    const success = test.exists && test.visible
                    showResult('playingResults', test.name, success,
                        test.exists ? (test.visible ? '存在且可见' : '存在但不可见') : '不存在')
                    log(`${success ? '✅' : '❌'} ${test.name}: ${test.exists ? (test.visible ? '存在且可见' : '存在但不可见') : '不存在'}`,
                        success ? 'success' : 'error')
                })

                // 检查出牌方法
                if (typeof gameScene.playSelectedCards === 'function') {
                    showResult('playingResults', '出牌方法', true, 'playSelectedCards方法存在')
                    log('✅ playSelectedCards方法存在', 'success')
                } else {
                    showResult('playingResults', '出牌方法', false, 'playSelectedCards方法不存在')
                    log('❌ playSelectedCards方法不存在', 'error')
                }

            } catch (error) {
                showResult('playingResults', '出牌按钮测试', false, '测试失败', error.message)
                log(`❌ 出牌按钮测试失败: ${error.message}`, 'error')
            }
        }

        // 测试牌型验证
        window.testCardValidation = function() {
            log('🎯 开始测试牌型验证功能', 'info')

            if (!gameScene) {
                showResult('playingResults', '牌型验证测试', false, '游戏场景未就绪')
                return
            }

            try {
                // 检查验证方法
                if (gameScene.gameState && typeof gameScene.gameState.getPlayValidationInfo === 'function') {
                    showResult('playingResults', '验证方法', true, 'getPlayValidationInfo方法存在')
                    log('✅ getPlayValidationInfo方法存在', 'success')

                    // 如果有选中的卡牌，测试验证
                    if (gameScene.selectedCards && gameScene.selectedCards.length > 0) {
                        const validationResult = gameScene.gameState.getPlayValidationInfo(gameScene.selectedCards)
                        showResult('playingResults', '牌型验证', validationResult.valid,
                            validationResult.valid ? `有效牌型: ${validationResult.pattern?.type || '未知'}` : `无效牌型: ${validationResult.reason}`)
                        log(`${validationResult.valid ? '✅' : '❌'} 牌型验证: ${validationResult.valid ? '有效' : validationResult.reason}`,
                            validationResult.valid ? 'success' : 'warning')
                    } else {
                        showResult('playingResults', '牌型验证', true, '没有选中卡牌，无法测试验证')
                        log('⚠️ 没有选中卡牌，无法测试验证', 'warning')
                    }
                } else {
                    showResult('playingResults', '验证方法', false, 'getPlayValidationInfo方法不存在')
                    log('❌ getPlayValidationInfo方法不存在', 'error')
                }

            } catch (error) {
                showResult('playingResults', '牌型验证测试', false, '测试失败', error.message)
                log(`❌ 牌型验证测试失败: ${error.message}`, 'error')
            }
        }

        // 清空选择
        window.clearSelection = function() {
            log('🧹 清空卡牌选择', 'info')

            if (!gameScene) {
                log('❌ 游戏场景未就绪', 'error')
                return
            }

            try {
                // 清空选中的卡牌
                if (gameScene.selectedCards) {
                    gameScene.selectedCards.length = 0
                }

                // 重置卡牌视觉状态
                if (gameScene.playerHandAreas && gameScene.playerHandAreas.bottom && gameScene.playerHandAreas.bottom.cards) {
                    gameScene.playerHandAreas.bottom.cards.forEach(cardSprite => {
                        if (cardSprite.selected) {
                            cardSprite.selected = false
                            cardSprite.y = cardSprite.originalY || cardSprite.y + 20
                            cardSprite.setAlpha(1)

                            // 移除选中效果
                            if (cardSprite.glowEffect) {
                                cardSprite.glowEffect.destroy()
                                cardSprite.glowEffect = null
                            }
                        }
                    })
                }

                log('✅ 卡牌选择已清空', 'success')
                updateSelectedCardsDisplay()

            } catch (error) {
                log(`❌ 清空选择失败: ${error.message}`, 'error')
            }
        }

        // 快速测试单牌
        window.quickTestSingleCard = function() {
            log('🎯 快速测试单牌出牌', 'info')

            if (!gameScene || !gameScene.playerHandAreas || !gameScene.playerHandAreas.bottom || !gameScene.playerHandAreas.bottom.cards) {
                showResult('quickTestResults', '快速单牌测试', false, '手牌不存在')
                return
            }

            try {
                clearSelection()

                // 选择第一张牌
                const firstCard = gameScene.playerHandAreas.bottom.cards[0]
                if (firstCard && typeof gameScene.selectCard === 'function') {
                    gameScene.selectCard(firstCard)

                    setTimeout(() => {
                        if (gameScene.selectedCards && gameScene.selectedCards.length === 1) {
                            showResult('quickTestResults', '快速单牌测试', true, '单牌选择成功')
                            log('✅ 单牌选择成功', 'success')

                            // 测试出牌
                            if (typeof gameScene.playSelectedCards === 'function') {
                                log('🎴 尝试出单牌', 'card')
                                gameScene.playSelectedCards()
                            }
                        } else {
                            showResult('quickTestResults', '快速单牌测试', false, '单牌选择失败')
                            log('❌ 单牌选择失败', 'error')
                        }
                    }, 500)
                } else {
                    showResult('quickTestResults', '快速单牌测试', false, '无法选择卡牌')
                    log('❌ 无法选择卡牌', 'error')
                }

            } catch (error) {
                showResult('quickTestResults', '快速单牌测试', false, '测试失败', error.message)
                log(`❌ 快速单牌测试失败: ${error.message}`, 'error')
            }
        }

        // 快速测试对子
        window.quickTestPair = function() {
            log('🎯 快速测试对子出牌', 'info')

            if (!gameScene || !gameScene.playerHandAreas || !gameScene.playerHandAreas.bottom || !gameScene.playerHandAreas.bottom.cards) {
                showResult('quickTestResults', '快速对子测试', false, '手牌不存在')
                return
            }

            try {
                clearSelection()

                // 尝试找到两张相同点数的牌
                const cards = gameScene.playerHandAreas.bottom.cards
                let pairFound = false

                for (let i = 0; i < cards.length - 1; i++) {
                    for (let j = i + 1; j < cards.length; j++) {
                        if (cards[i].cardData && cards[j].cardData &&
                            cards[i].cardData.rank === cards[j].cardData.rank) {

                            // 选择这两张牌
                            gameScene.selectCard(cards[i])
                            gameScene.selectCard(cards[j])
                            pairFound = true
                            break
                        }
                    }
                    if (pairFound) break
                }

                if (pairFound) {
                    setTimeout(() => {
                        if (gameScene.selectedCards && gameScene.selectedCards.length === 2) {
                            showResult('quickTestResults', '快速对子测试', true, '对子选择成功')
                            log('✅ 对子选择成功', 'success')

                            // 测试出牌
                            if (typeof gameScene.playSelectedCards === 'function') {
                                log('🎴 尝试出对子', 'card')
                                gameScene.playSelectedCards()
                            }
                        }
                    }, 500)
                } else {
                    showResult('quickTestResults', '快速对子测试', false, '未找到对子')
                    log('⚠️ 未找到对子', 'warning')
                }

            } catch (error) {
                showResult('quickTestResults', '快速对子测试', false, '测试失败', error.message)
                log(`❌ 快速对子测试失败: ${error.message}`, 'error')
            }
        }

        // 快速测试三张
        window.quickTestTriple = function() {
            log('🎯 快速测试三张出牌', 'info')

            if (!gameScene || !gameScene.playerHandAreas || !gameScene.playerHandAreas.bottom || !gameScene.playerHandAreas.bottom.cards) {
                showResult('quickTestResults', '快速三张测试', false, '手牌不存在')
                return
            }

            try {
                clearSelection()

                // 选择前三张牌（简单测试）
                const cards = gameScene.playerHandAreas.bottom.cards
                if (cards.length >= 3) {
                    gameScene.selectCard(cards[0])
                    gameScene.selectCard(cards[1])
                    gameScene.selectCard(cards[2])

                    setTimeout(() => {
                        if (gameScene.selectedCards && gameScene.selectedCards.length === 3) {
                            showResult('quickTestResults', '快速三张测试', true, '三张牌选择成功')
                            log('✅ 三张牌选择成功', 'success')

                            // 测试出牌
                            if (typeof gameScene.playSelectedCards === 'function') {
                                log('🎴 尝试出三张', 'card')
                                gameScene.playSelectedCards()
                            }
                        }
                    }, 500)
                } else {
                    showResult('quickTestResults', '快速三张测试', false, '手牌不足3张')
                    log('⚠️ 手牌不足3张', 'warning')
                }

            } catch (error) {
                showResult('quickTestResults', '快速三张测试', false, '测试失败', error.message)
                log(`❌ 快速三张测试失败: ${error.message}`, 'error')
            }
        }

        // 完整出牌流程测试
        window.runFullPlayingTest = async function() {
            log('🚀 开始完整出牌流程测试', 'info')

            try {
                // 1. 启动游戏
                if (!gameScene) {
                    await startGame()
                    await new Promise(resolve => setTimeout(resolve, 3000))
                }

                // 2. 模拟发牌
                simulateDealing()
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 3. 显示出牌UI
                showPlayingUI()
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 4. 测试卡牌选择
                testCardSelection()
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 5. 测试出牌按钮
                testPlayingButtons()
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 6. 快速测试单牌
                quickTestSingleCard()

                showResult('quickTestResults', '完整流程测试', true, '完整出牌流程测试完成')
                log('🎉 完整出牌流程测试完成', 'success')

            } catch (error) {
                showResult('quickTestResults', '完整流程测试', false, '测试失败', error.message)
                log(`❌ 完整流程测试失败: ${error.message}`, 'error')
            }
        }

        // 一键测试出牌功能
        window.testPlayingFeature = function() {
            log('🧪 一键测试出牌功能', 'info')

            if (!gameScene) {
                showResult('quickTestResults', '一键测试', false, '游戏场景未就绪')
                return
            }

            try {
                if (typeof gameScene.testPlayingFeature === 'function') {
                    gameScene.testPlayingFeature()
                    showResult('quickTestResults', '一键测试', true, '出牌功能测试已启动')
                    log('✅ 出牌功能测试已启动', 'success')
                    updateGameStatus('出牌功能测试中')
                } else {
                    showResult('quickTestResults', '一键测试', false, 'testPlayingFeature方法不存在')
                    log('❌ testPlayingFeature方法不存在', 'error')
                }
            } catch (error) {
                showResult('quickTestResults', '一键测试', false, '测试失败', error.message)
                log(`❌ 一键测试失败: ${error.message}`, 'error')
            }
        }

        // 快速选择指定数量的卡牌
        window.quickSelectCards = function(count) {
            log(`🎯 快速选择${count}张卡牌`, 'info')

            if (!gameScene) {
                showResult('quickTestResults', `快速选择${count}张`, false, '游戏场景未就绪')
                return
            }

            try {
                if (typeof gameScene.quickSelectTestCards === 'function') {
                    gameScene.quickSelectTestCards(count)

                    setTimeout(() => {
                        const selectedCount = gameScene.selectedCards ? gameScene.selectedCards.length : 0
                        if (selectedCount === count) {
                            showResult('quickTestResults', `快速选择${count}张`, true, `成功选择${selectedCount}张卡牌`)
                            log(`✅ 成功选择${selectedCount}张卡牌`, 'success')
                        } else {
                            showResult('quickTestResults', `快速选择${count}张`, false, `只选择了${selectedCount}张卡牌`)
                            log(`⚠️ 只选择了${selectedCount}张卡牌`, 'warning')
                        }
                        updateSelectedCardsDisplay()
                    }, 500)
                } else {
                    showResult('quickTestResults', `快速选择${count}张`, false, 'quickSelectTestCards方法不存在')
                    log('❌ quickSelectTestCards方法不存在', 'error')
                }
            } catch (error) {
                showResult('quickTestResults', `快速选择${count}张`, false, '选择失败', error.message)
                log(`❌ 快速选择失败: ${error.message}`, 'error')
            }
        }

        // 导出测试日志
        window.exportTestLog = function() {
            const logContent = document.getElementById('testLog').textContent
            const selectedCards = gameScene?.selectedCards || []

            const reportContent = `出牌操作测试报告
=====================================

测试时间: ${new Date().toLocaleString()}

当前选中卡牌: ${selectedCards.length > 0 ? selectedCards.map(card => card.getDisplayName()).join(', ') : '无'}

游戏状态: ${document.getElementById('gameStatus').textContent}

详细测试日志:
${logContent}
`

            const blob = new Blob([reportContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `card-playing-test-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)

            log('📄 测试日志已导出', 'success')
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎴 出牌操作测试页面已加载', 'info')
            log('请按顺序进行测试：1.启动游戏 → 2.模拟发牌 → 3.选择卡牌 → 4.测试出牌', 'info')
        })
    </script>
</body>
</html>
