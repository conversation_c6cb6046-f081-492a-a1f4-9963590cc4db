<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌系统最终测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button.primary {
            background: #2196F3;
        }
        
        button.primary:hover {
            background: #1976D2;
        }
        
        button.danger {
            background: #f44336;
        }
        
        button.danger:hover {
            background: #d32f2f;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            min-height: 100px;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .error {
            color: #f44336;
            font-weight: bold;
        }
        
        .info {
            color: #2196F3;
            font-weight: bold;
        }
        
        .warning {
            color: #FF9800;
            font-weight: bold;
        }
        
        h3 {
            margin-top: 0;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 叫牌系统最终测试</h1>
        
        <div class="test-section">
            <h3>🎯 自动化测试</h3>
            <div class="controls">
                <button class="primary" onclick="runFullTest()">运行完整测试</button>
                <button onclick="runBasicTest()">基础功能测试</button>
                <button onclick="runTimeoutTest()">超时测试</button>
                <button onclick="runSpecialRulesTest()">特殊规则测试</button>
                <button class="danger" onclick="clearAll()">清空日志</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试状态</h3>
            <div id="testStatus" class="status">等待开始测试...</div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script type="module">
        import { BiddingManager } from './src/game/BiddingManager.js'
        
        let testResults = []
        let currentTest = 0
        
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog')
            const time = new Date().toLocaleTimeString()
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : 'info'
            
            logElement.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`
            logElement.scrollTop = logElement.scrollHeight
            console.log(message)
        }
        
        function updateStatus(status) {
            document.getElementById('testStatus').textContent = status
        }
        
        // 测试1: 基础功能测试
        async function runBasicTest() {
            log('🎯 开始基础功能测试', 'info')
            updateStatus('运行基础功能测试...')
            
            try {
                const biddingManager = new BiddingManager()
                let eventCount = 0
                
                // 设置事件监听
                biddingManager.addEventListener('gameStarted', () => {
                    eventCount++
                    log('✅ gameStarted 事件触发', 'success')
                })
                
                biddingManager.addEventListener('bidMade', (data) => {
                    eventCount++
                    log(`✅ bidMade 事件: ${data.player} ${biddingManager.getBidDisplayName(data.bidOption)}`, 'success')
                })
                
                biddingManager.addEventListener('landlordSelected', (data) => {
                    eventCount++
                    log(`✅ landlordSelected 事件: ${data.landlord} 成为地主`, 'success')
                })
                
                // 初始化游戏
                const players = {
                    west: { name: '西家', cards: [] },
                    south: { name: '南家', cards: [] },
                    east: { name: '东家', cards: [] }
                }
                
                biddingManager.initGame(players, 1, 1, 'south')
                await sleep(200)
                
                // 模拟叫牌流程
                biddingManager.simulateOtherPlayerBid('west', 'no_bid')
                await sleep(200)
                
                biddingManager.makeBid('one_point')
                await sleep(200)
                
                biddingManager.simulateOtherPlayerBid('east', 'two_point')
                await sleep(200)
                
                if (eventCount >= 3) {
                    log('✅ 基础功能测试通过', 'success')
                    testResults.push({ test: '基础功能', result: 'PASS' })
                } else {
                    log('❌ 基础功能测试失败：事件数量不足', 'error')
                    testResults.push({ test: '基础功能', result: 'FAIL' })
                }
                
                biddingManager.destroy()
                
            } catch (error) {
                log(`❌ 基础功能测试异常: ${error.message}`, 'error')
                testResults.push({ test: '基础功能', result: 'FAIL' })
            }
            
            updateStatus('基础功能测试完成')
        }
        
        // 测试2: 超时测试
        async function runTimeoutTest() {
            log('🎯 开始超时测试', 'info')
            updateStatus('运行超时测试...')
            
            try {
                const biddingManager = new BiddingManager()
                let timeoutTriggered = false
                
                biddingManager.addEventListener('bidTimeout', (data) => {
                    timeoutTriggered = true
                    log(`✅ 超时事件触发: ${data.player}`, 'success')
                })
                
                biddingManager.setTimeLimit(1) // 1秒超时
                
                const players = {
                    west: { name: '西家', cards: [] },
                    south: { name: '南家', cards: [] },
                    east: { name: '东家', cards: [] }
                }
                
                biddingManager.initGame(players, 1, 1, 'south')
                
                // 等待超时
                await sleep(1500)
                
                if (timeoutTriggered) {
                    log('✅ 超时测试通过', 'success')
                    testResults.push({ test: '超时处理', result: 'PASS' })
                } else {
                    log('❌ 超时测试失败：超时事件未触发', 'error')
                    testResults.push({ test: '超时处理', result: 'FAIL' })
                }
                
                biddingManager.destroy()
                
            } catch (error) {
                log(`❌ 超时测试异常: ${error.message}`, 'error')
                testResults.push({ test: '超时处理', result: 'FAIL' })
            }
            
            updateStatus('超时测试完成')
        }
        
        // 测试3: 特殊规则测试
        async function runSpecialRulesTest() {
            log('🎯 开始特殊规则测试', 'info')
            updateStatus('运行特殊规则测试...')
            
            try {
                const biddingManager = new BiddingManager()
                let landlordSelected = false
                
                biddingManager.addEventListener('landlordSelected', (data) => {
                    landlordSelected = true
                    log(`✅ 3分立即结束规则生效: ${data.landlord}`, 'success')
                })
                
                const players = {
                    west: { name: '西家', cards: [] },
                    south: { name: '南家', cards: [] },
                    east: { name: '东家', cards: [] }
                }
                
                biddingManager.initGame(players, 1, 1, 'south')
                await sleep(200)
                
                // 西家不叫
                biddingManager.simulateOtherPlayerBid('west', 'no_bid')
                await sleep(200)
                
                // 南家叫3分（应该立即结束）
                biddingManager.makeBid('three_point')
                await sleep(200)
                
                if (landlordSelected) {
                    log('✅ 特殊规则测试通过', 'success')
                    testResults.push({ test: '特殊规则', result: 'PASS' })
                } else {
                    log('❌ 特殊规则测试失败：3分未立即结束', 'error')
                    testResults.push({ test: '特殊规则', result: 'FAIL' })
                }
                
                biddingManager.destroy()
                
            } catch (error) {
                log(`❌ 特殊规则测试异常: ${error.message}`, 'error')
                testResults.push({ test: '特殊规则', result: 'FAIL' })
            }
            
            updateStatus('特殊规则测试完成')
        }
        
        // 运行完整测试
        async function runFullTest() {
            log('🎮 开始完整测试套件', 'info')
            testResults = []
            
            await runBasicTest()
            await sleep(500)
            
            await runTimeoutTest()
            await sleep(500)
            
            await runSpecialRulesTest()
            await sleep(500)
            
            // 输出测试结果
            log('\n' + '='.repeat(50), 'info')
            log('🎯 测试结果汇总', 'info')
            log('='.repeat(50), 'info')
            
            const passCount = testResults.filter(r => r.result === 'PASS').length
            const totalCount = testResults.length
            
            testResults.forEach((result, index) => {
                const status = result.result === 'PASS' ? '✅' : '❌'
                const type = result.result === 'PASS' ? 'success' : 'error'
                log(`${index + 1}. ${result.test}: ${status} ${result.result}`, type)
            })
            
            const passRate = Math.round(passCount / totalCount * 100)
            log(`\n📊 测试通过率: ${passCount}/${totalCount} (${passRate}%)`, 
                passRate === 100 ? 'success' : passRate >= 80 ? 'warning' : 'error')
            
            if (passRate === 100) {
                log('🎉 所有测试通过！叫牌系统集成成功！', 'success')
                updateStatus('✅ 所有测试通过！叫牌系统集成成功！')
            } else {
                log('⚠️ 部分测试失败，需要检查问题', 'warning')
                updateStatus(`⚠️ ${passCount}/${totalCount} 测试通过`)
            }
        }
        
        // 辅助函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms))
        }
        
        // 全局函数
        window.runFullTest = runFullTest
        window.runBasicTest = runBasicTest
        window.runTimeoutTest = runTimeoutTest
        window.runSpecialRulesTest = runSpecialRulesTest
        
        window.clearAll = function() {
            document.getElementById('testLog').innerHTML = ''
            document.getElementById('testStatus').textContent = '日志已清空'
            testResults = []
        }
        
        // 页面加载完成后自动运行测试
        log('🎮 叫牌系统最终测试页面加载完成', 'info')
        updateStatus('准备就绪，点击按钮开始测试')
    </script>
</body>
</html>
