<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phaser纯净测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        #gameContainer {
            width: 800px;
            height: 600px;
            border: 2px solid #FFD700;
            margin: 20px auto;
            background: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Phaser纯净测试</h1>
        <p>测试Phaser库本身，不导入任何自定义模块</p>
        
        <div class="test-section">
            <h3>测试1: Phaser库检查</h3>
            <button onclick="testPhaserLibrary()">测试Phaser库</button>
            <div id="phaserResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 创建简单游戏</h3>
            <button onclick="createSimpleGame()">创建简单游戏</button>
            <div id="gameResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 创建内联场景</h3>
            <button onclick="createInlineScene()">创建内联场景</button>
            <div id="sceneResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>游戏显示区域</h3>
            <div id="gameContainer"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script>
        let gameInstance = null

        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.textContent = `${success ? '✅ 通过' : '❌ 失败'}: ${message}`
        }

        // 测试1: Phaser库检查
        window.testPhaserLibrary = function() {
            log('🧪 开始测试Phaser库')
            
            try {
                if (typeof Phaser !== 'undefined') {
                    log(`✅ Phaser库已加载，版本: ${Phaser.VERSION}`)
                    
                    // 检查关键类
                    const requiredClasses = ['Game', 'Scene', 'AUTO', 'Scale']
                    let missingClasses = []
                    
                    requiredClasses.forEach(className => {
                        if (Phaser[className] !== undefined) {
                            log(`✅ Phaser.${className} 存在`)
                        } else {
                            log(`❌ Phaser.${className} 缺失`)
                            missingClasses.push(className)
                        }
                    })
                    
                    if (missingClasses.length === 0) {
                        showResult('phaserResult', true, `Phaser库完整 (v${Phaser.VERSION})`)
                    } else {
                        showResult('phaserResult', false, `Phaser库不完整，缺少: ${missingClasses.join(', ')}`)
                    }
                } else {
                    log('❌ Phaser库未加载')
                    showResult('phaserResult', false, 'Phaser库未加载')
                }
            } catch (error) {
                log(`❌ Phaser库测试失败: ${error.message}`)
                showResult('phaserResult', false, `测试异常: ${error.message}`)
            }
        }

        // 测试2: 创建简单游戏
        window.createSimpleGame = function() {
            log('🧪 开始创建简单游戏')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }
                
                // 创建简单场景
                class SimpleScene extends Phaser.Scene {
                    constructor() {
                        super({ key: 'SimpleScene' })
                    }
                    
                    preload() {
                        log('✅ SimpleScene preload 执行')
                    }
                    
                    create() {
                        log('✅ SimpleScene create 执行')
                        
                        // 创建简单图形
                        const graphics = this.add.graphics()
                        graphics.fillStyle(0x00ff00)
                        graphics.fillRect(100, 100, 200, 100)
                        
                        const text = this.add.text(200, 150, 'Phaser测试成功!', {
                            fontSize: '16px',
                            color: '#ffffff'
                        }).setOrigin(0.5)
                        
                        log('✅ 基本图形和文字创建成功')
                    }
                }
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: SimpleScene
                }
                
                gameInstance = new Phaser.Game(config)
                log('✅ 简单Phaser游戏实例创建成功')
                showResult('gameResult', true, '简单游戏创建成功')
                
            } catch (error) {
                log(`❌ 简单游戏创建失败: ${error.message}`)
                showResult('gameResult', false, `游戏创建失败: ${error.message}`)
            }
        }

        // 测试3: 创建内联场景
        window.createInlineScene = function() {
            log('🧪 开始创建内联场景')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }
                
                // 创建游戏配置（内联场景）
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: {
                        key: 'InlineScene',
                        preload: function() {
                            log('✅ 内联场景 preload 执行')
                        },
                        create: function() {
                            log('✅ 内联场景 create 执行')
                            
                            // 创建简单图形
                            const graphics = this.add.graphics()
                            graphics.fillStyle(0xff0000)
                            graphics.fillRect(150, 150, 150, 80)
                            
                            const text = this.add.text(225, 190, '内联场景成功!', {
                                fontSize: '14px',
                                color: '#ffffff'
                            }).setOrigin(0.5)
                            
                            log('✅ 内联场景图形和文字创建成功')
                        }
                    }
                }
                
                gameInstance = new Phaser.Game(config)
                log('✅ 内联场景游戏实例创建成功')
                showResult('sceneResult', true, '内联场景创建成功')
                
            } catch (error) {
                log(`❌ 内联场景创建失败: ${error.message}`)
                showResult('sceneResult', false, `内联场景创建失败: ${error.message}`)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎮 Phaser纯净测试页面已加载')
            log('这个测试不导入任何自定义模块，只测试Phaser本身')
        })
    </script>
</body>
</html>
