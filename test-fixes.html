<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warn {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复验证测试</h1>
        <p>验证之前测试中发现的问题是否已经修复</p>

        <div class="test-section">
            <h3>测试1: GameScene导出验证</h3>
            <button onclick="testGameSceneExport()">测试GameScene导出</button>
            <div id="gameSceneResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: BiddingManager方法验证</h3>
            <button onclick="testBiddingManagerMethods()">测试BiddingManager方法</button>
            <div id="biddingManagerResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 倒计时功能验证</h3>
            <button onclick="testCountdownFeature()">测试倒计时功能</button>
            <div id="countdownResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 综合功能测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="allTestsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script type="module">
        let testResults = []

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message, details = '') {
            const element = document.getElementById(elementId)
            element.className = `test-result ${success ? 'pass' : 'fail'}`
            element.innerHTML = `
                <strong>${success ? '✅ 通过' : '❌ 失败'}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
        }

        // 测试1: GameScene导出验证
        window.testGameSceneExport = async function() {
            log('🧪 开始测试GameScene导出')
            try {
                const module = await import('./src/game/GameScene.js')
                
                if (module.GameScene && typeof module.GameScene === 'function') {
                    log('✅ GameScene类导出成功')
                    testResults.push({ test: 'GameScene导出', result: 'PASS' })
                    showResult('gameSceneResult', true, 'GameScene类导出正常')
                } else {
                    log('❌ GameScene类导出失败')
                    testResults.push({ test: 'GameScene导出', result: 'FAIL' })
                    showResult('gameSceneResult', false, 'GameScene类未正确导出')
                }

                if (module.GameMainScene && typeof module.GameMainScene === 'function') {
                    log('✅ GameMainScene类导出成功')
                } else {
                    log('⚠️ GameMainScene类导出可能有问题')
                }

            } catch (error) {
                log(`❌ GameScene导入失败: ${error.message}`)
                testResults.push({ test: 'GameScene导出', result: 'FAIL', error: error.message })
                showResult('gameSceneResult', false, 'GameScene导入失败', error.message)
            }
        }

        // 测试2: BiddingManager方法验证
        window.testBiddingManagerMethods = async function() {
            log('🧪 开始测试BiddingManager方法')
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 检查必要方法
                const requiredMethods = [
                    'initGame', 'makeBid', 'processAIBidding', 
                    'determineLandlord', 'getCurrentState', 'getBidDisplayName',
                    'startCountdown', 'clearCountdown', 'getRemainingTime'
                ]
                
                let missingMethods = []
                let presentMethods = []
                
                requiredMethods.forEach(method => {
                    if (typeof manager[method] === 'function') {
                        presentMethods.push(method)
                        log(`✅ 方法存在: ${method}`)
                    } else {
                        missingMethods.push(method)
                        log(`❌ 方法缺失: ${method}`)
                    }
                })
                
                if (missingMethods.length === 0) {
                    testResults.push({ test: 'BiddingManager方法', result: 'PASS' })
                    showResult('biddingManagerResult', true, '所有必要方法都存在', `检查了${requiredMethods.length}个方法`)
                } else {
                    testResults.push({ test: 'BiddingManager方法', result: 'FAIL' })
                    showResult('biddingManagerResult', false, '缺少必要方法', `缺少: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ BiddingManager测试失败: ${error.message}`)
                testResults.push({ test: 'BiddingManager方法', result: 'FAIL', error: error.message })
                showResult('biddingManagerResult', false, 'BiddingManager测试失败', error.message)
            }
        }

        // 测试3: 倒计时功能验证
        window.testCountdownFeature = async function() {
            log('🧪 开始测试倒计时功能')
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 测试倒计时相关方法
                if (typeof manager.startCountdown === 'function' &&
                    typeof manager.clearCountdown === 'function' &&
                    typeof manager.getRemainingTime === 'function') {
                    
                    log('✅ 倒计时方法存在')
                    
                    // 测试初始状态
                    const initialTime = manager.getRemainingTime()
                    log(`📊 初始剩余时间: ${initialTime}`)
                    
                    // 测试设置时间限制
                    manager.setTimeLimit(5)
                    log('✅ 时间限制设置成功')
                    
                    testResults.push({ test: '倒计时功能', result: 'PASS' })
                    showResult('countdownResult', true, '倒计时功能正常', '所有倒计时方法都存在且可调用')
                    
                } else {
                    log('❌ 倒计时方法缺失')
                    testResults.push({ test: '倒计时功能', result: 'FAIL' })
                    showResult('countdownResult', false, '倒计时方法缺失')
                }
                
            } catch (error) {
                log(`❌ 倒计时功能测试失败: ${error.message}`)
                testResults.push({ test: '倒计时功能', result: 'FAIL', error: error.message })
                showResult('countdownResult', false, '倒计时功能测试失败', error.message)
            }
        }

        // 运行所有测试
        window.runAllTests = async function() {
            log('🚀 开始运行所有修复验证测试')
            testResults = []
            
            await testGameSceneExport()
            await new Promise(resolve => setTimeout(resolve, 100))
            
            await testBiddingManagerMethods()
            await new Promise(resolve => setTimeout(resolve, 100))
            
            await testCountdownFeature()
            await new Promise(resolve => setTimeout(resolve, 100))
            
            // 汇总结果
            const passCount = testResults.filter(r => r.result === 'PASS').length
            const totalCount = testResults.length
            const passRate = Math.round((passCount / totalCount) * 100)
            
            log(`\n📊 测试结果汇总:`)
            log(`✅ 通过: ${passCount}/${totalCount} (${passRate}%)`)
            
            testResults.forEach((result, index) => {
                const status = result.result === 'PASS' ? '✅' : '❌'
                log(`${index + 1}. ${result.test}: ${status} ${result.result}`)
                if (result.error) {
                    log(`   错误: ${result.error}`)
                }
            })
            
            if (passCount === totalCount) {
                showResult('allTestsResult', true, '所有测试通过', `${passCount}/${totalCount} 测试通过`)
                log('🎉 所有修复验证测试通过！')
            } else {
                showResult('allTestsResult', false, '部分测试失败', `${passCount}/${totalCount} 测试通过`)
                log('⚠️ 仍有部分问题需要修复')
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = ''
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 修复验证测试页面已加载')
            log('点击按钮开始测试各项修复内容')
        })
    </script>
</body>
</html>
