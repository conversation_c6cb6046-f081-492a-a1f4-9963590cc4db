<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI回归测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        button.test-btn {
            background-color: #2196F3;
        }
        button.test-btn:hover {
            background-color: #42A5F5;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .summary {
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #FFD700;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        #gameContainer {
            width: 800px;
            height: 400px;
            border: 2px solid #FFD700;
            margin: 20px auto;
            background: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 UI回归测试</h1>
        <p>验证UI显示和交互功能是否正常工作</p>
        
        <div class="test-section">
            <h3>快速测试</h3>
            <div class="controls">
                <button class="test-btn" onclick="runAllTests()">运行所有测试</button>
                <button onclick="clearResults()">清空结果</button>
            </div>
        </div>

        <div class="test-section">
            <h3>游戏初始化测试</h3>
            <div class="controls">
                <button class="test-btn" onclick="testGameInitialization()">测试游戏初始化</button>
                <button class="test-btn" onclick="testSceneCreation()">测试场景创建</button>
            </div>
            <div id="initResults"></div>
        </div>

        <div class="test-section">
            <h3>UI显示测试</h3>
            <div class="controls">
                <button class="test-btn" onclick="testBiddingUIDisplay()">测试叫牌UI显示</button>
                <button class="test-btn" onclick="testPlayingUIDisplay()">测试出牌UI显示</button>
                <button class="test-btn" onclick="testCountdownDisplay()">测试倒计时显示</button>
            </div>
            <div id="uiResults"></div>
        </div>

        <div class="test-section">
            <h3>游戏显示区域</h3>
            <div id="gameContainer"></div>
            <div class="controls">
                <button onclick="startTestGame()">启动测试游戏</button>
                <button onclick="destroyTestGame()">销毁游戏</button>
            </div>
        </div>

        <div class="summary" id="testSummary">
            <h3>📊 测试总结</h3>
            <div id="summaryContent">点击"运行所有测试"开始自动测试</div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null
        let testResults = []

        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(containerId, testName, success, message, details = '') {
            const container = document.getElementById(containerId)
            const resultDiv = document.createElement('div')
            resultDiv.className = `result ${success ? 'pass' : 'fail'}`
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${testName}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
            container.appendChild(resultDiv)
            
            // 记录测试结果
            testResults.push({
                name: testName,
                success: success,
                message: message,
                details: details
            })
            
            updateSummary()
        }

        function updateSummary() {
            const passCount = testResults.filter(r => r.success).length
            const failCount = testResults.filter(r => !r.success).length
            const totalCount = testResults.length
            const passRate = totalCount > 0 ? Math.round(passCount / totalCount * 100) : 0
            
            const summaryContent = document.getElementById('summaryContent')
            summaryContent.innerHTML = `
                <p><strong>测试统计:</strong></p>
                <p>✅ 通过: ${passCount} | ❌ 失败: ${failCount} | 📊 总计: ${totalCount}</p>
                <p><strong>通过率: ${passRate}%</strong></p>
                ${failCount > 0 ? '<p style="color: #ff6b6b;">⚠️ 有测试失败，请检查具体问题</p>' : ''}
                ${passRate === 100 && totalCount > 0 ? '<p style="color: #51cf66;">🎉 所有测试通过！</p>' : ''}
            `
        }

        // 运行所有测试
        window.runAllTests = async function() {
            log('🚀 开始运行所有UI测试')
            clearResults()
            
            // 按顺序运行测试
            await testGameInitialization()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testSceneCreation()
            await new Promise(resolve => setTimeout(resolve, 2000))
            
            await testBiddingUIDisplay()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testPlayingUIDisplay()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testCountdownDisplay()
            
            log('🏁 所有UI测试完成')
        }

        function clearResults() {
            testResults = []
            document.getElementById('initResults').innerHTML = ''
            document.getElementById('uiResults').innerHTML = ''
            updateSummary()
        }

        // 测试游戏初始化
        window.testGameInitialization = async function() {
            log('🧪 开始测试游戏初始化')
            
            try {
                // 测试Phaser是否可用
                if (typeof Phaser !== 'undefined') {
                    showResult('initResults', 'Phaser库检查', true, `Phaser ${Phaser.VERSION} 已加载`)
                    log('✅ Phaser库检查通过')
                } else {
                    showResult('initResults', 'Phaser库检查', false, 'Phaser库未加载')
                    log('❌ Phaser库检查失败')
                    return
                }

                // 测试模块导入
                const { GameMainScene } = await import('./src/game/GameScene.js')
                showResult('initResults', '模块导入', true, 'GameMainScene导入成功')
                log('✅ 模块导入成功')

                // 测试场景实例化
                const scene = new GameMainScene()
                if (scene instanceof Phaser.Scene) {
                    showResult('initResults', '场景实例化', true, 'GameMainScene正确继承Phaser.Scene')
                    log('✅ 场景实例化成功')
                } else {
                    showResult('initResults', '场景实例化', false, 'GameMainScene继承问题')
                    log('❌ 场景实例化失败')
                }

            } catch (error) {
                showResult('initResults', '游戏初始化', false, '初始化失败', error.message)
                log(`❌ 游戏初始化失败: ${error.message}`)
            }
        }

        // 测试场景创建
        window.testSceneCreation = async function() {
            log('🧪 开始测试场景创建')
            
            try {
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }

                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 400,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene
                }

                gameInstance = new Phaser.Game(config)
                
                return new Promise((resolve) => {
                    setTimeout(() => {
                        gameScene = gameInstance.scene.getScene('GameMainScene')
                        if (gameScene) {
                            showResult('initResults', '场景创建', true, '游戏场景创建成功')
                            log('✅ 游戏场景创建成功')
                        } else {
                            showResult('initResults', '场景创建', false, '游戏场景获取失败')
                            log('❌ 游戏场景获取失败')
                        }
                        resolve()
                    }, 1000)
                })

            } catch (error) {
                showResult('initResults', '场景创建', false, '场景创建失败', error.message)
                log(`❌ 场景创建失败: ${error.message}`)
            }
        }

        // 测试叫牌UI显示
        window.testBiddingUIDisplay = function() {
            log('🧪 开始测试叫牌UI显示')

            if (!gameScene) {
                showResult('uiResults', '叫牌UI显示', false, '游戏场景未就绪')
                log('❌ 游戏场景未就绪')
                return
            }

            try {
                // 检查方法是否存在
                const hasShowMethod = typeof gameScene.showBiddingUIWithManager === 'function'
                const hasForceMethod = typeof gameScene.forceShowBiddingUI === 'function'
                const hasCreateMethod = typeof gameScene.createBiddingContainer === 'function'

                if (hasShowMethod && hasForceMethod && hasCreateMethod) {
                    // 测试强制显示
                    gameScene.forceShowBiddingUI()

                    // 检查UI是否真的显示了
                    setTimeout(() => {
                        if (gameScene.uiElements.biddingContainer && gameScene.uiElements.biddingContainer.visible) {
                            showResult('uiResults', '叫牌UI显示', true, '叫牌UI显示功能正常')
                            log('✅ 叫牌UI显示测试通过')
                        } else {
                            showResult('uiResults', '叫牌UI显示', false, '叫牌UI未正确显示')
                            log('❌ 叫牌UI未正确显示')
                        }
                    }, 500)
                } else {
                    showResult('uiResults', '叫牌UI显示', false, '叫牌UI方法不完整')
                    log('❌ 叫牌UI方法不完整')
                }

            } catch (error) {
                showResult('uiResults', '叫牌UI显示', false, '测试失败', error.message)
                log(`❌ 叫牌UI显示测试失败: ${error.message}`)
            }
        }

        // 测试出牌UI显示
        window.testPlayingUIDisplay = function() {
            log('🧪 开始测试出牌UI显示')

            if (!gameScene) {
                showResult('uiResults', '出牌UI显示', false, '游戏场景未就绪')
                log('❌ 游戏场景未就绪')
                return
            }

            try {
                // 检查方法是否存在
                const hasShowMethod = typeof gameScene.showPlayingButtons === 'function'
                const hasForceMethod = typeof gameScene.forceShowPlayingButtons === 'function'
                const hasCreateMethod = typeof gameScene.createPlayingButtons === 'function'

                if (hasShowMethod && hasForceMethod && hasCreateMethod) {
                    // 测试强制显示
                    gameScene.forceShowPlayingButtons()

                    // 检查UI是否真的显示了
                    setTimeout(() => {
                        if (gameScene.uiElements.playingButtons && gameScene.uiElements.playingButtons.visible) {
                            showResult('uiResults', '出牌UI显示', true, '出牌UI显示功能正常')
                            log('✅ 出牌UI显示测试通过')
                        } else {
                            showResult('uiResults', '出牌UI显示', false, '出牌UI未正确显示')
                            log('❌ 出牌UI未正确显示')
                        }
                    }, 500)
                } else {
                    showResult('uiResults', '出牌UI显示', false, '出牌UI方法不完整')
                    log('❌ 出牌UI方法不完整')
                }

            } catch (error) {
                showResult('uiResults', '出牌UI显示', false, '测试失败', error.message)
                log(`❌ 出牌UI显示测试失败: ${error.message}`)
            }
        }

        // 测试倒计时显示
        window.testCountdownDisplay = function() {
            log('🧪 开始测试倒计时显示')

            if (!gameScene) {
                showResult('uiResults', '倒计时显示', false, '游戏场景未就绪')
                log('❌ 游戏场景未就绪')
                return
            }

            try {
                // 检查倒计时方法是否存在
                const hasStartMethod = typeof gameScene.startBiddingCountdown === 'function'
                const hasStopMethod = typeof gameScene.stopBiddingCountdown === 'function'
                const hasUpdateMethod = typeof gameScene.updateBiddingCountdownDisplay === 'function'

                if (hasStartMethod && hasStopMethod && hasUpdateMethod) {
                    showResult('uiResults', '倒计时显示', true, '倒计时功能方法完整')
                    log('✅ 倒计时显示测试通过')
                } else {
                    showResult('uiResults', '倒计时显示', false, '倒计时方法不完整')
                    log('❌ 倒计时方法不完整')
                }

            } catch (error) {
                showResult('uiResults', '倒计时显示', false, '测试失败', error.message)
                log(`❌ 倒计时显示测试失败: ${error.message}`)
            }
        }

        // 启动测试游戏
        window.startTestGame = async function() {
            log('🎮 启动测试游戏')
            await testSceneCreation()
        }

        // 销毁测试游戏
        window.destroyTestGame = function() {
            log('🗑️ 销毁测试游戏')
            if (gameInstance) {
                gameInstance.destroy(true)
                gameInstance = null
                gameScene = null
                log('✅ 游戏实例已销毁')
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 UI回归测试页面已加载')
            log('点击"运行所有测试"进行自动化UI测试')
        })
    </script>
</body>
</html>
