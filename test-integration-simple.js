// 简化的叫牌系统集成测试
import GameScene from './src/game/GameScene.js'

console.log('🎮 开始叫牌系统集成测试')

// 创建一个临时的DOM容器
const container = document.createElement('div')
container.id = 'test-game-container'
container.style.width = '1200px'
container.style.height = '800px'
container.style.position = 'absolute'
container.style.top = '-9999px' // 隐藏在屏幕外
document.body.appendChild(container)

try {
    console.log('🎯 创建游戏场景')
    const gameScene = new GameScene('test-game-container')
    
    // 等待游戏初始化
    setTimeout(() => {
        console.log('🎯 检查游戏场景状态')
        
        if (gameScene.game) {
            console.log('✅ Phaser游戏实例创建成功')
            
            // 获取主场景
            const mainScene = gameScene.game.scene.getScene('GameMainScene')
            if (mainScene) {
                console.log('✅ 获取到GameMainScene实例')
                
                // 检查叫牌管理器
                if (mainScene.biddingManager) {
                    console.log('✅ 叫牌管理器已初始化')
                    
                    // 测试叫牌管理器基本功能
                    testBiddingManager(mainScene.biddingManager)
                } else {
                    console.error('❌ 叫牌管理器未初始化')
                }
            } else {
                console.error('❌ 无法获取GameMainScene实例')
            }
        } else {
            console.error('❌ Phaser游戏实例创建失败')
        }
    }, 2000)
    
} catch (error) {
    console.error('❌ 创建游戏场景失败:', error)
}

function testBiddingManager(biddingManager) {
    console.log('🎯 测试叫牌管理器功能')
    
    // 设置事件监听
    biddingManager.addEventListener('gameStarted', (data) => {
        console.log('✅ 叫牌游戏开始事件:', data.firstBidder)
    })
    
    biddingManager.addEventListener('bidMade', (data) => {
        console.log('✅ 叫牌完成事件:', data.player, biddingManager.getBidDisplayName(data.bidOption))
    })
    
    biddingManager.addEventListener('landlordSelected', (data) => {
        console.log('✅ 地主选定事件:', data.landlord)
    })
    
    // 测试初始化游戏
    const players = {
        west: { name: '西家玩家', cards: [] },
        south: { name: '南家玩家', cards: [] },
        east: { name: '东家玩家', cards: [] }
    }
    
    console.log('🎯 初始化叫牌游戏')
    const firstBidder = biddingManager.initGame(players, 1, 1, 'south')
    console.log('✅ 首叫者:', firstBidder)
    
    // 模拟叫牌流程
    setTimeout(() => {
        console.log('🎯 开始模拟叫牌流程')
        
        // 西家不叫
        setTimeout(() => {
            console.log('🎯 西家不叫')
            biddingManager.simulateOtherPlayerBid('west', 'no_bid')
        }, 1000)
        
        // 南家叫1分
        setTimeout(() => {
            console.log('🎯 南家叫1分')
            biddingManager.makeBid('one_point')
        }, 2000)
        
        // 东家叫2分
        setTimeout(() => {
            console.log('🎯 东家叫2分')
            biddingManager.simulateOtherPlayerBid('east', 'two_point')
        }, 3000)
        
        // 西家不叫
        setTimeout(() => {
            console.log('🎯 西家不叫')
            biddingManager.simulateOtherPlayerBid('west', 'no_bid')
        }, 4000)
        
        // 南家不叫
        setTimeout(() => {
            console.log('🎯 南家不叫')
            biddingManager.makeBid('no_bid')
        }, 5000)
        
    }, 1000)
}

// 5秒后输出测试结果
setTimeout(() => {
    console.log('🎯 叫牌系统集成测试完成')
    console.log('✅ 如果看到上述事件日志，说明集成成功')
    
    // 清理测试容器
    document.body.removeChild(container)
}, 8000)
