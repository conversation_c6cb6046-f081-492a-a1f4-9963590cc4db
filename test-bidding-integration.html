<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌系统集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .game-container {
            width: 100%;
            height: 600px;
            border: 2px solid #FFD700;
            border-radius: 10px;
            margin: 20px 0;
            background: #0f5132;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        h3 {
            margin-top: 0;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 叫牌系统集成测试</h1>
        
        <div class="section">
            <h3>游戏控制</h3>
            <div class="controls">
                <button onclick="initGame()">初始化游戏</button>
                <button onclick="startGame()">开始游戏</button>
                <button onclick="resetGame()">重置游戏</button>
                <button onclick="testBiddingFlow()">测试叫牌流程</button>
            </div>
        </div>

        <div class="game-container" id="gameContainer">
            <!-- Phaser游戏将在这里渲染 -->
        </div>

        <div class="info-panel">
            <div class="section">
                <h3>游戏状态</h3>
                <div id="gameStatus" class="status">等待初始化...</div>
            </div>
            
            <div class="section">
                <h3>叫牌状态</h3>
                <div id="biddingStatus" class="status">未开始</div>
            </div>
        </div>

        <div class="section">
            <h3>测试控制</h3>
            <div class="controls">
                <button onclick="simulateBid('west', 'no_bid')">西家不叫</button>
                <button onclick="simulateBid('west', 'one_point')">西家叫1分</button>
                <button onclick="simulateBid('east', 'two_point')">东家叫2分</button>
                <button onclick="simulateBid('east', 'no_bid')">东家不叫</button>
            </div>
        </div>

        <div class="section">
            <h3>事件日志</h3>
            <div id="eventLog" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script type="module">
        import GameScene from './src/game/GameScene.js'
        
        let gameScene = null
        let gameMainScene = null
        
        // 初始化游戏
        function init() {
            try {
                console.log('🎮 初始化叫牌系统集成测试')
                
                // 创建游戏场景
                gameScene = new GameScene('gameContainer')
                
                // 等待游戏初始化完成
                setTimeout(() => {
                    // 获取Phaser场景实例
                    if (gameScene.game && gameScene.game.scene) {
                        gameMainScene = gameScene.game.scene.getScene('GameMainScene')
                        if (gameMainScene) {
                            console.log('✅ 获取到GameMainScene实例')
                            setupGameEvents()
                        } else {
                            console.error('❌ 无法获取GameMainScene实例')
                        }
                    }
                }, 1000)
                
                log('✅ 游戏场景初始化完成')
                updateGameStatus('游戏已初始化')
                
            } catch (error) {
                console.error('❌ 初始化失败:', error)
                log(`❌ 初始化失败: ${error.message}`)
            }
        }
        
        // 设置游戏事件监听
        function setupGameEvents() {
            if (!gameMainScene || !gameMainScene.biddingManager) {
                console.error('❌ 游戏场景或叫牌管理器不存在')
                return
            }
            
            console.log('🎯 设置游戏事件监听')
            
            // 监听叫牌管理器事件
            gameMainScene.biddingManager.addEventListener('gameStarted', (data) => {
                log(`🎮 叫牌游戏开始: 首叫者 ${data.firstBidder}`)
                updateBiddingStatus(`叫牌开始 - ${data.firstBidder}先叫`)
            })
            
            gameMainScene.biddingManager.addEventListener('stateUpdate', (data) => {
                log(`📊 状态更新: ${data.biddingStatus.currentBidder}叫牌`)
                updateBiddingStatus(`当前: ${data.biddingStatus.currentBidder}叫牌`)
            })
            
            gameMainScene.biddingManager.addEventListener('bidMade', (data) => {
                const bidName = gameMainScene.biddingManager.getBidDisplayName(data.bidOption)
                log(`🎯 ${data.player} ${bidName}`)
                updateBiddingStatus(`${data.player} ${bidName}`)
            })
            
            gameMainScene.biddingManager.addEventListener('landlordSelected', (data) => {
                log(`🎉 ${data.landlord} 成为地主！`)
                updateBiddingStatus(`地主: ${data.landlord}`)
            })
            
            gameMainScene.biddingManager.addEventListener('redealRequired', (data) => {
                log(`🔄 ${data.reason}`)
                updateBiddingStatus('需要重新发牌')
            })
            
            log('✅ 事件监听设置完成')
        }
        
        // 全局函数
        window.initGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化')
                return
            }
            
            try {
                // 重置游戏状态
                gameMainScene.gameState.reset()
                log('🎮 游戏状态已重置')
                updateGameStatus('游戏已重置，准备开始')
            } catch (error) {
                log(`❌ 初始化游戏失败: ${error.message}`)
            }
        }
        
        window.startGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化')
                return
            }
            
            try {
                log('🎮 开始游戏')
                gameMainScene.startNewGame()
                updateGameStatus('游戏已开始')
            } catch (error) {
                log(`❌ 开始游戏失败: ${error.message}`)
            }
        }
        
        window.resetGame = function() {
            if (!gameMainScene) {
                log('❌ 游戏场景未初始化')
                return
            }
            
            try {
                log('🔄 重置游戏')
                if (gameMainScene.biddingManager) {
                    gameMainScene.biddingManager.reset()
                }
                gameMainScene.gameState.reset()
                updateGameStatus('游戏已重置')
                updateBiddingStatus('未开始')
            } catch (error) {
                log(`❌ 重置游戏失败: ${error.message}`)
            }
        }
        
        window.testBiddingFlow = function() {
            log('🎯 开始测试叫牌流程')
            
            // 先开始游戏
            startGame()
            
            // 模拟叫牌流程
            setTimeout(() => simulateBid('west', 'no_bid'), 2000)
            setTimeout(() => simulateBid('south', 'one_point'), 4000)
            setTimeout(() => simulateBid('east', 'two_point'), 6000)
            setTimeout(() => simulateBid('west', 'no_bid'), 8000)
            setTimeout(() => simulateBid('south', 'no_bid'), 10000)
        }
        
        window.simulateBid = function(player, bidOption) {
            if (!gameMainScene || !gameMainScene.biddingManager) {
                log('❌ 叫牌管理器未初始化')
                return
            }
            
            try {
                if (player === 'south') {
                    // 南家是当前玩家，直接叫牌
                    const result = gameMainScene.biddingManager.makeBid(bidOption)
                    if (!result.success) {
                        log(`❌ 南家叫牌失败: ${result.reason}`)
                    }
                } else {
                    // 其他玩家，使用模拟叫牌
                    const result = gameMainScene.biddingManager.simulateOtherPlayerBid(player, bidOption)
                    if (!result.success) {
                        log(`❌ ${player}模拟叫牌失败: ${result.reason}`)
                    }
                }
            } catch (error) {
                log(`❌ 叫牌操作失败: ${error.message}`)
            }
        }
        
        window.clearLog = function() {
            document.getElementById('eventLog').innerHTML = ''
        }
        
        function updateGameStatus(status) {
            document.getElementById('gameStatus').textContent = status
        }
        
        function updateBiddingStatus(status) {
            document.getElementById('biddingStatus').textContent = status
        }
        
        function log(message) {
            const logElement = document.getElementById('eventLog')
            const time = new Date().toLocaleTimeString()
            logElement.innerHTML += `[${time}] ${message}\n`
            logElement.scrollTop = logElement.scrollHeight
            console.log(message)
        }
        
        // 初始化
        init()
    </script>
</body>
</html>
