# 🎮 斗地主游戏本地运行指南

## 📋 项目概述

这是一个基于React + Vite + Phaser.js的斗地主游戏项目，包含完整的前端游戏逻辑和UI界面。

## 🛠️ 环境要求

### 必需软件
- **Node.js**: 版本 16.0+ (推荐 18.0+)
- **npm**: 版本 8.0+ (通常随Node.js安装)
- **Git**: 用于版本控制

### 检查环境
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查Git版本
git --version
```

## 🚀 快速启动

### 1. 克隆项目（如果需要）
```bash
git clone <项目地址>
cd DouDiZhu
```

### 2. 安装依赖
```bash
# 安装所有依赖包
npm install
```

### 3. 启动开发服务器
```bash
# 启动开发模式
npm run dev
```

### 4. 访问应用
- **主应用**: http://localhost:3000
- **游戏测试页面**: http://localhost:3000/test-game-ui.html
- **出牌测试**: http://localhost:3000/card-playing-test.html
- **移动端测试**: http://localhost:3000/mobile-compatibility-test.html

## 📁 项目结构

```
DouDiZhu/
├── src/                    # 源代码目录
│   ├── game/              # 游戏核心逻辑
│   │   ├── GameScene.js   # 主游戏场景
│   │   ├── GameState.js   # 游戏状态管理
│   │   ├── BiddingManager.js # 叫牌管理
│   │   └── ...
│   ├── components/        # React组件
│   ├── pages/            # 页面组件
│   └── main.jsx          # 应用入口
├── public/               # 静态资源
├── resources/            # 游戏资源
├── test-*.html          # 各种测试页面
├── package.json         # 项目配置
└── vite.config.js       # Vite配置
```

## 🎯 测试页面说明

### 1. **主游戏界面**
- **URL**: http://localhost:3000
- **功能**: React应用主界面
- **特点**: 完整的用户界面和路由

### 2. **游戏UI测试**
- **URL**: http://localhost:3000/test-game-ui.html
- **功能**: 测试Phaser游戏场景和UI
- **用途**: 验证游戏基本功能

### 3. **出牌功能测试**
- **URL**: http://localhost:3000/card-playing-test.html
- **功能**: 专门测试出牌操作
- **特点**: 
  - 一键测试出牌功能
  - 实时监控选中卡牌
  - 详细的测试日志

### 4. **移动端兼容性测试**
- **URL**: http://localhost:3000/mobile-compatibility-test.html
- **功能**: 测试移动设备兼容性
- **特点**: 触摸交互、响应式布局测试

### 5. **UI回归测试**
- **URL**: http://localhost:3000/ui-regression-test.html
- **功能**: 自动化UI功能测试
- **特点**: 一键运行所有UI测试

## 🔧 开发命令

```bash
# 开发模式（热重载）
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 安装新依赖
npm install <package-name>

# 安装开发依赖
npm install <package-name> --save-dev
```

## 🎮 游戏功能测试

### 快速测试流程
1. **启动项目**: `npm run dev`
2. **打开出牌测试页**: http://localhost:3000/card-playing-test.html
3. **点击"一键测试出牌功能"**
4. **等待游戏加载完成**
5. **点击手牌选择卡牌**
6. **点击"出牌"按钮测试**

### 功能验证清单
- [ ] 游戏场景正常加载
- [ ] 手牌正确显示
- [ ] 卡牌可以点击选择
- [ ] 选中卡牌有视觉反馈
- [ ] 出牌按钮正常显示
- [ ] 出牌功能正常工作
- [ ] 叫牌功能正常工作
- [ ] 移动端触摸正常

## 🐛 常见问题解决

### 1. 端口被占用
```bash
# 如果3000端口被占用，可以指定其他端口
npm run dev -- --port 3001
```

### 2. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. 游戏资源加载失败
- 检查 `public/` 和 `resources/` 目录是否存在
- 确保静态资源服务器正常运行

### 4. Phaser游戏不显示
- 检查浏览器控制台是否有错误
- 确认Phaser.js正确加载
- 检查游戏容器DOM元素是否存在

## 📱 移动端测试

### 在移动设备上测试
1. 确保电脑和手机在同一网络
2. 查看开发服务器输出的网络地址
3. 在手机浏览器中访问该地址
4. 使用移动端测试页面进行测试

### 模拟移动设备
1. 在Chrome中按F12打开开发者工具
2. 点击设备模拟按钮
3. 选择移动设备型号
4. 测试触摸交互功能

## 🔍 调试技巧

### 1. 浏览器开发者工具
- **F12**: 打开开发者工具
- **Console**: 查看日志和错误
- **Network**: 检查资源加载
- **Elements**: 检查DOM结构

### 2. 游戏调试
- 所有测试页面都有详细的控制台日志
- 使用测试页面的日志面板查看实时信息
- 可以导出测试日志进行分析

### 3. 性能监控
- 使用移动端测试页面的性能监控功能
- 查看FPS和内存使用情况
- 检查游戏运行是否流畅

## 📞 技术支持

如果遇到问题：
1. 检查控制台错误信息
2. 查看测试页面的详细日志
3. 确认所有依赖正确安装
4. 验证Node.js版本兼容性

## ❗ 主应用空白页面问题解决

如果访问 `http://localhost:3002/` 出现空白页面，这是正常的，因为主应用依赖后端API。

### 🔧 解决方案：

#### 方案1：使用简化登录页面（推荐）
```bash
# 访问简化登录页面
http://localhost:3002/simple-login.html
```
- 点击"进入游戏大厅"会自动设置模拟用户数据
- 然后可以正常访问主应用

#### 方案2：直接使用游戏测试页面
```bash
# 出牌功能测试（最完整）
http://localhost:3002/card-playing-test.html

# 游戏UI测试
http://localhost:3002/test-game-ui.html

# 移动端兼容性测试
http://localhost:3002/mobile-compatibility-test.html

# 调试页面
http://localhost:3002/debug.html
```

#### 方案3：手动设置用户数据
在浏览器控制台中执行：
```javascript
// 设置模拟用户数据
localStorage.setItem('gameStore', JSON.stringify({
  user: {
    id: 'test_user_123',
    nickname: '测试玩家',
    avatar: '/images/headimage/avatar_1.png'
  },
  gameState: 'idle'
}))
localStorage.setItem('token', 'mock_token_' + Date.now())

// 刷新页面
location.reload()
```

## 🎯 推荐测试流程

### 1. 快速开始（推荐）
```bash
# 1. 启动服务器
npm run dev

# 2. 访问简化登录页面
http://localhost:3002/simple-login.html

# 3. 点击"进入游戏大厅"
# 4. 系统会自动跳转到主应用
```

### 2. 游戏功能测试
```bash
# 出牌功能完整测试
http://localhost:3002/card-playing-test.html
```
- 点击"一键测试出牌功能"
- 等待游戏加载完成
- 测试手牌选择和出牌操作

### 3. 移动端测试
```bash
# 移动端兼容性测试
http://localhost:3002/mobile-compatibility-test.html
```
- 在手机上访问: `http://*************:3002/mobile-compatibility-test.html`
- 测试触摸交互功能

## 🎮 游戏功能验证清单

访问出牌测试页面后，验证以下功能：

- [ ] ✅ 游戏场景正常加载
- [ ] ✅ 手牌正确显示在底部
- [ ] ✅ 点击卡牌可以选择（卡牌会上移）
- [ ] ✅ 选中卡牌信息实时更新
- [ ] ✅ 出牌按钮正常显示
- [ ] ✅ 出牌功能正常工作
- [ ] ✅ 叫牌功能正常工作
- [ ] ✅ 移动端触摸正常

## 🎉 开始游戏

现在你可以开始本地运行和测试斗地主游戏了！

```bash
# 一键启动
npm run dev

# 然后访问（选择其中一个）：
# 1. 简化登录页面（推荐）
http://localhost:3002/simple-login.html

# 2. 出牌功能测试（直接测试游戏）
http://localhost:3002/card-playing-test.html

# 3. 调试页面（检查项目状态）
http://localhost:3002/debug.html
```

🎮 **开始游戏测试！**
