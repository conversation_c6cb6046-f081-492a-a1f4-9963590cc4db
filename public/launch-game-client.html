<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 启动游戏客户端</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .launcher-container {
            background: rgba(0,0,0,0.9);
            padding: 40px;
            border-radius: 20px;
            border: 3px solid #FFD700;
            text-align: center;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .game-logo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .game-title {
            font-size: 32px;
            font-weight: bold;
            margin: 20px 0 10px;
            color: #FFD700;
        }
        
        .game-subtitle {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 30px;
        }
        
        .launch-button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            margin: 10px;
            min-width: 200px;
        }
        
        .launch-button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        }
        
        .launch-button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
            animation: glow 2s infinite;
        }
        
        .launch-button.primary:hover {
            background: linear-gradient(45deg, #42A5F5, #64B5F6);
        }
        
        @keyframes glow {
            0% { box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3); }
            50% { box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6); }
            100% { box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3); }
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196F3;
            color: #2196F3;
        }
        
        .status.loading {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            color: #FFC107;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #66BB6A);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .quick-links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #333;
        }
        
        .quick-links h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .link-button {
            background: rgba(255,255,255,0.1);
            border: 1px solid #666;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 12px;
            margin: 5px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            background: rgba(255,255,255,0.2);
            border-color: #FFD700;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <div class="game-logo">🎴</div>
        <h1 class="game-title">斗地主游戏客户端</h1>
        <p class="game-subtitle">正在准备启动游戏大厅...</p>
        
        <div id="status" class="status info">
            🔍 正在检查游戏环境...
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <button class="launch-button primary" id="launchButton" onclick="launchGameClient()" disabled>
            🚀 启动游戏大厅
        </button>
        
        <div class="quick-links">
            <h4>🎯 快速访问</h4>
            <a href="/hall" class="link-button" target="_blank">直接进入大厅</a>
            <a href="/card-playing-test.html" class="link-button" target="_blank">游戏功能测试</a>
            <a href="/user-data-test.html" class="link-button" target="_blank">用户数据测试</a>
            <a href="/debug.html" class="link-button" target="_blank">调试页面</a>
        </div>
    </div>

    <script>
        let currentStep = 0
        const totalSteps = 4
        
        function updateProgress(step) {
            const progress = (step / totalSteps) * 100
            document.getElementById('progressFill').style.width = `${progress}%`
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status')
            statusDiv.className = `status ${type}`
            statusDiv.textContent = message
        }
        
        function enableLaunchButton() {
            const button = document.getElementById('launchButton')
            button.disabled = false
            button.textContent = '🚀 启动游戏大厅'
        }
        
        // 自动初始化游戏环境
        async function initializeGameEnvironment() {
            console.log('=== 初始化游戏环境 ===')
            
            // 步骤1: 检查用户数据
            currentStep = 1
            updateProgress(currentStep)
            showStatus('🔍 步骤1/4: 检查用户数据...', 'loading')
            
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            if (!gameStore || !token) {
                console.log('用户数据不存在，创建模拟数据')
                
                // 创建完整的用户数据
                const mockUser = {
                    id: Date.now(),
                    openid: 'mock_openid_' + Date.now(),
                    nickname: '游戏玩家',
                    headerimg: '/images/headimage/avatar_1.png',
                    unionid: 'mock_unionid_' + Date.now(),
                    time: new Date().toISOString()
                }
                
                const gameStoreData = {
                    user: mockUser,
                    currentGame: null,
                    currentRoom: null,
                    gameState: 'idle'
                }
                
                const mockToken = 'mock_token_' + Date.now()
                
                localStorage.setItem('gameStore', JSON.stringify(gameStoreData))
                localStorage.setItem('token', mockToken)
                
                console.log('用户数据已创建:', mockUser)
            } else {
                console.log('用户数据已存在')
            }
            
            // 步骤2: 验证React应用兼容性
            currentStep = 2
            updateProgress(currentStep)
            showStatus('⚙️ 步骤2/4: 验证应用兼容性...', 'loading')
            
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            try {
                const data = JSON.parse(localStorage.getItem('gameStore'))
                if (data.user && data.user.id && data.user.nickname) {
                    console.log('React应用兼容性验证通过')
                } else {
                    throw new Error('用户数据格式不正确')
                }
            } catch (error) {
                console.error('兼容性验证失败:', error)
                showStatus('❌ 应用兼容性验证失败', 'error')
                return false
            }
            
            // 步骤3: 检查游戏资源
            currentStep = 3
            updateProgress(currentStep)
            showStatus('📁 步骤3/4: 检查游戏资源...', 'loading')
            
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            // 检查关键资源
            const resources = [
                '/images/headimage/avatar_1.png',
                '/resources/bg_login.jpg'
            ]
            
            let resourcesOK = true
            for (const resource of resources) {
                try {
                    const response = await fetch(resource)
                    if (!response.ok) {
                        console.warn(`资源检查失败: ${resource}`)
                        resourcesOK = false
                    }
                } catch (error) {
                    console.warn(`资源检查错误: ${resource}`, error)
                    resourcesOK = false
                }
            }
            
            if (resourcesOK) {
                console.log('游戏资源检查通过')
            } else {
                console.warn('部分游戏资源缺失，但不影响基本功能')
            }
            
            // 步骤4: 准备启动
            currentStep = 4
            updateProgress(currentStep)
            showStatus('🎮 步骤4/4: 准备启动游戏...', 'loading')
            
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            showStatus('✅ 游戏环境准备完成！', 'success')
            enableLaunchButton()
            
            console.log('游戏环境初始化完成')
            return true
        }
        
        // 启动游戏客户端
        function launchGameClient() {
            console.log('=== 启动游戏客户端 ===')
            
            showStatus('🚀 正在启动游戏大厅...', 'loading')
            
            // 检查用户数据
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            if (!gameStore || !token) {
                showStatus('❌ 用户数据缺失，请重新初始化', 'error')
                return
            }
            
            try {
                const data = JSON.parse(gameStore)
                console.log('启动游戏，用户:', data.user.nickname)
                
                // 跳转到游戏大厅
                setTimeout(() => {
                    window.location.href = '/hall'
                }, 1000)
                
            } catch (error) {
                console.error('启动失败:', error)
                showStatus('❌ 启动失败，数据格式错误', 'error')
            }
        }
        
        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('游戏客户端启动器已加载')
            
            // 延迟一下再开始初始化，让用户看到界面
            setTimeout(() => {
                initializeGameEnvironment()
            }, 500)
        })
    </script>
</body>
</html>
