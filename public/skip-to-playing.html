<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ 跳过叫牌直接出牌</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
            font-size: 18px;
            padding: 20px 30px;
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 16px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196F3;
            color: #2196F3;
        }
        
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            color: #FFC107;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid #FFC107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            color: #FFC107;
            margin-top: 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            border-left: 4px solid #FFC107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 跳过叫牌直接进入出牌阶段</h1>
            <p>快速跳过叫牌流程，直接体验出牌功能</p>
        </div>

        <div class="instructions">
            <h4>📋 操作说明</h4>
            <div class="step">1. 当前游戏在叫牌阶段，需要完成叫牌才能进入出牌阶段</div>
            <div class="step">2. 点击下方按钮可以快速跳过叫牌，直接进入出牌阶段</div>
            <div class="step">3. 跳过后你将成为地主，可以立即开始出牌</div>
            <div class="step">4. 这样就能直接测试出牌功能了</div>
        </div>

        <div class="section">
            <h3>🚀 快速跳过叫牌</h3>
            <div class="controls">
                <button class="primary" onclick="skipToPlaying()">⚡ 跳过叫牌，直接出牌</button>
                <button onclick="checkGameState()">📊 检查游戏状态</button>
                <button class="warning" onclick="resetGame()">🔄 重新开始游戏</button>
            </div>
            <div id="skipStatus" class="status info">点击"跳过叫牌，直接出牌"来快速进入出牌阶段</div>
        </div>

        <div class="section">
            <h3>🎮 游戏控制</h3>
            <div class="controls">
                <button onclick="goToGame()">🎯 返回游戏</button>
                <button onclick="openGameTest()">🧪 打开游戏测试</button>
                <button onclick="openConsole()">🔍 打开控制台</button>
            </div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="operationLog" style="background: rgba(0,0,0,0.9); padding: 15px; border-radius: 10px; font-family: 'Consolas', monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                跳过叫牌工具已加载...
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('operationLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('skipStatus')
            statusDiv.className = `status ${type}`
            statusDiv.textContent = message
        }
        
        // 跳过叫牌直接进入出牌阶段
        function skipToPlaying() {
            log('⚡ 开始跳过叫牌流程', 'info')
            showStatus('正在跳过叫牌，准备进入出牌阶段...', 'info')
            
            // 这个函数需要在游戏页面的控制台中执行
            const skipScript = `
// 跳过叫牌直接进入出牌阶段
if (window.gameScene && window.gameScene.gameState) {
    console.log('⚡ 开始跳过叫牌流程')
    
    // 1. 设置地主
    window.gameScene.gameState.setLandlord(1) // 设置玩家为地主
    console.log('✅ 设置玩家为地主')
    
    // 2. 分配底牌给地主
    const gameInfo = window.gameScene.gameState.getGameInfo()
    if (gameInfo.landlordCards && gameInfo.landlordCards.length > 0) {
        const player = window.gameScene.gameState.getPlayer(1)
        gameInfo.landlordCards.forEach(card => {
            player.hand.addCard(card)
        })
        console.log('✅ 底牌已分配给地主')
    }
    
    // 3. 切换到出牌阶段
    window.gameScene.gameState.startPlaying()
    console.log('✅ 切换到出牌阶段')
    
    // 4. 更新UI
    window.gameScene.updateUI()
    console.log('✅ UI已更新')
    
    // 5. 显示出牌UI
    setTimeout(() => {
        window.gameScene.showPlayingUI()
        console.log('✅ 出牌UI已显示')
        
        // 6. 强制显示出牌按钮
        setTimeout(() => {
            window.gameScene.forceShowPlayingButtons()
            console.log('✅ 出牌按钮已强制显示')
        }, 500)
    }, 1000)
    
    console.log('🎉 跳过叫牌完成，现在可以开始出牌了！')
} else {
    console.error('❌ 游戏场景或游戏状态不存在')
}
            `
            
            // 将脚本复制到剪贴板
            navigator.clipboard.writeText(skipScript).then(() => {
                log('✅ 跳过脚本已复制到剪贴板', 'success')
                showStatus('✅ 脚本已复制！请在游戏页面控制台中粘贴执行', 'success')
                
                // 显示详细说明
                setTimeout(() => {
                    alert(`跳过叫牌脚本已复制到剪贴板！

请按以下步骤操作：
1. 切换到游戏页面
2. 按F12打开浏览器控制台
3. 粘贴脚本并按回车执行
4. 游戏将自动跳过叫牌进入出牌阶段

或者点击"返回游戏"按钮，然后在控制台中粘贴执行。`)
                }, 1000)
                
            }).catch(err => {
                log('❌ 复制失败，请手动复制脚本', 'error')
                showStatus('❌ 自动复制失败，请手动复制下方脚本', 'error')
                
                // 显示脚本内容
                const scriptDisplay = document.createElement('textarea')
                scriptDisplay.value = skipScript
                scriptDisplay.style.width = '100%'
                scriptDisplay.style.height = '200px'
                scriptDisplay.style.background = 'rgba(0,0,0,0.8)'
                scriptDisplay.style.color = 'white'
                scriptDisplay.style.border = '1px solid #666'
                scriptDisplay.style.borderRadius = '5px'
                scriptDisplay.style.padding = '10px'
                scriptDisplay.style.fontFamily = 'Consolas, monospace'
                scriptDisplay.style.fontSize = '12px'
                
                const container = document.getElementById('operationLog').parentElement
                container.appendChild(scriptDisplay)
                
                scriptDisplay.select()
            })
        }
        
        // 检查游戏状态
        function checkGameState() {
            log('📊 检查游戏状态', 'info')
            
            // 打开游戏页面并检查状态
            const gameWindow = window.open('/hall', 'gameWindow')
            
            setTimeout(() => {
                try {
                    if (gameWindow.gameScene && gameWindow.gameScene.gameState) {
                        const gameInfo = gameWindow.gameScene.gameState.getGameInfo()
                        log(`当前游戏阶段: ${gameInfo.phase}`, 'info')
                        log(`当前玩家: ${gameInfo.currentPlayer}`, 'info')
                        log(`地主: ${gameInfo.landlord || '未确定'}`, 'info')
                        
                        if (gameInfo.phase === 'bidding') {
                            showStatus('🎯 游戏在叫牌阶段，可以跳过叫牌', 'warning')
                        } else if (gameInfo.phase === 'playing') {
                            showStatus('✅ 游戏已在出牌阶段', 'success')
                        } else {
                            showStatus(`游戏阶段: ${gameInfo.phase}`, 'info')
                        }
                    } else {
                        showStatus('❌ 无法访问游戏状态', 'error')
                        log('❌ 游戏场景或状态不存在', 'error')
                    }
                } catch (error) {
                    showStatus('❌ 检查游戏状态失败', 'error')
                    log(`❌ 检查失败: ${error.message}`, 'error')
                }
            }, 2000)
        }
        
        // 重新开始游戏
        function resetGame() {
            log('🔄 重新开始游戏', 'warning')
            
            if (confirm('确定要重新开始游戏吗？这将清除当前游戏进度。')) {
                const resetScript = `
if (window.gameScene) {
    console.log('🔄 重新开始游戏')
    window.gameScene.restartGame()
    console.log('✅ 游戏已重新开始')
} else {
    console.error('❌ 游戏场景不存在')
    location.reload()
}
                `
                
                navigator.clipboard.writeText(resetScript).then(() => {
                    showStatus('✅ 重置脚本已复制，请在游戏页面控制台执行', 'success')
                    log('✅ 重置脚本已复制到剪贴板', 'success')
                }).catch(() => {
                    showStatus('❌ 请手动在游戏页面控制台执行: window.gameScene.restartGame()', 'error')
                    log('❌ 复制失败，请手动执行重置命令', 'error')
                })
            }
        }
        
        // 返回游戏
        function goToGame() {
            log('🎯 返回游戏页面', 'info')
            window.open('/hall', '_blank')
        }
        
        // 打开游戏测试
        function openGameTest() {
            log('🧪 打开游戏测试页面', 'info')
            window.open('/card-playing-test.html', '_blank')
        }
        
        // 打开控制台
        function openConsole() {
            log('🔍 提示打开控制台', 'info')
            alert('请按F12键打开浏览器开发者工具控制台')
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('⚡ 跳过叫牌工具已加载', 'success')
            log('当前游戏在叫牌阶段，点击"跳过叫牌"可以直接进入出牌阶段', 'info')
        })
    </script>
</body>
</html>
