<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 调试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        
        .log {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            border: 1px solid #333;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 斗地主项目调试页面</h1>
            <p>检查项目运行状态和资源加载情况</p>
        </div>

        <div class="section">
            <h3>📊 基本信息</h3>
            <div id="basicInfo">
                <div class="status info">
                    <strong>当前URL:</strong> <span id="currentUrl"></span>
                </div>
                <div class="status info">
                    <strong>用户代理:</strong> <span id="userAgent"></span>
                </div>
                <div class="status info">
                    <strong>屏幕尺寸:</strong> <span id="screenSize"></span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🔗 页面链接测试</h3>
            <div class="controls">
                <button onclick="testMainApp()">测试主应用</button>
                <button onclick="testGameUI()">测试游戏UI</button>
                <button onclick="testCardPlaying()">测试出牌功能</button>
                <button onclick="testMobileCompat()">测试移动端</button>
            </div>
            <div id="linkResults"></div>
        </div>

        <div class="section">
            <h3>📁 资源检查</h3>
            <div class="controls">
                <button onclick="checkResources()">检查游戏资源</button>
                <button onclick="checkImages()">检查图片资源</button>
                <button onclick="checkScripts()">检查脚本文件</button>
            </div>
            <div id="resourceResults"></div>
        </div>

        <div class="section">
            <h3>🎮 游戏功能测试</h3>
            <div class="controls">
                <button class="primary" onclick="quickGameTest()">快速游戏测试</button>
                <button onclick="testPhaser()">测试Phaser</button>
                <button onclick="testReact()">测试React</button>
            </div>
            <div id="gameResults"></div>
        </div>

        <div class="section">
            <h3>📝 调试日志</h3>
            <div class="controls">
                <button onclick="clearLog()">清空日志</button>
                <button onclick="exportLog()">导出日志</button>
            </div>
            <div id="debugLog" class="log">调试页面已加载...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(containerId, testName, success, message) {
            const container = document.getElementById(containerId)
            const resultDiv = document.createElement('div')
            resultDiv.className = `status ${success ? 'success' : 'error'}`
            resultDiv.innerHTML = `<strong>${success ? '✅' : '❌'} ${testName}</strong>: ${message}`
            container.appendChild(resultDiv)
        }

        // 初始化基本信息
        function initBasicInfo() {
            document.getElementById('currentUrl').textContent = window.location.href
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...'
            document.getElementById('screenSize').textContent = `${window.screen.width}×${window.screen.height}`
        }

        // 测试主应用
        function testMainApp() {
            log('🧪 测试主应用访问', 'info')
            
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        showResult('linkResults', '主应用', true, '主应用响应正常')
                        log('✅ 主应用响应正常', 'success')
                    } else {
                        showResult('linkResults', '主应用', false, `响应错误: ${response.status}`)
                        log(`❌ 主应用响应错误: ${response.status}`, 'error')
                    }
                })
                .catch(error => {
                    showResult('linkResults', '主应用', false, `访问失败: ${error.message}`)
                    log(`❌ 主应用访问失败: ${error.message}`, 'error')
                })
        }

        // 测试游戏UI页面
        function testGameUI() {
            log('🧪 测试游戏UI页面', 'info')
            window.open('/test-game-ui.html', '_blank')
            showResult('linkResults', '游戏UI页面', true, '已在新窗口打开')
            log('✅ 游戏UI页面已在新窗口打开', 'success')
        }

        // 测试出牌功能页面
        function testCardPlaying() {
            log('🧪 测试出牌功能页面', 'info')
            window.open('/card-playing-test.html', '_blank')
            showResult('linkResults', '出牌功能页面', true, '已在新窗口打开')
            log('✅ 出牌功能页面已在新窗口打开', 'success')
        }

        // 测试移动端页面
        function testMobileCompat() {
            log('🧪 测试移动端兼容性页面', 'info')
            window.open('/mobile-compatibility-test.html', '_blank')
            showResult('linkResults', '移动端页面', true, '已在新窗口打开')
            log('✅ 移动端页面已在新窗口打开', 'success')
        }

        // 检查游戏资源
        function checkResources() {
            log('🔍 检查游戏资源', 'info')
            
            const resources = [
                '/resources/UI/card/card.png',
                '/resources/bg_login.jpg',
                '/images/headimage/avatar_1.png'
            ]
            
            let checkedCount = 0
            let successCount = 0
            
            resources.forEach(resource => {
                fetch(resource)
                    .then(response => {
                        checkedCount++
                        if (response.ok) {
                            successCount++
                            log(`✅ 资源存在: ${resource}`, 'success')
                        } else {
                            log(`❌ 资源缺失: ${resource}`, 'error')
                        }
                        
                        if (checkedCount === resources.length) {
                            showResult('resourceResults', '游戏资源', successCount === resources.length, 
                                `${successCount}/${resources.length} 个资源可用`)
                        }
                    })
                    .catch(error => {
                        checkedCount++
                        log(`❌ 资源检查失败: ${resource} - ${error.message}`, 'error')
                        
                        if (checkedCount === resources.length) {
                            showResult('resourceResults', '游戏资源', successCount === resources.length, 
                                `${successCount}/${resources.length} 个资源可用`)
                        }
                    })
            })
        }

        // 检查图片资源
        function checkImages() {
            log('🔍 检查图片资源', 'info')
            
            const img = new Image()
            img.onload = function() {
                showResult('resourceResults', '图片加载', true, '图片资源加载正常')
                log('✅ 图片资源加载正常', 'success')
            }
            img.onerror = function() {
                showResult('resourceResults', '图片加载', false, '图片资源加载失败')
                log('❌ 图片资源加载失败', 'error')
            }
            img.src = '/resources/UI/card/card.png'
        }

        // 检查脚本文件
        function checkScripts() {
            log('🔍 检查脚本文件', 'info')
            
            // 检查Phaser是否可用
            if (typeof Phaser !== 'undefined') {
                showResult('resourceResults', 'Phaser脚本', true, `Phaser ${Phaser.VERSION} 已加载`)
                log(`✅ Phaser ${Phaser.VERSION} 已加载`, 'success')
            } else {
                // 尝试加载Phaser
                const script = document.createElement('script')
                script.src = 'https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js'
                script.onload = function() {
                    showResult('resourceResults', 'Phaser脚本', true, 'Phaser动态加载成功')
                    log('✅ Phaser动态加载成功', 'success')
                }
                script.onerror = function() {
                    showResult('resourceResults', 'Phaser脚本', false, 'Phaser加载失败')
                    log('❌ Phaser加载失败', 'error')
                }
                document.head.appendChild(script)
            }
        }

        // 测试Phaser
        function testPhaser() {
            log('🧪 测试Phaser功能', 'info')
            
            if (typeof Phaser === 'undefined') {
                showResult('gameResults', 'Phaser测试', false, 'Phaser未加载')
                log('❌ Phaser未加载', 'error')
                return
            }
            
            try {
                const config = {
                    type: Phaser.AUTO,
                    width: 400,
                    height: 300,
                    parent: document.body,
                    scene: {
                        create: function() {
                            this.add.text(200, 150, 'Phaser测试成功!', {
                                fontSize: '16px',
                                fill: '#ffffff'
                            }).setOrigin(0.5)
                            
                            setTimeout(() => {
                                this.game.destroy(true)
                                showResult('gameResults', 'Phaser测试', true, 'Phaser功能正常')
                                log('✅ Phaser功能正常', 'success')
                            }, 2000)
                        }
                    }
                }
                
                new Phaser.Game(config)
            } catch (error) {
                showResult('gameResults', 'Phaser测试', false, `Phaser错误: ${error.message}`)
                log(`❌ Phaser错误: ${error.message}`, 'error')
            }
        }

        // 测试React
        function testReact() {
            log('🧪 测试React功能', 'info')
            
            // 检查React是否在主应用中可用
            fetch('/src/main.jsx')
                .then(response => {
                    if (response.ok) {
                        showResult('gameResults', 'React测试', true, 'React应用文件存在')
                        log('✅ React应用文件存在', 'success')
                    } else {
                        showResult('gameResults', 'React测试', false, 'React应用文件不存在')
                        log('❌ React应用文件不存在', 'error')
                    }
                })
                .catch(error => {
                    showResult('gameResults', 'React测试', false, `React检查失败: ${error.message}`)
                    log(`❌ React检查失败: ${error.message}`, 'error')
                })
        }

        // 快速游戏测试
        function quickGameTest() {
            log('🚀 开始快速游戏测试', 'info')
            
            // 依次执行各项测试
            setTimeout(() => testMainApp(), 100)
            setTimeout(() => checkResources(), 500)
            setTimeout(() => checkScripts(), 1000)
            setTimeout(() => {
                showResult('gameResults', '快速测试', true, '快速测试完成，请查看各项结果')
                log('🎉 快速游戏测试完成', 'success')
            }, 1500)
        }

        // 清空日志
        function clearLog() {
            document.getElementById('debugLog').textContent = '日志已清空\n'
        }

        // 导出日志
        function exportLog() {
            const logContent = document.getElementById('debugLog').textContent
            const blob = new Blob([logContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `debug-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            log('📄 调试日志已导出', 'success')
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initBasicInfo()
            log('🔍 调试页面已加载完成', 'success')
            log('请点击各项测试按钮检查项目状态', 'info')
        })
    </script>
</body>
</html>
