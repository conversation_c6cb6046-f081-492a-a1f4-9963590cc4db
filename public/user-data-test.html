<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👤 用户数据测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        
        button.danger {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .data-display {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            border: 1px solid #333;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 用户数据测试与验证</h1>
            <p>检查和设置用户登录数据</p>
        </div>

        <div class="section">
            <h3>📊 当前用户数据状态</h3>
            <div class="controls">
                <button onclick="checkCurrentData()">检查当前数据</button>
                <button onclick="refreshData()">刷新数据</button>
                <button class="danger" onclick="clearAllData()">清除所有数据</button>
            </div>
            <div id="currentStatus" class="status info">点击"检查当前数据"查看状态</div>
            <div id="currentData" class="data-display">等待检查...</div>
        </div>

        <div class="section">
            <h3>🔧 设置模拟用户数据</h3>
            <div class="controls">
                <button class="primary" onclick="setMockUserData()">设置标准模拟数据</button>
                <button onclick="setCompleteUserData()">设置完整用户数据</button>
                <button onclick="setMinimalUserData()">设置最小用户数据</button>
            </div>
            <div id="setStatus" class="status info">选择一种数据设置方式</div>
        </div>

        <div class="section">
            <h3>🧪 React应用兼容性测试</h3>
            <div class="controls">
                <button onclick="testReactCompatibility()">测试React兼容性</button>
                <button onclick="simulateReactLogin()">模拟React登录流程</button>
                <button class="primary" onclick="goToMainApp()">跳转到主应用</button>
            </div>
            <div id="reactStatus" class="status info">测试React应用数据兼容性</div>
        </div>

        <div class="section">
            <h3>📝 详细数据显示</h3>
            <div class="controls">
                <button onclick="showDetailedData()">显示详细数据</button>
                <button onclick="exportData()">导出数据</button>
            </div>
            <div id="detailedData" class="data-display">点击"显示详细数据"查看</div>
        </div>
    </div>

    <script>
        function showStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId)
            container.className = `status ${type}`
            container.textContent = message
        }

        function showData(containerId, data) {
            const container = document.getElementById(containerId)
            container.textContent = JSON.stringify(data, null, 2)
        }

        // 检查当前数据
        function checkCurrentData() {
            console.log('=== 检查当前用户数据 ===')
            
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            console.log('gameStore (raw):', gameStore)
            console.log('token:', token)
            
            if (!gameStore || !token) {
                showStatus('currentStatus', '❌ 没有找到用户数据', 'error')
                showData('currentData', {
                    gameStore: gameStore,
                    token: token,
                    message: '用户数据不存在'
                })
                return
            }
            
            try {
                const parsedGameStore = JSON.parse(gameStore)
                console.log('gameStore (parsed):', parsedGameStore)
                
                if (parsedGameStore.user) {
                    showStatus('currentStatus', `✅ 找到用户: ${parsedGameStore.user.nickname}`, 'success')
                } else {
                    showStatus('currentStatus', '⚠️ gameStore存在但缺少用户信息', 'error')
                }
                
                showData('currentData', {
                    gameStore: parsedGameStore,
                    token: token,
                    hasUser: !!parsedGameStore.user,
                    userInfo: parsedGameStore.user || null
                })
                
            } catch (error) {
                showStatus('currentStatus', '❌ 数据解析失败', 'error')
                showData('currentData', {
                    error: error.message,
                    gameStore: gameStore,
                    token: token
                })
                console.error('解析错误:', error)
            }
        }

        // 设置标准模拟数据
        function setMockUserData() {
            console.log('=== 设置标准模拟用户数据 ===')
            
            const mockUser = {
                id: Date.now(),
                openid: 'mock_openid_' + Date.now(),
                nickname: '测试玩家',
                headerimg: '/images/headimage/avatar_1.png',
                unionid: 'mock_unionid_' + Date.now(),
                time: new Date().toISOString()
            }
            
            const gameStoreData = {
                user: mockUser,
                token: null,
                currentGame: null,
                currentRoom: null,
                gameState: 'idle'
            }
            
            const token = 'mock_token_' + Date.now()
            
            localStorage.setItem('gameStore', JSON.stringify(gameStoreData))
            localStorage.setItem('token', token)
            
            console.log('设置的用户数据:', mockUser)
            console.log('设置的gameStore:', gameStoreData)
            console.log('设置的token:', token)
            
            showStatus('setStatus', '✅ 标准模拟数据设置成功', 'success')
            
            // 自动检查设置结果
            setTimeout(checkCurrentData, 500)
        }

        // 设置完整用户数据
        function setCompleteUserData() {
            console.log('=== 设置完整用户数据 ===')
            
            const mockUser = {
                id: Date.now(),
                openid: 'mock_openid_' + Date.now(),
                nickname: '完整测试玩家',
                headerimg: '/images/headimage/avatar_1.png',
                avatar: '/images/headimage/avatar_1.png',
                unionid: 'mock_unionid_' + Date.now(),
                time: new Date().toISOString(),
                // 添加可能需要的额外字段
                phone: '13800138000',
                email: '<EMAIL>',
                level: 1,
                score: 1000
            }
            
            const gameStoreData = {
                user: mockUser,
                token: 'mock_token_' + Date.now(),
                currentGame: null,
                currentRoom: null,
                gameState: 'idle',
                roomInfo: null,
                players: [],
                currentPlayer: null,
                landlordId: null,
                handCards: [],
                lastPlayedCards: null,
                currentBidScore: 0,
                bidHistory: [],
                gameConfig: null
            }
            
            const token = 'mock_token_' + Date.now()
            
            localStorage.setItem('gameStore', JSON.stringify(gameStoreData))
            localStorage.setItem('token', token)
            
            console.log('设置的完整用户数据:', mockUser)
            console.log('设置的完整gameStore:', gameStoreData)
            
            showStatus('setStatus', '✅ 完整用户数据设置成功', 'success')
            
            // 自动检查设置结果
            setTimeout(checkCurrentData, 500)
        }

        // 设置最小用户数据
        function setMinimalUserData() {
            console.log('=== 设置最小用户数据 ===')
            
            const mockUser = {
                id: Date.now(),
                nickname: '最小测试玩家'
            }
            
            const gameStoreData = {
                user: mockUser
            }
            
            const token = 'mock_token_' + Date.now()
            
            localStorage.setItem('gameStore', JSON.stringify(gameStoreData))
            localStorage.setItem('token', token)
            
            console.log('设置的最小用户数据:', mockUser)
            
            showStatus('setStatus', '✅ 最小用户数据设置成功', 'success')
            
            // 自动检查设置结果
            setTimeout(checkCurrentData, 500)
        }

        // 测试React兼容性
        function testReactCompatibility() {
            console.log('=== 测试React应用兼容性 ===')
            
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            if (!gameStore || !token) {
                showStatus('reactStatus', '❌ 没有用户数据，请先设置', 'error')
                return
            }
            
            try {
                const data = JSON.parse(gameStore)
                
                // 检查React应用需要的字段
                const checks = [
                    { name: 'user对象', value: !!data.user },
                    { name: 'user.id', value: !!(data.user && data.user.id) },
                    { name: 'user.nickname', value: !!(data.user && data.user.nickname) },
                    { name: 'token', value: !!token },
                    { name: 'gameState', value: !!data.gameState }
                ]
                
                const passedChecks = checks.filter(check => check.value).length
                const totalChecks = checks.length
                
                console.log('React兼容性检查结果:', checks)
                
                if (passedChecks === totalChecks) {
                    showStatus('reactStatus', `✅ React兼容性测试通过 (${passedChecks}/${totalChecks})`, 'success')
                } else {
                    showStatus('reactStatus', `⚠️ React兼容性测试部分通过 (${passedChecks}/${totalChecks})`, 'error')
                }
                
            } catch (error) {
                showStatus('reactStatus', '❌ React兼容性测试失败', 'error')
                console.error('兼容性测试错误:', error)
            }
        }

        // 模拟React登录流程
        function simulateReactLogin() {
            console.log('=== 模拟React登录流程 ===')
            
            // 清除现有数据
            localStorage.removeItem('gameStore')
            localStorage.removeItem('token')
            
            // 设置符合React应用期望的数据结构
            const mockUser = {
                id: Date.now(),
                openid: 'mock_openid_' + Date.now(),
                nickname: 'React测试玩家',
                headerimg: '/images/headimage/avatar_1.png',
                unionid: 'mock_unionid_' + Date.now(),
                time: new Date().toISOString()
            }
            
            // 使用与React应用相同的数据结构
            const gameStoreData = {
                user: mockUser,
                currentGame: null,
                currentRoom: null,
                gameState: 'idle'
            }
            
            const token = 'mock_token_' + Date.now()
            
            // 分别设置，模拟React应用的设置方式
            localStorage.setItem('gameStore', JSON.stringify(gameStoreData))
            localStorage.setItem('token', token)
            
            console.log('React登录流程完成')
            console.log('用户数据:', mockUser)
            console.log('gameStore:', gameStoreData)
            console.log('token:', token)
            
            showStatus('reactStatus', '✅ React登录流程模拟完成', 'success')
            
            // 自动检查设置结果
            setTimeout(() => {
                checkCurrentData()
                testReactCompatibility()
            }, 500)
        }

        // 跳转到主应用
        function goToMainApp() {
            console.log('=== 准备跳转到主应用 ===')
            
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            if (!gameStore || !token) {
                showStatus('reactStatus', '❌ 请先设置用户数据', 'error')
                return
            }
            
            showStatus('reactStatus', '🚀 正在跳转到主应用...', 'info')
            
            setTimeout(() => {
                window.location.href = '/'
            }, 1000)
        }

        // 刷新数据
        function refreshData() {
            checkCurrentData()
        }

        // 清除所有数据
        function clearAllData() {
            localStorage.removeItem('gameStore')
            localStorage.removeItem('token')
            
            showStatus('currentStatus', '✅ 所有数据已清除', 'success')
            showData('currentData', { message: '所有数据已清除' })
            
            console.log('所有用户数据已清除')
        }

        // 显示详细数据
        function showDetailedData() {
            const allData = {
                localStorage: {
                    gameStore: localStorage.getItem('gameStore'),
                    token: localStorage.getItem('token')
                },
                parsedGameStore: null,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            }
            
            try {
                if (allData.localStorage.gameStore) {
                    allData.parsedGameStore = JSON.parse(allData.localStorage.gameStore)
                }
            } catch (error) {
                allData.parseError = error.message
            }
            
            showData('detailedData', allData)
            console.log('详细数据:', allData)
        }

        // 导出数据
        function exportData() {
            showDetailedData()
            
            const data = document.getElementById('detailedData').textContent
            const blob = new Blob([data], { type: 'application/json' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `user-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            
            console.log('用户数据已导出')
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户数据测试页面已加载')
            checkCurrentData()
        })
    </script>
</body>
</html>
