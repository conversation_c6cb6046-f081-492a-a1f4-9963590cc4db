<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 恢复出牌操作</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
            font-size: 18px;
            padding: 20px 30px;
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 16px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196F3;
            color: #2196F3;
        }
        
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            color: #FFC107;
        }
        
        .problem-analysis {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid #FFC107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .problem-analysis h4 {
            color: #FFC107;
            margin-top: 0;
        }
        
        .solution-step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 恢复出牌操作</h1>
            <p>快速恢复消失的出牌按钮和操作功能</p>
        </div>

        <div class="problem-analysis">
            <h4>🔍 问题分析</h4>
            <div class="solution-step">
                <strong>现象：</strong> 游戏已进入出牌阶段，但缺少"出牌"、"不要"、"提示"按钮
            </div>
            <div class="solution-step">
                <strong>原因：</strong> 
                <br>• 出牌UI组件没有正确显示
                <br>• 按钮被隐藏或销毁
                <br>• 事件监听器失效
                <br>• UI状态更新异常
            </div>
            <div class="solution-step">
                <strong>解决：</strong> 强制重新创建和显示出牌操作按钮
            </div>
        </div>

        <div class="section">
            <h3>🚀 一键恢复</h3>
            <div class="controls">
                <button class="primary" onclick="restorePlayingUI()">🎮 恢复出牌操作</button>
                <button onclick="forceShowButtons()">🔘 强制显示按钮</button>
                <button class="warning" onclick="recreateButtons()">🔧 重新创建按钮</button>
            </div>
            <div id="restoreStatus" class="status info">点击"恢复出牌操作"来恢复缺失的按钮</div>
        </div>

        <div class="section">
            <h3>🔍 诊断检查</h3>
            <div class="controls">
                <button onclick="checkUIState()">📊 检查UI状态</button>
                <button onclick="checkButtons()">🔘 检查按钮状态</button>
                <button onclick="checkGamePhase()">🎯 检查游戏阶段</button>
                <button onclick="checkEventListeners()">📡 检查事件监听</button>
            </div>
            <div id="diagnosisResults"></div>
        </div>

        <div class="section">
            <h3>🛠️ 手动修复</h3>
            <div class="controls">
                <button onclick="enableCardSelection()">🎴 启用卡牌选择</button>
                <button onclick="createPlayButton()">▶️ 创建出牌按钮</button>
                <button onclick="createPassButton()">⏭️ 创建不要按钮</button>
                <button onclick="createHintButton()">💡 创建提示按钮</button>
            </div>
            <div id="manualResults"></div>
        </div>

        <div class="section">
            <h3>🎮 游戏控制</h3>
            <div class="controls">
                <button onclick="goToGame()">🎯 返回游戏</button>
                <button onclick="openConsole()">🔍 打开控制台</button>
                <button onclick="testCardSelection()">🧪 测试卡牌选择</button>
            </div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="operationLog" style="background: rgba(0,0,0,0.9); padding: 15px; border-radius: 10px; font-family: 'Consolas', monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                出牌操作恢复工具已加载...
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('operationLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('restoreStatus')
            statusDiv.className = `status ${type}`
            statusDiv.textContent = message
        }
        
        // 恢复出牌操作
        function restorePlayingUI() {
            log('🎮 开始恢复出牌操作', 'info')
            showStatus('正在恢复出牌操作...', 'info')
            
            const restoreScript = `
// 恢复出牌操作
console.log('🎮 开始恢复出牌操作')

if (window.gameScene) {
    // 1. 检查游戏状态
    const gameInfo = window.gameScene.gameState ? window.gameScene.gameState.getGameInfo() : null
    console.log('当前游戏状态:', gameInfo)
    
    if (gameInfo && gameInfo.phase === 'playing') {
        console.log('✅ 游戏在出牌阶段，开始恢复UI')
        
        // 2. 强制显示出牌UI
        console.log('🎯 强制显示出牌UI')
        window.gameScene.showPlayingUI()
        
        // 3. 确保按钮存在
        console.log('🔘 确保出牌按钮存在')
        window.gameScene.ensurePlayingButtonsExist()
        
        // 4. 强制显示按钮
        console.log('💪 强制显示出牌按钮')
        window.gameScene.forceShowPlayingButtons()
        
        // 5. 启用卡牌交互
        console.log('🎴 启用卡牌交互')
        if (window.gameScene.playingInteractionManager) {
            window.gameScene.playingInteractionManager.initialize()
        }
        
        // 6. 验证按钮状态
        setTimeout(() => {
            console.log('🔍 验证按钮状态')
            if (window.gameScene.uiElements.playCardsButton) {
                console.log('✅ 出牌按钮存在:', window.gameScene.uiElements.playCardsButton.visible)
            }
            if (window.gameScene.uiElements.passButton) {
                console.log('✅ 不要按钮存在:', window.gameScene.uiElements.passButton.visible)
            }
            if (window.gameScene.uiElements.hintButton) {
                console.log('✅ 提示按钮存在:', window.gameScene.uiElements.hintButton.visible)
            }
            
            console.log('🎉 出牌操作恢复完成！')
        }, 1000)
        
    } else {
        console.log('❌ 游戏不在出牌阶段，当前阶段:', gameInfo ? gameInfo.phase : '未知')
        
        // 强制切换到出牌阶段
        if (window.gameScene.gameState) {
            console.log('⚡ 强制切换到出牌阶段')
            window.gameScene.gameState.startPlaying()
            window.gameScene.updateUI()
            
            setTimeout(() => {
                window.gameScene.showPlayingUI()
                window.gameScene.forceShowPlayingButtons()
            }, 1000)
        }
    }
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(restoreScript, '恢复出牌操作脚本')
        }
        
        // 强制显示按钮
        function forceShowButtons() {
            log('🔘 强制显示按钮', 'info')
            
            const script = `
// 强制显示出牌按钮
console.log('🔘 强制显示出牌按钮')

if (window.gameScene) {
    // 强制显示出牌按钮
    if (window.gameScene.uiElements.playCardsButton) {
        window.gameScene.uiElements.playCardsButton.setVisible(true)
        window.gameScene.uiElements.playCardsButton.setDepth(10000)
        console.log('✅ 出牌按钮已显示')
    }
    
    // 强制显示不要按钮
    if (window.gameScene.uiElements.passButton) {
        window.gameScene.uiElements.passButton.setVisible(true)
        window.gameScene.uiElements.passButton.setDepth(10000)
        console.log('✅ 不要按钮已显示')
    }
    
    // 强制显示提示按钮
    if (window.gameScene.uiElements.hintButton) {
        window.gameScene.uiElements.hintButton.setVisible(true)
        window.gameScene.uiElements.hintButton.setDepth(10000)
        console.log('✅ 提示按钮已显示')
    }
    
    // 如果按钮不存在，强制创建
    if (!window.gameScene.uiElements.playCardsButton || !window.gameScene.uiElements.passButton) {
        console.log('🔧 按钮不存在，强制创建')
        window.gameScene.ensurePlayingButtonsExist()
        window.gameScene.forceShowPlayingButtons()
    }
    
    console.log('✅ 按钮显示完成')
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(script, '强制显示按钮脚本')
        }
        
        // 重新创建按钮
        function recreateButtons() {
            log('🔧 重新创建按钮', 'warning')
            
            const script = `
// 重新创建出牌按钮
console.log('🔧 重新创建出牌按钮')

if (window.gameScene) {
    // 销毁现有按钮
    if (window.gameScene.uiElements.playCardsButton) {
        window.gameScene.uiElements.playCardsButton.destroy()
        window.gameScene.uiElements.playCardsButton = null
    }
    if (window.gameScene.uiElements.passButton) {
        window.gameScene.uiElements.passButton.destroy()
        window.gameScene.uiElements.passButton = null
    }
    if (window.gameScene.uiElements.hintButton) {
        window.gameScene.uiElements.hintButton.destroy()
        window.gameScene.uiElements.hintButton = null
    }
    
    console.log('🗑️ 旧按钮已销毁')
    
    // 重新创建按钮
    const centerX = 600
    const buttonY = 650
    
    // 创建出牌按钮
    window.gameScene.uiElements.playCardsButton = window.gameScene.createStandardButton(
        centerX - 80, buttonY, '出牌', 'orange', 
        () => window.gameScene.playSelectedCards()
    )
    
    // 创建不要按钮
    window.gameScene.uiElements.passButton = window.gameScene.createStandardButton(
        centerX + 80, buttonY, '不要', 'blue', 
        () => window.gameScene.passCards()
    )
    
    // 创建提示按钮
    window.gameScene.uiElements.hintButton = window.gameScene.createStandardButton(
        centerX, buttonY + 60, '提示', 'gray', 
        () => window.gameScene.showCardHint()
    )
    
    console.log('✅ 新按钮创建完成')
    
    // 显示按钮
    window.gameScene.uiElements.playCardsButton.setVisible(true)
    window.gameScene.uiElements.passButton.setVisible(true)
    window.gameScene.uiElements.hintButton.setVisible(true)
    
    console.log('✅ 按钮重新创建完成')
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(script, '重新创建按钮脚本')
        }
        
        // 检查UI状态
        function checkUIState() {
            log('📊 检查UI状态', 'info')
            
            const script = `
// 检查UI状态
console.log('📊 检查UI状态')

if (window.gameScene) {
    console.log('=== 游戏场景状态 ===')
    console.log('gameScene存在:', !!window.gameScene)
    console.log('gameState存在:', !!window.gameScene.gameState)
    console.log('uiElements存在:', !!window.gameScene.uiElements)
    
    if (window.gameScene.gameState) {
        const gameInfo = window.gameScene.gameState.getGameInfo()
        console.log('=== 游戏状态 ===')
        console.log('游戏阶段:', gameInfo.phase)
        console.log('当前玩家:', gameInfo.currentPlayer)
        console.log('是否轮到玩家:', gameInfo.isMyTurn)
    }
    
    if (window.gameScene.uiElements) {
        console.log('=== UI元素状态 ===')
        console.log('出牌按钮存在:', !!window.gameScene.uiElements.playCardsButton)
        console.log('不要按钮存在:', !!window.gameScene.uiElements.passButton)
        console.log('提示按钮存在:', !!window.gameScene.uiElements.hintButton)
        
        if (window.gameScene.uiElements.playCardsButton) {
            console.log('出牌按钮可见:', window.gameScene.uiElements.playCardsButton.visible)
            console.log('出牌按钮激活:', window.gameScene.uiElements.playCardsButton.active)
        }
        
        if (window.gameScene.uiElements.passButton) {
            console.log('不要按钮可见:', window.gameScene.uiElements.passButton.visible)
            console.log('不要按钮激活:', window.gameScene.uiElements.passButton.active)
        }
    }
    
    console.log('✅ UI状态检查完成')
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(script, 'UI状态检查脚本')
        }
        
        // 测试卡牌选择
        function testCardSelection() {
            log('🧪 测试卡牌选择', 'info')
            
            const script = `
// 测试卡牌选择
console.log('🧪 测试卡牌选择功能')

if (window.gameScene) {
    // 检查手牌
    if (window.gameScene.playerCards && window.gameScene.playerCards.length > 0) {
        console.log('手牌数量:', window.gameScene.playerCards.length)
        
        // 尝试选择第一张牌
        const firstCard = window.gameScene.playerCards[0]
        if (firstCard && firstCard.sprite) {
            console.log('尝试选择第一张牌')
            window.gameScene.selectCard(firstCard)
            
            setTimeout(() => {
                console.log('当前选中牌数量:', window.gameScene.selectedCards.length)
                if (window.gameScene.selectedCards.length > 0) {
                    console.log('✅ 卡牌选择功能正常')
                } else {
                    console.log('❌ 卡牌选择功能异常')
                }
            }, 500)
        }
    } else {
        console.log('❌ 没有找到手牌')
    }
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(script, '测试卡牌选择脚本')
        }
        
        // 复制脚本并显示说明
        function copyScriptAndShowInstructions(script, scriptName) {
            navigator.clipboard.writeText(script).then(() => {
                showStatus(`✅ ${scriptName}已复制！请在游戏页面控制台执行`, 'success')
                log(`✅ ${scriptName}已复制到剪贴板`, 'success')
                
                setTimeout(() => {
                    alert(`${scriptName}已复制到剪贴板！

请按以下步骤操作：
1. 切换到游戏页面
2. 按F12打开浏览器控制台
3. 粘贴脚本并按回车执行

或者点击"返回游戏"按钮，然后在控制台中粘贴执行。`)
                }, 1000)
                
            }).catch(err => {
                showStatus('❌ 复制失败，请手动复制脚本', 'error')
                log('❌ 复制失败，请手动复制脚本', 'error')
            })
        }
        
        // 其他功能
        function goToGame() {
            log('🎯 返回游戏页面', 'info')
            window.open('/hall', '_blank')
        }
        
        function openConsole() {
            log('🔍 提示打开控制台', 'info')
            alert('请按F12键打开浏览器开发者工具控制台')
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎮 出牌操作恢复工具已加载', 'success')
            log('检测到出牌操作缺失，点击"恢复出牌操作"来修复', 'info')
        })
    </script>
</body>
</html>
