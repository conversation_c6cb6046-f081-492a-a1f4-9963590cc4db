<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 修复AI叫牌卡住问题</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #FFD700;
        }
        
        .section {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
            font-size: 18px;
            padding: 20px 30px;
        }
        
        button.danger {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 16px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196F3;
            color: #2196F3;
        }
        
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            color: #FFC107;
        }
        
        .log-display {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            border: 1px solid #333;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .problem-analysis {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid #FFC107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .problem-analysis h4 {
            color: #FFC107;
            margin-top: 0;
        }
        
        .solution-step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 修复AI叫牌卡住问题</h1>
            <p>诊断并修复AI玩家叫牌流程卡住的问题</p>
        </div>

        <div class="problem-analysis">
            <h4>🔍 问题分析</h4>
            <div class="solution-step">
                <strong>问题现象：</strong> 游戏显示"叫牌阶段 - 东家出牌"，但AI玩家不自动叫牌，流程卡住
            </div>
            <div class="solution-step">
                <strong>可能原因：</strong> 
                <br>• AI叫牌逻辑有冲突（BiddingManager vs GameScene）
                <br>• 超时处理机制失效
                <br>• 事件监听器未正确触发
                <br>• 叫牌状态管理混乱
            </div>
            <div class="solution-step">
                <strong>解决策略：</strong> 强制推进叫牌流程，清理冲突逻辑，直接进入出牌阶段
            </div>
        </div>

        <div class="section">
            <h3>🚀 一键修复</h3>
            <div class="controls">
                <button class="primary" onclick="fixAIBiddingIssue()">🔧 修复AI叫牌卡住</button>
                <button onclick="forceCompleteBidding()">⚡ 强制完成叫牌</button>
                <button class="warning" onclick="resetBiddingSystem()">🔄 重置叫牌系统</button>
            </div>
            <div id="fixStatus" class="status info">点击"修复AI叫牌卡住"来解决问题</div>
        </div>

        <div class="section">
            <h3>🔍 诊断工具</h3>
            <div class="controls">
                <button onclick="diagnoseBiddingState()">📊 诊断叫牌状态</button>
                <button onclick="checkAILogic()">🤖 检查AI逻辑</button>
                <button onclick="checkEventListeners()">📡 检查事件监听</button>
                <button onclick="checkTimers()">⏰ 检查计时器</button>
            </div>
            <div id="diagnosisResults"></div>
        </div>

        <div class="section">
            <h3>🛠️ 手动控制</h3>
            <div class="controls">
                <button onclick="manualAIBid('no_bid')">🤖 AI不叫</button>
                <button onclick="manualAIBid('one_point')">🤖 AI叫1分</button>
                <button onclick="manualAIBid('two_point')">🤖 AI叫2分</button>
                <button class="danger" onclick="skipToPlayingPhase()">⚡ 直接跳到出牌</button>
            </div>
            <div id="manualResults"></div>
        </div>

        <div class="section">
            <h3>🎮 游戏控制</h3>
            <div class="controls">
                <button onclick="goToGame()">🎯 返回游戏</button>
                <button onclick="openConsole()">🔍 打开控制台</button>
                <button onclick="refreshGame()">🔄 刷新游戏</button>
            </div>
        </div>

        <div class="section">
            <h3>📝 修复日志</h3>
            <div class="controls">
                <button onclick="clearLog()">清空日志</button>
                <button onclick="exportLog()">导出日志</button>
            </div>
            <div id="fixLog" class="log-display">AI叫牌修复工具已加载...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('fixLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }
        
        function showStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId)
            container.className = `status ${type}`
            container.textContent = message
        }
        
        // 修复AI叫牌卡住问题
        function fixAIBiddingIssue() {
            log('🔧 开始修复AI叫牌卡住问题', 'info')
            showStatus('fixStatus', '正在修复AI叫牌问题...', 'info')
            
            const fixScript = `
// 修复AI叫牌卡住问题
console.log('🔧 开始修复AI叫牌卡住问题')

if (window.gameScene) {
    // 1. 检查当前状态
    console.log('📊 检查当前游戏状态')
    const gameInfo = window.gameScene.gameState ? window.gameScene.gameState.getGameInfo() : null
    console.log('游戏状态:', gameInfo)
    
    // 2. 检查叫牌管理器状态
    if (window.gameScene.biddingManager) {
        const biddingState = window.gameScene.biddingManager.getCurrentState()
        console.log('叫牌状态:', biddingState)
        
        // 3. 清除所有计时器
        console.log('🧹 清除所有计时器')
        window.gameScene.biddingManager.clearTimeout()
        
        // 4. 强制推进AI叫牌
        const currentBidder = biddingState.biddingStatus.currentBidder
        if (currentBidder && currentBidder !== 'south') {
            console.log('🤖 强制AI叫牌:', currentBidder)
            
            // 简单策略：AI不叫
            const result = window.gameScene.biddingManager.simulateOtherPlayerBid(currentBidder, 'no_bid')
            console.log('AI叫牌结果:', result)
            
            if (!result.success) {
                // 如果模拟失败，直接完成叫牌
                console.log('⚡ 模拟失败，强制完成叫牌')
                window.gameScene.gameState.setLandlord(1) // 设置玩家为地主
                window.gameScene.gameState.startPlaying()
                window.gameScene.updateUI()
                
                setTimeout(() => {
                    window.gameScene.showPlayingUI()
                    window.gameScene.forceShowPlayingButtons()
                }, 1000)
            }
        } else if (currentBidder === 'south') {
            console.log('🎯 轮到玩家叫牌，显示叫牌UI')
            window.gameScene.showBiddingUIWithManager(biddingState)
        } else {
            console.log('⚡ 叫牌状态异常，强制完成叫牌')
            window.gameScene.gameState.setLandlord(1)
            window.gameScene.gameState.startPlaying()
            window.gameScene.updateUI()
            
            setTimeout(() => {
                window.gameScene.showPlayingUI()
                window.gameScene.forceShowPlayingButtons()
            }, 1000)
        }
    } else {
        console.log('❌ 叫牌管理器不存在，直接跳到出牌阶段')
        window.gameScene.gameState.setLandlord(1)
        window.gameScene.gameState.startPlaying()
        window.gameScene.updateUI()
        
        setTimeout(() => {
            window.gameScene.showPlayingUI()
            window.gameScene.forceShowPlayingButtons()
        }, 1000)
    }
    
    console.log('✅ AI叫牌修复完成')
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(fixScript, 'AI叫牌修复脚本')
        }
        
        // 强制完成叫牌
        function forceCompleteBidding() {
            log('⚡ 强制完成叫牌流程', 'info')
            
            const script = `
// 强制完成叫牌
console.log('⚡ 强制完成叫牌流程')

if (window.gameScene && window.gameScene.gameState) {
    // 设置玩家为地主
    window.gameScene.gameState.setLandlord(1)
    console.log('✅ 设置玩家为地主')
    
    // 切换到出牌阶段
    window.gameScene.gameState.startPlaying()
    console.log('✅ 切换到出牌阶段')
    
    // 更新UI
    window.gameScene.updateUI()
    console.log('✅ UI已更新')
    
    // 显示出牌UI
    setTimeout(() => {
        window.gameScene.showPlayingUI()
        window.gameScene.forceShowPlayingButtons()
        console.log('✅ 出牌UI已显示')
    }, 1000)
    
    console.log('🎉 强制完成叫牌成功！')
} else {
    console.error('❌ 游戏场景或状态不存在')
}
            `
            
            copyScriptAndShowInstructions(script, '强制完成叫牌脚本')
        }
        
        // 重置叫牌系统
        function resetBiddingSystem() {
            log('🔄 重置叫牌系统', 'warning')
            
            const script = `
// 重置叫牌系统
console.log('🔄 重置叫牌系统')

if (window.gameScene) {
    // 重置叫牌管理器
    if (window.gameScene.biddingManager) {
        window.gameScene.biddingManager.reset()
        console.log('✅ 叫牌管理器已重置')
    }
    
    // 重新开始游戏
    window.gameScene.restartGame()
    console.log('✅ 游戏已重新开始')
} else {
    console.error('❌ 游戏场景不存在')
    location.reload()
}
            `
            
            copyScriptAndShowInstructions(script, '重置叫牌系统脚本')
        }
        
        // 诊断叫牌状态
        function diagnoseBiddingState() {
            log('📊 诊断叫牌状态', 'info')
            
            const script = `
// 诊断叫牌状态
console.log('📊 开始诊断叫牌状态')

if (window.gameScene) {
    console.log('=== 游戏场景状态 ===')
    console.log('gameScene存在:', !!window.gameScene)
    console.log('gameState存在:', !!window.gameScene.gameState)
    console.log('biddingManager存在:', !!window.gameScene.biddingManager)
    
    if (window.gameScene.gameState) {
        const gameInfo = window.gameScene.gameState.getGameInfo()
        console.log('=== 游戏状态 ===')
        console.log('游戏阶段:', gameInfo.phase)
        console.log('当前玩家:', gameInfo.currentPlayer)
        console.log('地主:', gameInfo.landlord)
    }
    
    if (window.gameScene.biddingManager) {
        const biddingState = window.gameScene.biddingManager.getCurrentState()
        console.log('=== 叫牌状态 ===')
        console.log('叫牌阶段:', biddingState.gameState.phase)
        console.log('当前叫牌者:', biddingState.biddingStatus.currentBidder)
        console.log('最高叫分:', biddingState.biddingStatus.highestBid)
        console.log('叫牌历史:', biddingState.biddingStatus.biddingHistory)
        console.log('是否轮到玩家:', biddingState.isMyTurn)
        console.log('剩余时间:', window.gameScene.biddingManager.getRemainingTime())
    }
    
    console.log('✅ 诊断完成，请查看上方详细信息')
} else {
    console.error('❌ 游戏场景不存在')
}
            `
            
            copyScriptAndShowInstructions(script, '诊断叫牌状态脚本')
        }
        
        // 手动AI叫牌
        function manualAIBid(bidOption) {
            log(`🤖 手动AI叫牌: ${bidOption}`, 'info')
            
            const script = `
// 手动AI叫牌
console.log('🤖 手动AI叫牌: ${bidOption}')

if (window.gameScene && window.gameScene.biddingManager) {
    const biddingState = window.gameScene.biddingManager.getCurrentState()
    const currentBidder = biddingState.biddingStatus.currentBidder
    
    if (currentBidder && currentBidder !== 'south') {
        console.log('执行AI叫牌:', currentBidder, '${bidOption}')
        const result = window.gameScene.biddingManager.simulateOtherPlayerBid(currentBidder, '${bidOption}')
        console.log('叫牌结果:', result)
        
        if (result.success) {
            console.log('✅ AI叫牌成功')
        } else {
            console.log('❌ AI叫牌失败:', result.reason)
        }
    } else {
        console.log('❌ 当前不是AI叫牌回合')
    }
} else {
    console.error('❌ 游戏场景或叫牌管理器不存在')
}
            `
            
            copyScriptAndShowInstructions(script, `手动AI叫牌(${bidOption})脚本`)
        }
        
        // 直接跳到出牌阶段
        function skipToPlayingPhase() {
            log('⚡ 直接跳到出牌阶段', 'warning')
            
            if (confirm('确定要直接跳到出牌阶段吗？这将跳过叫牌流程。')) {
                const script = `
// 直接跳到出牌阶段
console.log('⚡ 直接跳到出牌阶段')

if (window.gameScene && window.gameScene.gameState) {
    // 强制设置游戏状态
    window.gameScene.gameState.setLandlord(1) // 玩家为地主
    window.gameScene.gameState.startPlaying()
    
    // 更新UI
    window.gameScene.updateUI()
    
    // 显示出牌UI
    setTimeout(() => {
        window.gameScene.showPlayingUI()
        window.gameScene.forceShowPlayingButtons()
        console.log('🎉 已跳转到出牌阶段！')
    }, 1000)
} else {
    console.error('❌ 游戏场景或状态不存在')
}
                `
                
                copyScriptAndShowInstructions(script, '跳转出牌阶段脚本')
            }
        }
        
        // 复制脚本并显示说明
        function copyScriptAndShowInstructions(script, scriptName) {
            navigator.clipboard.writeText(script).then(() => {
                showStatus('fixStatus', `✅ ${scriptName}已复制！请在游戏页面控制台执行`, 'success')
                log(`✅ ${scriptName}已复制到剪贴板`, 'success')
                
                setTimeout(() => {
                    alert(`${scriptName}已复制到剪贴板！

请按以下步骤操作：
1. 切换到游戏页面
2. 按F12打开浏览器控制台
3. 粘贴脚本并按回车执行

或者点击"返回游戏"按钮，然后在控制台中粘贴执行。`)
                }, 1000)
                
            }).catch(err => {
                showStatus('fixStatus', '❌ 复制失败，请手动复制脚本', 'error')
                log('❌ 复制失败，请手动复制脚本', 'error')
            })
        }
        
        // 其他功能
        function goToGame() {
            log('🎯 返回游戏页面', 'info')
            window.open('/hall', '_blank')
        }
        
        function openConsole() {
            log('🔍 提示打开控制台', 'info')
            alert('请按F12键打开浏览器开发者工具控制台')
        }
        
        function refreshGame() {
            log('🔄 刷新游戏页面', 'info')
            if (confirm('确定要刷新游戏页面吗？')) {
                window.open('/hall', '_blank')
            }
        }
        
        function clearLog() {
            document.getElementById('fixLog').textContent = '日志已清空\n'
        }
        
        function exportLog() {
            const logContent = document.getElementById('fixLog').textContent
            const blob = new Blob([logContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `ai-bidding-fix-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            log('📄 修复日志已导出', 'success')
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🤖 AI叫牌修复工具已加载', 'success')
            log('检测到AI叫牌卡住问题，点击"修复AI叫牌卡住"来解决', 'info')
        })
    </script>
</body>
</html>
