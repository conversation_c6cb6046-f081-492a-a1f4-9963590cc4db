<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 斗地主游戏 - 简化登录</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(0,0,0,0.8);
            padding: 40px;
            border-radius: 20px;
            border: 3px solid #FFD700;
            text-align: center;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .game-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }
        
        .game-title {
            font-size: 28px;
            font-weight: bold;
            margin: 20px 0 10px;
            color: #FFD700;
        }
        
        .game-subtitle {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 30px;
        }
        
        .login-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        button:hover {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        
        button.primary:hover {
            background: linear-gradient(45deg, #42A5F5, #64B5F6);
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        button.warning:hover {
            background: linear-gradient(45deg, #FFB74D, #FFCC02);
        }
        
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
        
        .game-links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #333;
        }
        
        .game-links h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .link-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .link-button {
            background: rgba(255,255,255,0.1);
            border: 1px solid #666;
            color: white;
            padding: 10px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            background: rgba(255,255,255,0.2);
            border-color: #FFD700;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="game-logo">🎴</div>
        <h1 class="game-title">斗地主竞技赛</h1>
        <p class="game-subtitle">线上智力竞技比赛选拔系统</p>
        
        <div id="status" class="status info">
            欢迎来到斗地主游戏！请选择进入方式
        </div>
        
        <div class="login-buttons">
            <button class="primary" onclick="enterGame()">🎮 进入游戏大厅</button>
            <button onclick="testGame()">🧪 测试游戏功能</button>
            <button class="warning" onclick="debugMode()">🔍 调试模式</button>
        </div>
        
        <div class="game-links">
            <h4>🎯 游戏测试页面</h4>
            <div class="link-grid">
                <a href="/test-game-ui.html" class="link-button" target="_blank">游戏UI测试</a>
                <a href="/card-playing-test.html" class="link-button" target="_blank">出牌功能测试</a>
                <a href="/mobile-compatibility-test.html" class="link-button" target="_blank">移动端测试</a>
                <a href="/ui-regression-test.html" class="link-button" target="_blank">回归测试</a>
            </div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status')
            statusDiv.className = `status ${type}`
            statusDiv.textContent = message
        }

        function enterGame() {
            showStatus('正在进入游戏大厅...', 'info')
            
            // 模拟登录过程
            setTimeout(() => {
                // 设置模拟用户数据
                const mockUser = {
                    id: 'test_user_' + Date.now(),
                    nickname: '测试玩家',
                    avatar: '/images/headimage/avatar_1.png'
                }
                
                // 保存到localStorage
                localStorage.setItem('gameStore', JSON.stringify({
                    user: mockUser,
                    gameState: 'idle'
                }))
                localStorage.setItem('token', 'mock_token_' + Date.now())
                
                showStatus('登录成功！正在跳转到游戏大厅...', 'success')
                
                setTimeout(() => {
                    // 跳转到主应用
                    window.location.href = '/'
                }, 1000)
            }, 1000)
        }

        function testGame() {
            showStatus('正在打开游戏测试页面...', 'info')
            
            setTimeout(() => {
                window.open('/card-playing-test.html', '_blank')
                showStatus('游戏测试页面已在新窗口打开', 'success')
            }, 500)
        }

        function debugMode() {
            showStatus('正在打开调试页面...', 'info')
            
            setTimeout(() => {
                window.open('/debug.html', '_blank')
                showStatus('调试页面已在新窗口打开', 'success')
            }, 500)
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简化登录页面已加载')
            
            // 检查是否已经登录
            const gameStore = localStorage.getItem('gameStore')
            const token = localStorage.getItem('token')
            
            if (gameStore && token) {
                try {
                    const data = JSON.parse(gameStore)
                    if (data.user) {
                        showStatus(`欢迎回来，${data.user.nickname}！`, 'success')
                    }
                } catch (error) {
                    console.error('解析存储数据失败:', error)
                }
            }
        })
    </script>
</body>
</html>
