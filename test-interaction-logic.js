// 出牌界面交互逻辑测试（不依赖Phaser）
console.log('🎮 开始出牌界面交互逻辑测试')

// 测试核心逻辑功能
class InteractionLogicTester {
  constructor() {
    this.testResults = []
    
    // 模拟卡牌数据
    this.mockCards = this.createMockCards()
    
    console.log('✅ 交互逻辑测试器初始化完成')
  }
  
  createMockCards() {
    const cards = []
    
    // 创建一些测试卡牌组合
    // 对子: 两张8
    cards.push(
      { id: 'h8', suit: 'hearts', rank: '8', value: 8, getDisplayName: () => '红桃8' },
      { id: 's8', suit: 'spades', rank: '8', value: 8, getDisplayName: () => '黑桃8' }
    )
    
    // 三张: 三张9
    cards.push(
      { id: 'h9', suit: 'hearts', rank: '9', value: 9, getDisplayName: () => '红桃9' },
      { id: 's9', suit: 'spades', rank: '9', value: 9, getDisplayName: () => '黑桃9' },
      { id: 'c9', suit: 'clubs', rank: '9', value: 9, getDisplayName: () => '梅花9' }
    )
    
    // 顺子: 3-7
    cards.push(
      { id: 'h3', suit: 'hearts', rank: '3', value: 3, getDisplayName: () => '红桃3' },
      { id: 's4', suit: 'spades', rank: '4', value: 4, getDisplayName: () => '黑桃4' },
      { id: 'c5', suit: 'clubs', rank: '5', value: 5, getDisplayName: () => '梅花5' },
      { id: 'd6', suit: 'diamonds', rank: '6', value: 6, getDisplayName: () => '方块6' },
      { id: 'h7', suit: 'hearts', rank: '7', value: 7, getDisplayName: () => '红桃7' }
    )
    
    // 炸弹: 四张10
    cards.push(
      { id: 'h10', suit: 'hearts', rank: '10', value: 10, getDisplayName: () => '红桃10' },
      { id: 's10', suit: 'spades', rank: '10', value: 10, getDisplayName: () => '黑桃10' },
      { id: 'c10', suit: 'clubs', rank: '10', value: 10, getDisplayName: () => '梅花10' },
      { id: 'd10', suit: 'diamonds', rank: '10', value: 10, getDisplayName: () => '方块10' }
    )
    
    // 单牌
    cards.push(
      { id: 'hJ', suit: 'hearts', rank: 'J', value: 11, getDisplayName: () => '红桃J' },
      { id: 'sQ', suit: 'spades', rank: 'Q', value: 12, getDisplayName: () => '黑桃Q' },
      { id: 'cK', suit: 'clubs', rank: 'K', value: 13, getDisplayName: () => '梅花K' }
    )
    
    console.log('🎴 创建了', cards.length, '张测试卡牌')
    return cards
  }
  
  // 测试按点数分组功能
  testGroupCardsByRank() {
    console.log('\n🧪 测试按点数分组功能')
    
    try {
      const groups = this.groupCardsByRank(this.mockCards)
      
      // 验证分组结果
      const expectedGroups = ['8', '9', '10']
      let success = true
      
      expectedGroups.forEach(rank => {
        if (!groups[rank]) {
          console.log(`❌ 缺少${rank}的分组`)
          success = false
        } else {
          console.log(`✅ ${rank}: ${groups[rank].length}张`)
        }
      })
      
      if (success) {
        console.log('✅ 按点数分组测试通过')
        this.testResults.push({ test: '按点数分组', result: 'PASS' })
      } else {
        console.log('❌ 按点数分组测试失败')
        this.testResults.push({ test: '按点数分组', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 按点数分组测试异常:', error.message)
      this.testResults.push({ test: '按点数分组', result: 'FAIL' })
    }
  }
  
  // 测试查找对子功能
  testFindPairs() {
    console.log('\n🧪 测试查找对子功能')
    
    try {
      const pairs = this.findPairs(this.mockCards)
      
      if (pairs.length > 0) {
        console.log(`✅ 找到 ${pairs.length} 个对子`)
        pairs.forEach((pair, index) => {
          console.log(`   对子${index + 1}: ${pair.map(c => c.getDisplayName()).join(', ')}`)
        })
        this.testResults.push({ test: '查找对子', result: 'PASS' })
      } else {
        console.log('❌ 未找到对子')
        this.testResults.push({ test: '查找对子', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 查找对子测试异常:', error.message)
      this.testResults.push({ test: '查找对子', result: 'FAIL' })
    }
  }
  
  // 测试查找三张功能
  testFindTriples() {
    console.log('\n🧪 测试查找三张功能')
    
    try {
      const triples = this.findTriples(this.mockCards)
      
      if (triples.length > 0) {
        console.log(`✅ 找到 ${triples.length} 个三张`)
        triples.forEach((triple, index) => {
          console.log(`   三张${index + 1}: ${triple.map(c => c.getDisplayName()).join(', ')}`)
        })
        this.testResults.push({ test: '查找三张', result: 'PASS' })
      } else {
        console.log('❌ 未找到三张')
        this.testResults.push({ test: '查找三张', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 查找三张测试异常:', error.message)
      this.testResults.push({ test: '查找三张', result: 'FAIL' })
    }
  }
  
  // 测试查找顺子功能
  testFindStraights() {
    console.log('\n🧪 测试查找顺子功能')
    
    try {
      const straights = this.findStraights(this.mockCards)
      
      if (straights.length > 0) {
        console.log(`✅ 找到 ${straights.length} 个顺子`)
        straights.forEach((straight, index) => {
          console.log(`   顺子${index + 1}: ${straight.map(c => c.getDisplayName()).join(', ')}`)
        })
        this.testResults.push({ test: '查找顺子', result: 'PASS' })
      } else {
        console.log('❌ 未找到顺子')
        this.testResults.push({ test: '查找顺子', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 查找顺子测试异常:', error.message)
      this.testResults.push({ test: '查找顺子', result: 'FAIL' })
    }
  }
  
  // 测试查找炸弹功能
  testFindBombs() {
    console.log('\n🧪 测试查找炸弹功能')
    
    try {
      const bombs = this.findBombs(this.mockCards)
      
      if (bombs.length > 0) {
        console.log(`✅ 找到 ${bombs.length} 个炸弹`)
        bombs.forEach((bomb, index) => {
          console.log(`   炸弹${index + 1}: ${bomb.map(c => c.getDisplayName()).join(', ')}`)
        })
        this.testResults.push({ test: '查找炸弹', result: 'PASS' })
      } else {
        console.log('❌ 未找到炸弹')
        this.testResults.push({ test: '查找炸弹', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 查找炸弹测试异常:', error.message)
      this.testResults.push({ test: '查找炸弹', result: 'FAIL' })
    }
  }
  
  // 测试事件系统
  testEventSystem() {
    console.log('\n🧪 测试事件系统')
    
    try {
      const eventListeners = new Map()
      let eventReceived = false
      
      // 模拟addEventListener
      const addEventListener = (event, callback) => {
        if (!eventListeners.has(event)) {
          eventListeners.set(event, [])
        }
        eventListeners.get(event).push(callback)
      }
      
      // 模拟dispatchEvent
      const dispatchEvent = (event, data) => {
        if (eventListeners.has(event)) {
          eventListeners.get(event).forEach(callback => callback(data))
        }
      }
      
      // 测试事件监听和触发
      addEventListener('testEvent', (data) => {
        eventReceived = true
        console.log('✅ 事件接收成功:', data.message)
      })
      
      dispatchEvent('testEvent', { message: '测试数据' })
      
      if (eventReceived) {
        console.log('✅ 事件系统测试通过')
        this.testResults.push({ test: '事件系统', result: 'PASS' })
      } else {
        console.log('❌ 事件系统测试失败')
        this.testResults.push({ test: '事件系统', result: 'FAIL' })
      }
      
    } catch (error) {
      console.log('❌ 事件系统测试异常:', error.message)
      this.testResults.push({ test: '事件系统', result: 'FAIL' })
    }
  }
  
  // 实现核心逻辑方法
  groupCardsByRank(cards) {
    const groups = {}
    cards.forEach(card => {
      const rank = card.rank
      if (!groups[rank]) {
        groups[rank] = []
      }
      groups[rank].push(card)
    })
    return groups
  }
  
  findPairs(cards) {
    const groups = this.groupCardsByRank(cards)
    const pairs = []
    
    Object.values(groups).forEach(group => {
      if (group.length >= 2) {
        pairs.push(group.slice(0, 2))
      }
    })
    
    return pairs.sort((a, b) => a[0].value - b[0].value)
  }
  
  findTriples(cards) {
    const groups = this.groupCardsByRank(cards)
    const triples = []
    
    Object.values(groups).forEach(group => {
      if (group.length >= 3) {
        triples.push(group.slice(0, 3))
      }
    })
    
    return triples.sort((a, b) => a[0].value - b[0].value)
  }
  
  findStraights(cards) {
    const straights = []
    const sortedCards = [...cards].sort((a, b) => a.value - b.value)
    
    for (let i = 0; i <= sortedCards.length - 5; i++) {
      const straight = []
      for (let j = i; j < sortedCards.length && straight.length < 12; j++) {
        if (straight.length === 0 || sortedCards[j].value === straight[straight.length - 1].value + 1) {
          straight.push(sortedCards[j])
        } else {
          break
        }
      }
      
      if (straight.length >= 5) {
        straights.push(straight)
      }
    }
    
    return straights
  }
  
  findBombs(cards) {
    const groups = this.groupCardsByRank(cards)
    const bombs = []
    
    Object.values(groups).forEach(group => {
      if (group.length === 4) {
        bombs.push(group)
      }
    })
    
    return bombs.sort((a, b) => a[0].value - b[0].value)
  }
  
  // 运行所有测试
  runAllTests() {
    console.log('🎯 开始运行所有交互逻辑测试')
    
    this.testGroupCardsByRank()
    this.testFindPairs()
    this.testFindTriples()
    this.testFindStraights()
    this.testFindBombs()
    this.testEventSystem()
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50))
    console.log('🎯 交互逻辑测试结果汇总')
    console.log('='.repeat(50))
    
    const passCount = this.testResults.filter(r => r.result === 'PASS').length
    const totalCount = this.testResults.length
    
    this.testResults.forEach((result, index) => {
      const status = result.result === 'PASS' ? '✅' : '❌'
      console.log(`${index + 1}. ${result.test}: ${status} ${result.result}`)
    })
    
    const passRate = Math.round(passCount / totalCount * 100)
    console.log(`\n📊 测试通过率: ${passCount}/${totalCount} (${passRate}%)`)
    
    if (passRate === 100) {
      console.log('🎉 所有交互逻辑测试通过！')
    } else if (passRate >= 80) {
      console.log('⚠️ 大部分测试通过，但仍有问题需要解决')
    } else {
      console.log('❌ 测试失败较多，需要重点检查问题')
    }
    
    console.log('\n✅ 出牌界面交互逻辑测试完成')
  }
}

// 运行测试
const tester = new InteractionLogicTester()
tester.runAllTests()
