// 牌型识别优化测试
import { CardPattern } from './src/game/CardPattern.js'
import { Card } from './src/game/Card.js'

console.log('🎮 开始牌型识别优化测试')

// 创建测试用的卡牌
function createCard(suit, rank, value) {
  return new Card(suit, rank, value)
}

// 创建测试卡牌集合
const testCards = {
  // 单牌
  single: [createCard('hearts', '7', 7)],
  
  // 对子
  pair: [
    createCard('hearts', '8', 8),
    createCard('spades', '8', 8)
  ],
  
  // 三张
  triple: [
    createCard('hearts', '9', 9),
    createCard('spades', '9', 9),
    createCard('clubs', '9', 9)
  ],
  
  // 三带一
  tripleWithSingle: [
    createCard('hearts', '10', 10),
    createCard('spades', '10', 10),
    createCard('clubs', '10', 10),
    createCard('diamonds', '5', 5)
  ],
  
  // 三带二
  tripleWithPair: [
    createCard('hearts', 'J', 11),
    createCard('spades', 'J', 11),
    createCard('clubs', 'J', 11),
    createCard('diamonds', '6', 6),
    createCard('hearts', '6', 6)
  ],
  
  // 顺子
  straight: [
    createCard('hearts', '3', 3),
    createCard('spades', '4', 4),
    createCard('clubs', '5', 5),
    createCard('diamonds', '6', 6),
    createCard('hearts', '7', 7)
  ],
  
  // 连对
  pairStraight: [
    createCard('hearts', '3', 3),
    createCard('spades', '3', 3),
    createCard('clubs', '4', 4),
    createCard('diamonds', '4', 4),
    createCard('hearts', '5', 5),
    createCard('spades', '5', 5)
  ],
  
  // 炸弹
  bomb: [
    createCard('hearts', 'K', 13),
    createCard('spades', 'K', 13),
    createCard('clubs', 'K', 13),
    createCard('diamonds', 'K', 13)
  ],
  
  // 火箭
  rocket: [
    createCard('joker', 'small_joker', 16),
    createCard('joker', 'big_joker', 17)
  ],
  
  // 飞机不带牌
  tripleStraightPure: [
    createCard('hearts', '7', 7),
    createCard('spades', '7', 7),
    createCard('clubs', '7', 7),
    createCard('diamonds', '8', 8),
    createCard('hearts', '8', 8),
    createCard('spades', '8', 8)
  ],
  
  // 无效牌型
  invalid: [
    createCard('hearts', '3', 3),
    createCard('spades', '5', 5),
    createCard('clubs', '7', 7)
  ]
}

// 性能测试
async function performanceTest() {
  console.log('\n🚀 性能测试开始')
  
  const cardPattern = new CardPattern()
  const iterations = 10000
  
  // 测试不同牌型的识别性能
  const testCases = [
    { name: '单牌', cards: testCards.single },
    { name: '对子', cards: testCards.pair },
    { name: '三张', cards: testCards.triple },
    { name: '顺子', cards: testCards.straight },
    { name: '炸弹', cards: testCards.bomb },
    { name: '火箭', cards: testCards.rocket }
  ]
  
  for (const testCase of testCases) {
    const startTime = performance.now()
    
    for (let i = 0; i < iterations; i++) {
      cardPattern.identifyPattern(testCase.cards)
    }
    
    const endTime = performance.now()
    const avgTime = (endTime - startTime) / iterations
    
    console.log(`📊 ${testCase.name}: 平均 ${avgTime.toFixed(4)}ms/次`)
  }
  
  // 测试缓存效果
  console.log('\n🎯 缓存效果测试')
  
  const cacheTestCards = testCards.straight
  
  // 第一次识别（无缓存）
  const startTime1 = performance.now()
  for (let i = 0; i < iterations; i++) {
    cardPattern.identifyPattern(cacheTestCards)
  }
  const endTime1 = performance.now()
  
  // 清空缓存重新测试
  cardPattern.patternCache.clear()
  
  const startTime2 = performance.now()
  for (let i = 0; i < iterations; i++) {
    cardPattern.identifyPattern(cacheTestCards)
  }
  const endTime2 = performance.now()
  
  const withCacheTime = (endTime1 - startTime1) / iterations
  const withoutCacheTime = (endTime2 - startTime2) / iterations
  const improvement = ((withoutCacheTime - withCacheTime) / withoutCacheTime * 100).toFixed(1)
  
  console.log(`📈 有缓存: ${withCacheTime.toFixed(4)}ms/次`)
  console.log(`📉 无缓存: ${withoutCacheTime.toFixed(4)}ms/次`)
  console.log(`🎯 性能提升: ${improvement}%`)
}

// 功能测试
function functionalTest() {
  console.log('\n🧪 功能测试开始')
  
  const cardPattern = new CardPattern()
  let passCount = 0
  let totalCount = 0
  
  // 测试所有牌型
  Object.entries(testCards).forEach(([name, cards]) => {
    totalCount++
    
    console.log(`\n🎴 测试 ${name}:`)
    console.log(`📋 输入: ${cards.map(c => c.getDisplayName()).join(' ')}`)
    
    const result = cardPattern.identifyPattern(cards)
    
    if (result) {
      console.log(`✅ 识别结果: ${result.type}`)
      console.log(`⚖️ 权重: ${result.weight}`)
      if (result.subtype) {
        console.log(`🏷️ 子类型: ${result.subtype}`)
      }
      
      // 验证预期结果
      const expectedTypes = {
        single: 'single',
        pair: 'pair',
        triple: 'triple',
        tripleWithSingle: 'triple_with_single',
        tripleWithPair: 'triple_with_pair',
        straight: 'straight',
        pairStraight: 'pair_straight',
        bomb: 'bomb',
        rocket: 'rocket',
        tripleStraightPure: 'triple_straight'
      }
      
      if (expectedTypes[name] && result.type === expectedTypes[name]) {
        console.log(`🎯 识别正确`)
        passCount++
      } else if (name === 'invalid') {
        console.log(`❌ 应该识别失败但成功了`)
      } else {
        console.log(`❌ 识别错误，期望: ${expectedTypes[name]}`)
      }
    } else {
      if (name === 'invalid') {
        console.log(`✅ 正确识别为无效牌型`)
        passCount++
      } else {
        console.log(`❌ 识别失败`)
      }
    }
  })
  
  console.log(`\n📊 功能测试结果: ${passCount}/${totalCount} (${Math.round(passCount/totalCount*100)}%)`)
  return passCount === totalCount
}

// 边界测试
function boundaryTest() {
  console.log('\n🔬 边界测试开始')
  
  const cardPattern = new CardPattern()
  let passCount = 0
  let totalCount = 0
  
  const boundaryTests = [
    {
      name: '空数组',
      cards: [],
      expected: null
    },
    {
      name: '最长顺子',
      cards: [
        createCard('hearts', '3', 3),
        createCard('spades', '4', 4),
        createCard('clubs', '5', 5),
        createCard('diamonds', '6', 6),
        createCard('hearts', '7', 7),
        createCard('spades', '8', 8),
        createCard('clubs', '9', 9),
        createCard('diamonds', '10', 10),
        createCard('hearts', 'J', 11),
        createCard('spades', 'Q', 12),
        createCard('clubs', 'K', 13),
        createCard('diamonds', 'A', 14)
      ],
      expected: 'straight'
    },
    {
      name: '包含2的无效顺子',
      cards: [
        createCard('hearts', 'A', 14),
        createCard('spades', '2', 15),
        createCard('clubs', '3', 3),
        createCard('diamonds', '4', 4),
        createCard('hearts', '5', 5)
      ],
      expected: null
    }
  ]
  
  boundaryTests.forEach(test => {
    totalCount++
    console.log(`\n🧪 ${test.name}:`)
    
    const result = cardPattern.identifyPattern(test.cards)
    const success = (result?.type === test.expected) || (result === null && test.expected === null)
    
    if (success) {
      console.log(`✅ 测试通过`)
      passCount++
    } else {
      console.log(`❌ 测试失败，期望: ${test.expected}, 实际: ${result?.type || null}`)
    }
  })
  
  console.log(`\n📊 边界测试结果: ${passCount}/${totalCount} (${Math.round(passCount/totalCount*100)}%)`)
  return passCount === totalCount
}

// 运行所有测试
async function runAllTests() {
  console.log('🎯 牌型识别优化测试套件')
  console.log('='.repeat(50))
  
  const functionalPass = functionalTest()
  const boundaryPass = boundaryTest()
  await performanceTest()
  
  console.log('\n' + '='.repeat(50))
  console.log('🎊 测试总结')
  console.log('='.repeat(50))
  
  if (functionalPass && boundaryPass) {
    console.log('✅ 所有测试通过！牌型识别优化成功！')
  } else {
    console.log('❌ 部分测试失败，需要进一步优化')
  }
  
  console.log('\n🎉 牌型识别优化测试完成')
}

// 运行测试
runAllTests().catch(error => {
  console.error('❌ 测试执行失败:', error)
})
