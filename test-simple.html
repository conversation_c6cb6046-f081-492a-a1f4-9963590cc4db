<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
    <!-- 加载Phaser库 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔧 简单功能测试</h1>
        <p>测试基本的模块导入和方法存在性</p>
        
        <div class="test-section">
            <h3>测试1: 模块导入测试</h3>
            <button onclick="testModuleImports()">测试模块导入</button>
            <div id="moduleResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: BiddingManager功能测试</h3>
            <button onclick="testBiddingManager()">测试BiddingManager</button>
            <div id="biddingResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: GameMainScene方法测试</h3>
            <button onclick="testGameMainScene()">测试GameMainScene</button>
            <div id="gameSceneResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.textContent = `${success ? '✅ 通过' : '❌ 失败'}: ${message}`
        }

        // 测试1: 模块导入
        window.testModuleImports = async function() {
            log('🧪 开始测试模块导入')
            let allPassed = true
            let results = []

            // 测试GameScene导入
            try {
                const gameModule = await import('./src/game/GameScene.js')
                if (gameModule.GameMainScene && gameModule.GameScene) {
                    log('✅ GameScene模块导入成功')
                    results.push('GameScene: ✅')
                } else {
                    log('❌ GameScene模块导入失败 - 缺少类')
                    results.push('GameScene: ❌')
                    allPassed = false
                }
            } catch (error) {
                log(`❌ GameScene模块导入失败: ${error.message}`)
                results.push('GameScene: ❌')
                allPassed = false
            }

            // 测试BiddingManager导入
            try {
                const biddingModule = await import('./src/game/BiddingManager.js')
                if (biddingModule.BiddingManager) {
                    log('✅ BiddingManager模块导入成功')
                    results.push('BiddingManager: ✅')
                } else {
                    log('❌ BiddingManager模块导入失败 - 缺少类')
                    results.push('BiddingManager: ❌')
                    allPassed = false
                }
            } catch (error) {
                log(`❌ BiddingManager模块导入失败: ${error.message}`)
                results.push('BiddingManager: ❌')
                allPassed = false
            }

            // 测试AudioManager导入
            try {
                const audioModule = await import('./src/game/AudioManager.js')
                if (audioModule.AudioManager) {
                    log('✅ AudioManager模块导入成功')
                    results.push('AudioManager: ✅')
                } else {
                    log('❌ AudioManager模块导入失败 - 缺少类')
                    results.push('AudioManager: ❌')
                    allPassed = false
                }
            } catch (error) {
                log(`❌ AudioManager模块导入失败: ${error.message}`)
                results.push('AudioManager: ❌')
                allPassed = false
            }

            showResult('moduleResult', allPassed, `模块导入测试 - ${results.join(', ')}`)
        }

        // 测试2: BiddingManager功能
        window.testBiddingManager = async function() {
            log('🧪 开始测试BiddingManager功能')
            
            try {
                const { BiddingManager } = await import('./src/game/BiddingManager.js')
                const manager = new BiddingManager()
                
                // 测试基本方法存在
                const methods = ['initGame', 'makeBid', 'getCurrentState', 'processAIBidding', 'determineLandlord']
                let missingMethods = []
                
                methods.forEach(method => {
                    if (typeof manager[method] === 'function') {
                        log(`✅ 方法存在: ${method}`)
                    } else {
                        log(`❌ 方法缺失: ${method}`)
                        missingMethods.push(method)
                    }
                })
                
                // 测试基本功能
                const players = {
                    west: { name: '西家', cards: [] },
                    south: { name: '南家', cards: [] },
                    east: { name: '东家', cards: [] }
                }
                
                const firstBidder = manager.initGame(players, 1, 1, 'south')
                log(`✅ 游戏初始化成功，首叫者: ${firstBidder}`)
                
                const state = manager.getCurrentState()
                log(`✅ 状态获取成功，阶段: ${state.gameState.phase}`)
                
                if (missingMethods.length === 0) {
                    showResult('biddingResult', true, 'BiddingManager功能完整')
                } else {
                    showResult('biddingResult', false, `缺少方法: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ BiddingManager测试失败: ${error.message}`)
                showResult('biddingResult', false, `测试异常: ${error.message}`)
            }
        }

        // 测试3: GameMainScene方法
        window.testGameMainScene = async function() {
            log('🧪 开始测试GameMainScene方法')
            
            try {
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 测试方法存在
                const methods = [
                    'preload', 'create', 'startNewGame',
                    'showBiddingUIWithManager', 'updateButtonDisplay',
                    'displayPlayerCards', 'startBiddingWithManager'
                ]
                
                let missingMethods = []
                
                methods.forEach(method => {
                    if (typeof GameMainScene.prototype[method] === 'function') {
                        log(`✅ 方法存在: ${method}`)
                    } else {
                        log(`❌ 方法缺失: ${method}`)
                        missingMethods.push(method)
                    }
                })
                
                if (missingMethods.length === 0) {
                    showResult('gameSceneResult', true, 'GameMainScene方法完整')
                } else {
                    showResult('gameSceneResult', false, `缺少方法: ${missingMethods.join(', ')}`)
                }
                
            } catch (error) {
                log(`❌ GameMainScene测试失败: ${error.message}`)
                showResult('gameSceneResult', false, `测试异常: ${error.message}`)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 简单功能测试页面已加载')
        })
    </script>
</body>
</html>
