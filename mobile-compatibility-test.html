<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <title>📱 移动端兼容性测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }
        
        body {
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #FFD700;
        }
        
        .header h1 {
            font-size: clamp(18px, 5vw, 24px);
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: clamp(12px, 3vw, 16px);
            opacity: 0.9;
        }
        
        .device-info {
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #4CAF50;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .info-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-label {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 1px solid #333;
        }
        
        .test-section h3 {
            font-size: clamp(16px, 4vw, 20px);
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: clamp(12px, 3vw, 14px);
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            min-height: 44px; /* iOS推荐的最小触摸目标 */
            min-width: 44px;
        }
        
        button:active {
            transform: scale(0.95);
            background: linear-gradient(45deg, #66BB6A, #81C784);
        }
        
        button.primary {
            background: linear-gradient(45deg, #2196F3, #42A5F5);
        }
        
        button.warning {
            background: linear-gradient(45deg, #FF9800, #FFB74D);
        }
        
        button.danger {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .pass {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .fail {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .warn {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            color: #FF9800;
        }
        
        #gameContainer {
            width: 100%;
            max-width: 100vw;
            height: 50vh;
            min-height: 300px;
            border: 2px solid #FFD700;
            margin: 15px 0;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .touch-test-area {
            background: rgba(255,255,255,0.1);
            border: 2px dashed #FFD700;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 15px 0;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .touch-test-area.active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }
        
        .log {
            background: rgba(0,0,0,0.9);
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 11px;
            border: 1px solid #333;
        }
        
        .orientation-warning {
            background: rgba(255, 152, 0, 0.9);
            color: #000;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            display: none;
        }
        
        @media (orientation: portrait) and (max-width: 768px) {
            .orientation-warning {
                display: block;
            }
        }
        
        .performance-meter {
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .meter-bar {
            width: 100%;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .meter-fill {
            height: 100%;
            background: linear-gradient(90deg, #F44336, #FF9800, #4CAF50);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        /* 移动端特殊样式 */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
            }
            
            .header {
                padding: 10px;
                margin-bottom: 15px;
            }
            
            .test-section {
                padding: 12px;
                margin-bottom: 12px;
            }
            
            .controls {
                gap: 8px;
            }
            
            button {
                padding: 10px 14px;
                font-size: 12px;
            }
            
            #gameContainer {
                height: 40vh;
                min-height: 250px;
            }
        }
        
        /* 超小屏幕适配 */
        @media (max-width: 480px) {
            .info-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            button {
                width: 100%;
                margin: 2px 0;
            }
            
            #gameContainer {
                height: 35vh;
                min-height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="orientation-warning">
            📱 建议横屏使用以获得最佳游戏体验
        </div>
        
        <div class="header">
            <h1>📱 移动端兼容性测试</h1>
            <p>全面测试移动设备上的游戏体验</p>
        </div>

        <div class="device-info">
            <h3>📊 设备信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">设备类型</div>
                    <div class="info-value" id="deviceType">检测中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">屏幕尺寸</div>
                    <div class="info-value" id="screenSize">检测中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">像素密度</div>
                    <div class="info-value" id="pixelRatio">检测中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">方向</div>
                    <div class="info-value" id="orientation">检测中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">浏览器</div>
                    <div class="info-value" id="browser">检测中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">触摸支持</div>
                    <div class="info-value" id="touchSupport">检测中...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏初始化测试</h3>
            <div class="controls">
                <button class="primary" onclick="startMobileGameTest()">启动移动端游戏</button>
                <button onclick="testGameScaling()">测试游戏缩放</button>
                <button class="danger" onclick="destroyGame()">销毁游戏</button>
            </div>
            <div id="gameResults"></div>
            <div id="gameContainer"></div>
        </div>

        <div class="test-section">
            <h3>👆 触摸交互测试</h3>
            <div class="controls">
                <button onclick="testTouchEvents()">测试触摸事件</button>
                <button onclick="testUIInteraction()">测试UI交互</button>
                <button onclick="testGestureSupport()">测试手势支持</button>
            </div>
            <div class="touch-test-area" id="touchTestArea">
                <div>👆 点击这里测试触摸响应</div>
                <div style="font-size: 12px; margin-top: 10px; opacity: 0.7;">支持单击、双击、长按</div>
            </div>
            <div id="touchResults"></div>
        </div>

        <div class="test-section">
            <h3>📐 响应式布局测试</h3>
            <div class="controls">
                <button onclick="testResponsiveLayout()">测试响应式布局</button>
                <button onclick="testOrientationChange()">测试屏幕旋转</button>
                <button onclick="testViewportAdaptation()">测试视口适配</button>
            </div>
            <div id="layoutResults"></div>
        </div>

        <div class="test-section">
            <h3>⚡ 性能测试</h3>
            <div class="controls">
                <button onclick="testPerformance()">开始性能测试</button>
                <button onclick="testMemoryUsage()">测试内存使用</button>
                <button onclick="testFrameRate()">测试帧率</button>
            </div>
            <div class="performance-meter">
                <div>帧率 (FPS)</div>
                <div class="meter-bar">
                    <div class="meter-fill" id="fpsMeter"></div>
                </div>
                <div id="fpsValue">0 FPS</div>
            </div>
            <div id="performanceResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 游戏功能测试</h3>
            <div class="controls">
                <button onclick="testBiddingOnMobile()">测试移动端叫牌</button>
                <button onclick="testPlayingOnMobile()">测试移动端出牌</button>
                <button onclick="testFullGameFlow()">测试完整流程</button>
            </div>
            <div id="gameplayResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="controls">
                <button onclick="clearLog()">清空日志</button>
                <button onclick="exportMobileTestReport()">导出测试报告</button>
            </div>
            <div id="testLog" class="log">移动端兼容性测试已准备就绪...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null
        let performanceMonitor = null
        let touchTestData = {
            touches: 0,
            gestures: 0,
            responseTime: []
        }

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'touch': '👆'
            }
            const logEntry = `[${timestamp}] ${typeIcon[type]} ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(containerId, testName, success, message, details = '') {
            const container = document.getElementById(containerId)
            const resultDiv = document.createElement('div')
            resultDiv.className = `test-result ${success ? 'pass' : 'fail'}`
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${testName}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `
            container.appendChild(resultDiv)
        }

        // 检测设备信息
        function detectDeviceInfo() {
            // 设备类型检测
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            const isTablet = /iPad|Android(?=.*Mobile)/i.test(navigator.userAgent)
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
            const isAndroid = /Android/.test(navigator.userAgent)
            
            let deviceType = 'Desktop'
            if (isTablet) deviceType = 'Tablet'
            else if (isMobile) deviceType = 'Mobile'
            if (isIOS) deviceType += ' (iOS)'
            else if (isAndroid) deviceType += ' (Android)'
            
            document.getElementById('deviceType').textContent = deviceType
            
            // 屏幕信息
            const screenSize = `${window.screen.width}×${window.screen.height}`
            document.getElementById('screenSize').textContent = screenSize
            
            // 像素密度
            const pixelRatio = window.devicePixelRatio || 1
            document.getElementById('pixelRatio').textContent = `${pixelRatio}x`
            
            // 屏幕方向
            const orientation = window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape'
            document.getElementById('orientation').textContent = orientation
            
            // 浏览器检测
            let browser = 'Unknown'
            if (navigator.userAgent.includes('Chrome')) browser = 'Chrome'
            else if (navigator.userAgent.includes('Safari')) browser = 'Safari'
            else if (navigator.userAgent.includes('Firefox')) browser = 'Firefox'
            else if (navigator.userAgent.includes('Edge')) browser = 'Edge'
            document.getElementById('browser').textContent = browser
            
            // 触摸支持
            const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
            document.getElementById('touchSupport').textContent = touchSupport ? 'Yes' : 'No'
            
            log(`📱 设备检测完成: ${deviceType}, ${screenSize}, ${orientation}`, 'info')
            
            return {
                isMobile,
                isTablet,
                isIOS,
                isAndroid,
                touchSupport,
                screenSize,
                pixelRatio,
                orientation,
                browser
            }
        }

        // 启动移动端游戏测试
        window.startMobileGameTest = async function() {
            log('🎮 启动移动端游戏测试', 'info')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                }

                // 导入游戏场景
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 移动端优化的游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: window.innerWidth > 768 ? 800 : window.innerWidth - 40,
                    height: window.innerHeight > 600 ? 400 : window.innerHeight * 0.4,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene,
                    scale: {
                        mode: Phaser.Scale.FIT,
                        autoCenter: Phaser.Scale.CENTER_BOTH,
                        width: window.innerWidth > 768 ? 800 : window.innerWidth - 40,
                        height: window.innerHeight > 600 ? 400 : window.innerHeight * 0.4
                    },
                    input: {
                        touch: true,
                        mouse: true
                    },
                    render: {
                        antialias: false, // 移动端性能优化
                        pixelArt: false,
                        roundPixels: true
                    }
                }

                gameInstance = new Phaser.Game(config)
                
                // 等待场景加载
                setTimeout(() => {
                    gameScene = gameInstance.scene.getScene('GameMainScene')
                    if (gameScene) {
                        showResult('gameResults', '移动端游戏初始化', true, '游戏场景创建成功')
                        log('✅ 移动端游戏初始化成功', 'success')
                        
                        // 开始性能监控
                        startPerformanceMonitoring()
                    } else {
                        showResult('gameResults', '移动端游戏初始化', false, '游戏场景获取失败')
                        log('❌ 移动端游戏初始化失败', 'error')
                    }
                }, 2000)

            } catch (error) {
                showResult('gameResults', '移动端游戏初始化', false, '初始化失败', error.message)
                log(`❌ 移动端游戏初始化失败: ${error.message}`, 'error')
            }
        }

        // 设置触摸测试区域
        function setupTouchTestArea() {
            const touchArea = document.getElementById('touchTestArea')
            let touchStartTime = 0
            let touchCount = 0
            let lastTouchTime = 0

            // 触摸开始
            touchArea.addEventListener('touchstart', function(e) {
                e.preventDefault()
                touchStartTime = Date.now()
                touchArea.classList.add('active')
                log('👆 触摸开始', 'touch')
            })

            // 触摸结束
            touchArea.addEventListener('touchend', function(e) {
                e.preventDefault()
                const touchDuration = Date.now() - touchStartTime
                touchArea.classList.remove('active')
                touchCount++

                // 检测双击
                const timeSinceLastTouch = Date.now() - lastTouchTime
                if (timeSinceLastTouch < 300) {
                    log('👆 检测到双击', 'touch')
                    touchTestData.gestures++
                }

                // 检测长按
                if (touchDuration > 500) {
                    log('👆 检测到长按', 'touch')
                    touchTestData.gestures++
                }

                touchTestData.touches++
                touchTestData.responseTime.push(touchDuration)
                lastTouchTime = Date.now()

                log(`👆 触摸结束 (持续${touchDuration}ms)`, 'touch')
            })

            // 鼠标事件兼容
            touchArea.addEventListener('click', function(e) {
                if (!('ontouchstart' in window)) {
                    touchArea.classList.add('active')
                    setTimeout(() => touchArea.classList.remove('active'), 200)
                    log('🖱️ 鼠标点击 (触摸模拟)', 'touch')
                }
            })
        }

        // 测试触摸事件
        window.testTouchEvents = function() {
            log('👆 开始触摸事件测试', 'info')

            const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

            if (touchSupport) {
                showResult('touchResults', '触摸事件支持', true, '设备支持触摸事件')
                log('✅ 设备支持触摸事件', 'success')

                // 测试多点触控
                const maxTouchPoints = navigator.maxTouchPoints || 1
                showResult('touchResults', '多点触控', maxTouchPoints > 1, `支持${maxTouchPoints}个触摸点`)
                log(`📱 多点触控支持: ${maxTouchPoints}个触摸点`, 'info')
            } else {
                showResult('touchResults', '触摸事件支持', false, '设备不支持触摸事件')
                log('❌ 设备不支持触摸事件', 'error')
            }

            // 显示触摸统计
            if (touchTestData.touches > 0) {
                const avgResponseTime = touchTestData.responseTime.reduce((a, b) => a + b, 0) / touchTestData.responseTime.length
                showResult('touchResults', '触摸响应统计', true,
                    `触摸${touchTestData.touches}次，手势${touchTestData.gestures}次，平均响应${Math.round(avgResponseTime)}ms`)
            }
        }

        // 测试UI交互
        window.testUIInteraction = function() {
            log('🎯 开始UI交互测试', 'info')

            if (!gameScene) {
                showResult('touchResults', 'UI交互测试', false, '游戏场景未就绪')
                return
            }

            try {
                // 测试叫牌UI触摸
                if (typeof gameScene.forceShowBiddingUI === 'function') {
                    gameScene.forceShowBiddingUI()
                    log('✅ 叫牌UI已显示，请测试触摸交互', 'success')

                    setTimeout(() => {
                        // 测试出牌UI触摸
                        if (typeof gameScene.forceShowPlayingButtons === 'function') {
                            gameScene.forceShowPlayingButtons()
                            log('✅ 出牌UI已显示，请测试触摸交互', 'success')

                            showResult('touchResults', 'UI交互测试', true, '游戏UI已显示，请手动测试触摸交互')
                        }
                    }, 1000)
                } else {
                    showResult('touchResults', 'UI交互测试', false, '游戏UI方法不存在')
                }
            } catch (error) {
                showResult('touchResults', 'UI交互测试', false, '测试失败', error.message)
                log(`❌ UI交互测试失败: ${error.message}`, 'error')
            }
        }

        // 测试手势支持
        window.testGestureSupport = function() {
            log('✋ 开始手势支持测试', 'info')

            // 检测手势API支持
            const gestureSupport = {
                touch: 'ontouchstart' in window,
                pointer: 'onpointerdown' in window,
                gesture: 'ongesturestart' in window
            }

            Object.entries(gestureSupport).forEach(([key, supported]) => {
                showResult('touchResults', `${key.toUpperCase()}事件`, supported,
                    supported ? '支持' : '不支持')
                log(`${supported ? '✅' : '❌'} ${key.toUpperCase()}事件: ${supported ? '支持' : '不支持'}`,
                    supported ? 'success' : 'warning')
            })
        }

        // 测试响应式布局
        window.testResponsiveLayout = function() {
            log('📐 开始响应式布局测试', 'info')

            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                ratio: window.innerWidth / window.innerHeight
            }

            // 检查布局适配
            const gameContainer = document.getElementById('gameContainer')
            const containerRect = gameContainer.getBoundingClientRect()

            const layoutTests = [
                {
                    name: '容器宽度适配',
                    pass: containerRect.width <= viewport.width,
                    message: `容器宽度${Math.round(containerRect.width)}px，视口宽度${viewport.width}px`
                },
                {
                    name: '容器高度适配',
                    pass: containerRect.height <= viewport.height,
                    message: `容器高度${Math.round(containerRect.height)}px，视口高度${viewport.height}px`
                },
                {
                    name: '宽高比适配',
                    pass: viewport.ratio > 1 ? true : containerRect.height < viewport.height * 0.6,
                    message: `屏幕宽高比${viewport.ratio.toFixed(2)}`
                }
            ]

            layoutTests.forEach(test => {
                showResult('layoutResults', test.name, test.pass, test.message)
                log(`${test.pass ? '✅' : '❌'} ${test.name}: ${test.message}`, test.pass ? 'success' : 'warning')
            })
        }

        // 测试屏幕旋转
        window.testOrientationChange = function() {
            log('🔄 开始屏幕旋转测试', 'info')

            const currentOrientation = window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape'

            showResult('layoutResults', '当前屏幕方向', true, currentOrientation)
            log(`📱 当前屏幕方向: ${currentOrientation}`, 'info')

            // 监听方向变化
            let orientationChangeCount = 0
            const orientationHandler = function() {
                orientationChangeCount++
                const newOrientation = window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape'

                showResult('layoutResults', '屏幕旋转响应', true,
                    `检测到方向变化${orientationChangeCount}次，当前: ${newOrientation}`)
                log(`🔄 屏幕方向变化: ${newOrientation}`, 'info')

                // 重新测试布局
                setTimeout(() => {
                    testResponsiveLayout()
                    if (gameInstance && gameInstance.scale) {
                        gameInstance.scale.refresh()
                        log('🎮 游戏缩放已刷新', 'info')
                    }
                }, 500)
            }

            window.addEventListener('orientationchange', orientationHandler)
            window.addEventListener('resize', orientationHandler)

            log('🔄 屏幕旋转监听已启动，请旋转设备测试', 'info')
        }

        // 测试视口适配
        window.testViewportAdaptation = function() {
            log('📱 开始视口适配测试', 'info')

            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                availWidth: window.screen.availWidth,
                availHeight: window.screen.availHeight,
                pixelRatio: window.devicePixelRatio || 1
            }

            const viewportTests = [
                {
                    name: 'Viewport Meta标签',
                    pass: document.querySelector('meta[name="viewport"]') !== null,
                    message: 'viewport meta标签配置'
                },
                {
                    name: '像素密度适配',
                    pass: viewport.pixelRatio >= 1,
                    message: `像素密度${viewport.pixelRatio}x`
                },
                {
                    name: '可用屏幕空间',
                    pass: viewport.width <= viewport.availWidth && viewport.height <= viewport.availHeight,
                    message: `可用空间${viewport.availWidth}×${viewport.availHeight}`
                }
            ]

            viewportTests.forEach(test => {
                showResult('layoutResults', test.name, test.pass, test.message)
                log(`${test.pass ? '✅' : '❌'} ${test.name}: ${test.message}`, test.pass ? 'success' : 'warning')
            })
        }

        // 开始性能监控
        function startPerformanceMonitoring() {
            let frameCount = 0
            let lastTime = performance.now()
            let fps = 0

            function updateFPS() {
                const currentTime = performance.now()
                frameCount++

                if (currentTime - lastTime >= 1000) {
                    fps = Math.round(frameCount * 1000 / (currentTime - lastTime))
                    frameCount = 0
                    lastTime = currentTime

                    // 更新FPS显示
                    document.getElementById('fpsValue').textContent = `${fps} FPS`
                    const fpsPercent = Math.min(fps / 60 * 100, 100)
                    document.getElementById('fpsMeter').style.width = `${fpsPercent}%`
                }

                if (performanceMonitor) {
                    requestAnimationFrame(updateFPS)
                }
            }

            performanceMonitor = true
            requestAnimationFrame(updateFPS)
            log('⚡ 性能监控已启动', 'info')
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 移动端兼容性测试页面已加载', 'info')

            // 检测设备信息
            const deviceInfo = detectDeviceInfo()

            // 监听屏幕方向变化
            window.addEventListener('orientationchange', function() {
                setTimeout(() => {
                    const newOrientation = window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape'
                    document.getElementById('orientation').textContent = newOrientation
                    log(`📱 屏幕方向变化: ${newOrientation}`, 'info')
                }, 100)
            })

            // 设置触摸测试区域
            setupTouchTestArea()

            log('🚀 移动端兼容性测试已准备就绪', 'success')
        })

        // 测试游戏缩放
        window.testGameScaling = function() {
            log('🔍 开始游戏缩放测试', 'info')

            if (!gameInstance) {
                showResult('gameResults', '游戏缩放测试', false, '游戏未启动')
                return
            }

            try {
                const scale = gameInstance.scale
                const scaleInfo = {
                    mode: scale.scaleMode,
                    width: scale.gameSize.width,
                    height: scale.gameSize.height,
                    zoom: scale.zoom
                }

                showResult('gameResults', '游戏缩放配置', true,
                    `模式: ${scaleInfo.mode}, 尺寸: ${scaleInfo.width}×${scaleInfo.height}, 缩放: ${scaleInfo.zoom}`)
                log(`🔍 游戏缩放配置: ${JSON.stringify(scaleInfo)}`, 'info')

                // 测试缩放刷新
                scale.refresh()
                log('✅ 游戏缩放刷新完成', 'success')

            } catch (error) {
                showResult('gameResults', '游戏缩放测试', false, '测试失败', error.message)
                log(`❌ 游戏缩放测试失败: ${error.message}`, 'error')
            }
        }

        // 销毁游戏
        window.destroyGame = function() {
            log('🗑️ 销毁游戏实例', 'info')

            if (gameInstance) {
                gameInstance.destroy(true)
                gameInstance = null
                gameScene = null
                performanceMonitor = false
                log('✅ 游戏实例已销毁', 'success')
                showResult('gameResults', '游戏销毁', true, '游戏实例已成功销毁')
            } else {
                log('⚠️ 没有游戏实例需要销毁', 'warning')
            }
        }

        // 测试性能
        window.testPerformance = function() {
            log('⚡ 开始性能测试', 'info')

            const performanceTests = []

            // 测试内存使用
            if (performance.memory) {
                const memory = performance.memory
                const memoryMB = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
                }

                performanceTests.push({
                    name: '内存使用',
                    pass: memoryMB.used < memoryMB.limit * 0.8,
                    message: `使用${memoryMB.used}MB / 限制${memoryMB.limit}MB`
                })
            }

            // 测试渲染性能
            const renderStart = performance.now()
            requestAnimationFrame(() => {
                const renderTime = performance.now() - renderStart
                performanceTests.push({
                    name: '渲染延迟',
                    pass: renderTime < 16.67, // 60fps标准
                    message: `${renderTime.toFixed(2)}ms`
                })

                // 显示测试结果
                performanceTests.forEach(test => {
                    showResult('performanceResults', test.name, test.pass, test.message)
                    log(`${test.pass ? '✅' : '⚠️'} ${test.name}: ${test.message}`, test.pass ? 'success' : 'warning')
                })
            })
        }

        // 测试内存使用
        window.testMemoryUsage = function() {
            log('💾 开始内存使用测试', 'info')

            if (performance.memory) {
                const memory = performance.memory
                const memoryInfo = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
                }

                const memoryUsagePercent = (memoryInfo.used / memoryInfo.limit) * 100

                showResult('performanceResults', '内存使用情况', memoryUsagePercent < 80,
                    `使用${memoryInfo.used}MB，占用${memoryUsagePercent.toFixed(1)}%`)
                log(`💾 内存使用: ${memoryInfo.used}MB / ${memoryInfo.limit}MB (${memoryUsagePercent.toFixed(1)}%)`, 'info')

                // 内存压力测试
                if (memoryUsagePercent > 80) {
                    log('⚠️ 内存使用率较高，建议优化', 'warning')
                }
            } else {
                showResult('performanceResults', '内存使用情况', false, '浏览器不支持内存API')
                log('❌ 浏览器不支持内存API', 'error')
            }
        }

        // 测试帧率
        window.testFrameRate = function() {
            log('🎬 开始帧率测试', 'info')

            let frameCount = 0
            let startTime = performance.now()
            let testDuration = 3000 // 3秒测试

            function countFrames() {
                frameCount++
                const elapsed = performance.now() - startTime

                if (elapsed < testDuration) {
                    requestAnimationFrame(countFrames)
                } else {
                    const fps = Math.round(frameCount / (elapsed / 1000))
                    const fpsQuality = fps >= 55 ? 'excellent' : fps >= 30 ? 'good' : 'poor'

                    showResult('performanceResults', '帧率测试', fps >= 30,
                        `平均帧率${fps}FPS (${fpsQuality})`)
                    log(`🎬 帧率测试完成: ${fps}FPS`, fps >= 30 ? 'success' : 'warning')
                }
            }

            requestAnimationFrame(countFrames)
        }

        // 测试移动端叫牌
        window.testBiddingOnMobile = function() {
            log('🎯 开始移动端叫牌测试', 'info')

            if (!gameScene) {
                showResult('gameplayResults', '移动端叫牌', false, '游戏场景未就绪')
                return
            }

            try {
                if (typeof gameScene.forceShowBiddingUI === 'function') {
                    gameScene.forceShowBiddingUI()

                    setTimeout(() => {
                        if (gameScene.uiElements && gameScene.uiElements.biddingContainer) {
                            const isVisible = gameScene.uiElements.biddingContainer.visible
                            showResult('gameplayResults', '移动端叫牌UI', isVisible,
                                isVisible ? '叫牌UI已显示，请测试触摸交互' : '叫牌UI显示失败')
                            log(`${isVisible ? '✅' : '❌'} 移动端叫牌UI: ${isVisible ? '显示成功' : '显示失败'}`,
                                isVisible ? 'success' : 'error')
                        }
                    }, 500)
                } else {
                    showResult('gameplayResults', '移动端叫牌', false, '叫牌方法不存在')
                }
            } catch (error) {
                showResult('gameplayResults', '移动端叫牌', false, '测试失败', error.message)
                log(`❌ 移动端叫牌测试失败: ${error.message}`, 'error')
            }
        }

        // 测试移动端出牌
        window.testPlayingOnMobile = function() {
            log('🎴 开始移动端出牌测试', 'info')

            if (!gameScene) {
                showResult('gameplayResults', '移动端出牌', false, '游戏场景未就绪')
                return
            }

            try {
                if (typeof gameScene.forceShowPlayingButtons === 'function') {
                    gameScene.forceShowPlayingButtons()

                    setTimeout(() => {
                        if (gameScene.uiElements && gameScene.uiElements.playingButtons) {
                            const isVisible = gameScene.uiElements.playingButtons.visible
                            showResult('gameplayResults', '移动端出牌UI', isVisible,
                                isVisible ? '出牌UI已显示，请测试触摸交互' : '出牌UI显示失败')
                            log(`${isVisible ? '✅' : '❌'} 移动端出牌UI: ${isVisible ? '显示成功' : '显示失败'}`,
                                isVisible ? 'success' : 'error')
                        }
                    }, 500)
                } else {
                    showResult('gameplayResults', '移动端出牌', false, '出牌方法不存在')
                }
            } catch (error) {
                showResult('gameplayResults', '移动端出牌', false, '测试失败', error.message)
                log(`❌ 移动端出牌测试失败: ${error.message}`, 'error')
            }
        }

        // 测试完整游戏流程
        window.testFullGameFlow = function() {
            log('🎮 开始完整游戏流程测试', 'info')

            if (!gameScene) {
                showResult('gameplayResults', '完整游戏流程', false, '游戏场景未就绪')
                return
            }

            let testStep = 0
            const totalSteps = 3

            function nextStep() {
                testStep++

                switch(testStep) {
                    case 1:
                        log('📋 步骤1: 显示叫牌UI', 'info')
                        if (typeof gameScene.forceShowBiddingUI === 'function') {
                            gameScene.forceShowBiddingUI()
                            setTimeout(nextStep, 2000)
                        } else {
                            nextStep()
                        }
                        break

                    case 2:
                        log('📋 步骤2: 切换到出牌UI', 'info')
                        if (gameScene.uiElements && gameScene.uiElements.biddingContainer) {
                            gameScene.uiElements.biddingContainer.setVisible(false)
                        }
                        if (typeof gameScene.forceShowPlayingButtons === 'function') {
                            gameScene.forceShowPlayingButtons()
                            setTimeout(nextStep, 2000)
                        } else {
                            nextStep()
                        }
                        break

                    case 3:
                        log('📋 步骤3: 流程测试完成', 'info')
                        showResult('gameplayResults', '完整游戏流程', true,
                            '游戏流程测试完成，UI切换正常')
                        log('✅ 完整游戏流程测试完成', 'success')
                        break
                }
            }

            nextStep()
        }

        // 清空日志
        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 导出移动端测试报告
        window.exportMobileTestReport = function() {
            const deviceInfo = {
                userAgent: navigator.userAgent,
                screenSize: `${window.screen.width}×${window.screen.height}`,
                viewportSize: `${window.innerWidth}×${window.innerHeight}`,
                pixelRatio: window.devicePixelRatio || 1,
                touchSupport: 'ontouchstart' in window,
                orientation: window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape'
            }

            const testResults = {
                timestamp: new Date().toISOString(),
                deviceInfo: deviceInfo,
                touchData: touchTestData,
                testLog: document.getElementById('testLog').textContent
            }

            const reportContent = `移动端兼容性测试报告
=====================================

测试时间: ${new Date().toLocaleString()}

设备信息:
- 用户代理: ${deviceInfo.userAgent}
- 屏幕尺寸: ${deviceInfo.screenSize}
- 视口尺寸: ${deviceInfo.viewportSize}
- 像素密度: ${deviceInfo.pixelRatio}x
- 触摸支持: ${deviceInfo.touchSupport ? '是' : '否'}
- 屏幕方向: ${deviceInfo.orientation}

触摸交互统计:
- 触摸次数: ${touchTestData.touches}
- 手势次数: ${touchTestData.gestures}
- 平均响应时间: ${touchTestData.responseTime.length > 0 ?
    Math.round(touchTestData.responseTime.reduce((a, b) => a + b, 0) / touchTestData.responseTime.length) : 0}ms

详细测试日志:
${testResults.testLog}
`

            const blob = new Blob([reportContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `mobile-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)

            log('📄 移动端测试报告已导出', 'success')
        }
    </script>
</body>
</html>
