// 专门测试超时功能
import { BiddingManager } from './src/game/BiddingManager.js'

console.log('🎮 开始超时功能专项测试')

const biddingManager = new BiddingManager()
let timeoutEventReceived = false

// 监听超时事件
biddingManager.addEventListener('bidTimeout', (data) => {
    console.log('⏰ 收到超时事件:', data.player)
    timeoutEventReceived = true
})

biddingManager.addEventListener('stateUpdate', (data) => {
    console.log('📊 状态更新:', {
        phase: data.gameState.phase,
        currentBidder: data.biddingStatus.currentBidder,
        isMyTurn: data.isMyTurn
    })
})

// 设置短超时时间
biddingManager.setTimeLimit(2) // 2秒超时

const players = {
    west: { name: '西家玩家', cards: [] },
    south: { name: '南家玩家', cards: [] },
    east: { name: '东家玩家', cards: [] }
}

console.log('初始化游戏，等待超时...')
biddingManager.initGame(players, 1, 1, 'south')

// 等待超时发生
setTimeout(() => {
    if (timeoutEventReceived) {
        console.log('✅ 超时功能正常工作')
    } else {
        console.log('❌ 超时功能未触发')
        
        // 检查当前状态
        const state = biddingManager.getCurrentState()
        console.log('当前状态:', {
            phase: state.gameState.phase,
            currentBidder: state.biddingStatus.currentBidder,
            isMyTurn: state.isMyTurn
        })
    }
    
    biddingManager.destroy()
    process.exit(0)
}, 5000) // 等待5秒
