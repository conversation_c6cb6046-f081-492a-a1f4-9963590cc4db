<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牌型识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>斗地主牌型识别测试</h1>
    
    <div class="test-container">
        <h2>测试控制</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script type="module">
        import { Card } from './src/game/Card.js';
        import { CardPattern } from './src/game/CardPattern.js';

        window.Card = Card;
        window.CardPattern = CardPattern;

        // 创建测试牌
        function createTestCards(cardSpecs) {
            return cardSpecs.map(spec => {
                const [suit, rank] = spec.split('-');
                return new Card(suit, rank);
            });
        }

        // 运行单个测试
        function runTest(testName, cards, expectedType, expectedSubtype = null) {
            const pattern = new CardPattern();
            const result = pattern.identifyPattern(cards);
            
            const cardNames = cards.map(c => c.getDisplayName()).join(' ');
            let success = false;
            let message = '';

            if (!result && !expectedType) {
                success = true;
                message = `✅ ${testName}: 正确识别为无效牌型 (${cardNames})`;
            } else if (result && result.type === expectedType) {
                if (expectedSubtype && result.subtype !== expectedSubtype) {
                    message = `❌ ${testName}: 牌型正确但子类型错误 (${cardNames}) - 期望: ${expectedSubtype}, 实际: ${result.subtype}`;
                } else {
                    success = true;
                    const subtypeText = result.subtype ? ` (${result.subtype})` : '';
                    message = `✅ ${testName}: 正确识别为 ${result.type}${subtypeText} (${cardNames})`;
                }
            } else {
                const actualType = result ? result.type : 'null';
                message = `❌ ${testName}: 识别错误 (${cardNames}) - 期望: ${expectedType}, 实际: ${actualType}`;
            }

            return { success, message };
        }

        // 运行所有测试
        window.runAllTests = function() {
            const results = [];

            // 基础牌型测试
            results.push(runTest('单牌', createTestCards(['hearts-3']), 'single'));
            results.push(runTest('对子', createTestCards(['hearts-5', 'spades-5']), 'pair'));
            results.push(runTest('三张', createTestCards(['hearts-7', 'spades-7', 'clubs-7']), 'triple'));
            
            // 三带牌型测试
            results.push(runTest('三带一', createTestCards(['hearts-8', 'spades-8', 'clubs-8', 'diamonds-4']), 'triple_with_single'));
            results.push(runTest('三带二', createTestCards(['hearts-9', 'spades-9', 'clubs-9', 'diamonds-6', 'hearts-6']), 'triple_with_pair'));
            
            // 顺子测试
            results.push(runTest('顺子5张', createTestCards(['hearts-3', 'spades-4', 'clubs-5', 'diamonds-6', 'hearts-7']), 'straight'));
            results.push(runTest('顺子6张', createTestCards(['hearts-5', 'spades-6', 'clubs-7', 'diamonds-8', 'hearts-9', 'spades-10']), 'straight'));
            
            // 连对测试
            results.push(runTest('连对3对', createTestCards(['hearts-3', 'spades-3', 'clubs-4', 'diamonds-4', 'hearts-5', 'spades-5']), 'pair_straight'));
            
            // 飞机测试
            results.push(runTest('飞机不带牌', createTestCards(['hearts-5', 'spades-5', 'clubs-5', 'diamonds-6', 'hearts-6', 'spades-6']), 'triple_straight', 'pure'));
            results.push(runTest('飞机带单牌', createTestCards(['hearts-7', 'spades-7', 'clubs-7', 'diamonds-8', 'hearts-8', 'spades-8', 'clubs-3', 'diamonds-4']), 'triple_straight', 'with_singles'));
            
            // 炸弹测试
            results.push(runTest('炸弹', createTestCards(['hearts-10', 'spades-10', 'clubs-10', 'diamonds-10']), 'bomb'));
            results.push(runTest('火箭', createTestCards(['joker-small_joker', 'joker-big_joker']), 'rocket'));

            // 无效牌型测试
            results.push(runTest('无效组合', createTestCards(['hearts-3', 'spades-5', 'clubs-7']), null));

            displayResults(results);
        };

        // 显示测试结果
        function displayResults(results) {
            const container = document.getElementById('test-results');
            container.innerHTML = '';

            let passCount = 0;
            let totalCount = results.length;

            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.success ? 'success' : 'error'}`;
                div.textContent = result.message;
                container.appendChild(div);

                if (result.success) passCount++;
            });

            // 添加总结
            const summary = document.createElement('div');
            summary.className = 'test-result';
            summary.style.backgroundColor = passCount === totalCount ? '#d4edda' : '#fff3cd';
            summary.style.color = passCount === totalCount ? '#155724' : '#856404';
            summary.style.border = passCount === totalCount ? '1px solid #c3e6cb' : '1px solid #ffeaa7';
            summary.style.fontWeight = 'bold';
            summary.textContent = `测试完成: ${passCount}/${totalCount} 通过`;
            container.appendChild(summary);
        }

        // 清除结果
        window.clearResults = function() {
            document.getElementById('test-results').innerHTML = '';
        };
    </script>
</body>
</html>
