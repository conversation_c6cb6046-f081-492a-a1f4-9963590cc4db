// 出牌界面交互简化测试
import { PlayingInteractionManager } from './src/game/PlayingInteractionManager.js'

console.log('🎮 开始出牌界面交互测试')

// 模拟GameScene
class MockGameScene {
  constructor() {
    this.selectedCards = []
    this.gameState = {
      getGameInfo: () => ({
        phase: 'playing',
        players: [
          { hand: { getCards: () => this.mockCards } }
        ],
        lastPlayedPattern: null
      })
    }
    
    // 模拟卡牌数据
    this.mockCards = this.createMockCards()
    
    // 模拟Phaser方法
    this.add = {
      container: (x, y) => this.createMockContainer(x, y),
      graphics: () => this.createMockGraphics(),
      text: (x, y, text, style) => this.createMockText(x, y, text, style)
    }
    
    this.tweens = {
      add: (config) => {
        console.log('🎬 动画:', config.targets ? '目标动画' : '无目标动画')
        // 立即执行完成回调
        if (config.onComplete) {
          setTimeout(config.onComplete, 100)
        }
        return { stop: () => {} }
      }
    }
    
    console.log('✅ 模拟游戏场景创建完成')
  }
  
  createMockCards() {
    // 创建模拟卡牌
    const suits = ['hearts', 'spades', 'clubs', 'diamonds']
    const ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']
    const cards = []
    
    // 创建一些测试卡牌
    for (let i = 0; i < 13; i++) {
      const suit = suits[i % 4]
      const rank = ranks[i]
      const value = i + 3
      
      cards.push({
        id: `${suit}_${rank}`,
        suit: suit,
        rank: rank,
        value: value,
        getDisplayName: function() {
          return `${this.suit}_${this.rank}`
        }
      })
    }
    
    console.log('🎴 创建了', cards.length, '张模拟卡牌')
    return cards
  }
  
  createMockContainer(x, y) {
    return {
      x: x,
      y: y,
      setDepth: () => {},
      setVisible: (visible) => {
        console.log(`📦 容器可见性: ${visible}`)
      },
      add: () => {},
      setAlpha: () => {},
      list: [],
      destroy: () => {}
    }
  }
  
  createMockGraphics() {
    return {
      fillStyle: () => {},
      lineStyle: () => {},
      fillRoundedRect: () => {},
      strokeRoundedRect: () => {},
      clear: () => {},
      destroy: () => {}
    }
  }
  
  createMockText(x, y, text, style) {
    return {
      x: x,
      y: y,
      text: text,
      setOrigin: () => {},
      destroy: () => {}
    }
  }
  
  // 模拟GameScene方法
  selectCard(cardSprite) {
    console.log('🎯 模拟选择卡牌:', cardSprite.cardData?.getDisplayName())
  }
  
  sortPlayerCards() {
    console.log('🔄 模拟排序卡牌')
  }
}

// 运行测试
async function runInteractionTest() {
  console.log('🎯 开始交互功能测试')
  
  try {
    // 创建模拟游戏场景
    const mockScene = new MockGameScene()
    
    // 创建出牌交互管理器
    const interactionManager = new PlayingInteractionManager(mockScene)
    
    console.log('✅ 出牌交互管理器创建成功')
    
    // 测试1: 初始化
    console.log('\n=== 测试1: 初始化交互管理器 ===')
    interactionManager.initialize()
    console.log('✅ 初始化完成')
    
    // 测试2: 显示交互界面
    console.log('\n=== 测试2: 显示交互界面 ===')
    interactionManager.showInteractionUI()
    console.log('✅ 交互界面显示完成')
    
    // 测试3: 快速选择功能
    console.log('\n=== 测试3: 快速选择功能 ===')
    
    console.log('🎯 测试快速选择单牌')
    interactionManager.quickSelectSingle()
    await sleep(200)
    
    console.log('🎯 测试快速选择对子')
    interactionManager.quickSelectPair()
    await sleep(200)
    
    console.log('🎯 测试快速选择三张')
    interactionManager.quickSelectTriple()
    await sleep(200)
    
    console.log('✅ 快速选择功能测试完成')
    
    // 测试4: 工具功能
    console.log('\n=== 测试4: 工具功能 ===')
    
    console.log('🔄 测试自动排序')
    interactionManager.autoSortCards()
    await sleep(200)
    
    console.log('🧹 测试清空选择')
    interactionManager.clearSelection()
    await sleep(200)
    
    console.log('✅ 工具功能测试完成')
    
    // 测试5: 出牌建议
    console.log('\n=== 测试5: 出牌建议 ===')
    interactionManager.generatePlaySuggestions()
    console.log('✅ 出牌建议测试完成')
    
    // 测试6: 事件系统
    console.log('\n=== 测试6: 事件系统 ===')
    
    let eventReceived = false
    interactionManager.addEventListener('testEvent', (data) => {
      eventReceived = true
      console.log('✅ 事件接收成功:', data)
    })
    
    interactionManager.dispatchEvent('testEvent', { test: 'data' })
    
    if (eventReceived) {
      console.log('✅ 事件系统测试通过')
    } else {
      console.log('❌ 事件系统测试失败')
    }
    
    // 测试7: 隐藏交互界面
    console.log('\n=== 测试7: 隐藏交互界面 ===')
    interactionManager.hideInteractionUI()
    console.log('✅ 交互界面隐藏完成')
    
    // 测试8: 销毁管理器
    console.log('\n=== 测试8: 销毁管理器 ===')
    interactionManager.destroy()
    console.log('✅ 管理器销毁完成')
    
    console.log('\n🎉 所有交互功能测试完成！')
    
  } catch (error) {
    console.error('❌ 交互功能测试失败:', error)
  }
}

// 辅助函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 运行测试
runInteractionTest().catch(error => {
  console.error('❌ 测试执行失败:', error)
})
