<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叫牌UI专项测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1E3A8A;
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66BB6A;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        #gameContainer {
            width: 800px;
            height: 600px;
            border: 2px solid #FFD700;
            margin: 20px auto;
            background: #000;
        }
        .status {
            background: #2196F3;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 叫牌UI专项测试</h1>
        <p>专门测试叫牌界面的显示和交互功能</p>
        
        <div class="status" id="gameStatus">等待游戏启动...</div>
        
        <div class="test-section">
            <h3>测试1: 游戏初始化</h3>
            <button onclick="initializeGame()">初始化游戏</button>
            <div id="initResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 叫牌UI显示</h3>
            <button onclick="testBiddingUIDisplay()">测试叫牌UI显示</button>
            <button onclick="forceShowBiddingUI()">强制显示叫牌UI</button>
            <div id="biddingUIResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 叫牌交互</h3>
            <button onclick="testBiddingInteraction()">测试叫牌交互</button>
            <div id="biddingInteractionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 完整叫牌流程</h3>
            <button onclick="testFullBiddingFlow()">测试完整叫牌流程</button>
            <div id="fullFlowResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>游戏显示区域</h3>
            <div id="gameContainer"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        let gameInstance = null
        let gameScene = null

        function log(message) {
            const logDiv = document.getElementById('testLog')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logDiv.textContent += logEntry
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId)
            element.className = `result ${success ? 'pass' : 'fail'}`
            element.textContent = `${success ? '✅ 通过' : '❌ 失败'}: ${message}`
        }

        function updateStatus(message) {
            document.getElementById('gameStatus').textContent = message
        }

        // 测试1: 游戏初始化
        window.initializeGame = async function() {
            log('🧪 开始初始化游戏')
            
            try {
                // 清理之前的游戏实例
                if (gameInstance) {
                    gameInstance.destroy(true)
                    gameInstance = null
                    gameScene = null
                }
                
                const { GameMainScene } = await import('./src/game/GameScene.js')
                
                // 创建游戏配置
                const config = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    parent: 'gameContainer',
                    backgroundColor: '#1E3A8A',
                    scene: GameMainScene
                }
                
                gameInstance = new Phaser.Game(config)
                log('✅ Phaser游戏实例创建成功')
                
                // 等待场景初始化
                setTimeout(() => {
                    gameScene = gameInstance.scene.getScene('GameMainScene')
                    if (gameScene) {
                        log('✅ GameMainScene场景获取成功')
                        updateStatus('游戏已初始化，等待开始')
                        showResult('initResult', true, '游戏初始化成功')
                    } else {
                        log('❌ GameMainScene场景获取失败')
                        showResult('initResult', false, '场景获取失败')
                    }
                }, 2000)
                
            } catch (error) {
                log(`❌ 游戏初始化失败: ${error.message}`)
                showResult('initResult', false, `初始化失败: ${error.message}`)
            }
        }

        // 测试2: 叫牌UI显示
        window.testBiddingUIDisplay = function() {
            log('🧪 开始测试叫牌UI显示')
            
            if (!gameScene) {
                log('❌ 游戏场景未初始化')
                showResult('biddingUIResult', false, '游戏场景未初始化')
                return
            }
            
            try {
                // 启动新游戏
                gameScene.startNewGame()
                log('✅ 游戏启动成功')
                
                // 等待叫牌阶段
                setTimeout(() => {
                    const gameInfo = gameScene.gameState.getGameInfo()
                    log(`📊 当前游戏状态: ${gameInfo.phase}`)
                    
                    if (gameInfo.phase === 'bidding') {
                        log('✅ 进入叫牌阶段')
                        
                        // 检查叫牌UI是否显示
                        setTimeout(() => {
                            const biddingContainer = gameScene.uiElements.biddingContainer
                            if (biddingContainer && biddingContainer.visible) {
                                log('✅ 叫牌UI已显示')
                                updateStatus('叫牌阶段 - UI已显示')
                                showResult('biddingUIResult', true, '叫牌UI显示成功')
                            } else {
                                log('❌ 叫牌UI未显示')
                                updateStatus('叫牌阶段 - UI未显示')
                                showResult('biddingUIResult', false, '叫牌UI未显示')
                            }
                        }, 1000)
                    } else {
                        log(`❌ 未进入叫牌阶段，当前阶段: ${gameInfo.phase}`)
                        showResult('biddingUIResult', false, `未进入叫牌阶段: ${gameInfo.phase}`)
                    }
                }, 500)
                
            } catch (error) {
                log(`❌ 叫牌UI显示测试失败: ${error.message}`)
                showResult('biddingUIResult', false, `测试失败: ${error.message}`)
            }
        }

        // 强制显示叫牌UI
        window.forceShowBiddingUI = function() {
            log('🚨 强制显示叫牌UI')
            
            if (!gameScene) {
                log('❌ 游戏场景未初始化')
                return
            }
            
            try {
                gameScene.forceShowBiddingUI()
                log('✅ 强制显示叫牌UI完成')
                updateStatus('叫牌UI已强制显示')
                
                // 检查UI是否真的显示了
                setTimeout(() => {
                    const biddingContainer = gameScene.uiElements.biddingContainer
                    if (biddingContainer && biddingContainer.visible) {
                        log('✅ 强制显示成功，叫牌UI可见')
                        showResult('biddingUIResult', true, '强制显示成功')
                    } else {
                        log('❌ 强制显示失败，叫牌UI仍不可见')
                        showResult('biddingUIResult', false, '强制显示失败')
                    }
                }, 100)
                
            } catch (error) {
                log(`❌ 强制显示叫牌UI失败: ${error.message}`)
            }
        }

        // 测试3: 叫牌交互
        window.testBiddingInteraction = function() {
            log('🧪 开始测试叫牌交互')
            
            if (!gameScene) {
                log('❌ 游戏场景未初始化')
                showResult('biddingInteractionResult', false, '游戏场景未初始化')
                return
            }
            
            try {
                // 确保叫牌UI显示
                gameScene.forceShowBiddingUI()
                
                // 检查叫牌按钮是否存在
                const biddingButtons = gameScene.uiElements.biddingButtons
                if (biddingButtons) {
                    const buttonCount = Object.keys(biddingButtons).length
                    log(`✅ 找到 ${buttonCount} 个叫牌按钮`)
                    
                    // 检查按钮是否可交互
                    let interactiveButtons = 0
                    Object.values(biddingButtons).forEach(button => {
                        if (button.input && button.input.enabled) {
                            interactiveButtons++
                        }
                    })
                    
                    log(`✅ ${interactiveButtons} 个按钮可交互`)
                    
                    if (buttonCount >= 4 && interactiveButtons >= 4) {
                        showResult('biddingInteractionResult', true, `叫牌交互正常 (${buttonCount}个按钮)`)
                    } else {
                        showResult('biddingInteractionResult', false, `叫牌交互异常 (${buttonCount}个按钮，${interactiveButtons}个可交互)`)
                    }
                } else {
                    log('❌ 未找到叫牌按钮')
                    showResult('biddingInteractionResult', false, '未找到叫牌按钮')
                }
                
            } catch (error) {
                log(`❌ 叫牌交互测试失败: ${error.message}`)
                showResult('biddingInteractionResult', false, `测试失败: ${error.message}`)
            }
        }

        // 测试4: 完整叫牌流程
        window.testFullBiddingFlow = function() {
            log('🧪 开始测试完整叫牌流程')
            
            if (!gameScene) {
                log('❌ 游戏场景未初始化')
                showResult('fullFlowResult', false, '游戏场景未初始化')
                return
            }
            
            try {
                // 重新开始游戏
                gameScene.startNewGame()
                log('✅ 重新开始游戏')
                
                let testSteps = 0
                const totalSteps = 3
                
                // 步骤1: 等待叫牌阶段
                setTimeout(() => {
                    const gameInfo = gameScene.gameState.getGameInfo()
                    if (gameInfo.phase === 'bidding') {
                        testSteps++
                        log('✅ 步骤1: 成功进入叫牌阶段')
                    } else {
                        log(`❌ 步骤1: 未进入叫牌阶段 (${gameInfo.phase})`)
                    }
                    
                    // 步骤2: 检查叫牌管理器
                    if (gameScene.biddingManager) {
                        const biddingState = gameScene.biddingManager.getCurrentState()
                        if (biddingState && biddingState.gameState.phase === 'bidding') {
                            testSteps++
                            log('✅ 步骤2: 叫牌管理器状态正常')
                        } else {
                            log('❌ 步骤2: 叫牌管理器状态异常')
                        }
                    } else {
                        log('❌ 步骤2: 叫牌管理器不存在')
                    }
                    
                    // 步骤3: 检查UI显示
                    setTimeout(() => {
                        const biddingContainer = gameScene.uiElements.biddingContainer
                        if (biddingContainer && biddingContainer.visible) {
                            testSteps++
                            log('✅ 步骤3: 叫牌UI正常显示')
                        } else {
                            log('❌ 步骤3: 叫牌UI未显示')
                        }
                        
                        // 汇总结果
                        if (testSteps === totalSteps) {
                            log('🎉 完整叫牌流程测试通过')
                            updateStatus('完整叫牌流程正常')
                            showResult('fullFlowResult', true, `完整流程测试通过 (${testSteps}/${totalSteps})`)
                        } else {
                            log(`⚠️ 完整叫牌流程测试部分失败 (${testSteps}/${totalSteps})`)
                            updateStatus('完整叫牌流程存在问题')
                            showResult('fullFlowResult', false, `流程测试失败 (${testSteps}/${totalSteps})`)
                        }
                    }, 1000)
                }, 500)
                
            } catch (error) {
                log(`❌ 完整叫牌流程测试失败: ${error.message}`)
                showResult('fullFlowResult', false, `测试失败: ${error.message}`)
            }
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '日志已清空\n'
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎯 叫牌UI专项测试页面已加载')
            log('请先点击"初始化游戏"，然后进行其他测试')
        })
    </script>
</body>
</html>
