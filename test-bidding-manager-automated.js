// 自动化测试叫牌管理器
import { BiddingManager } from './src/game/BiddingManager.js'

console.log('🎮 开始叫牌管理器自动化测试')

// 创建测试实例
const biddingManager = new BiddingManager()
let testResults = []

// 测试事件监听器
const events = []
biddingManager.addEventListener('gameStarted', (data) => {
    events.push({ type: 'gameStarted', data })
    console.log('✅ 事件: 游戏开始', data)
})

biddingManager.addEventListener('stateUpdate', (data) => {
    events.push({ type: 'stateUpdate', data })
    console.log('📊 事件: 状态更新', {
        phase: data.gameState.phase,
        currentBidder: data.biddingStatus.currentBidder,
        isMyTurn: data.isMyTurn
    })
})

biddingManager.addEventListener('bidMade', (data) => {
    events.push({ type: 'bidMade', data })
    console.log('🎯 事件: 叫牌完成', {
        player: data.player,
        bidOption: data.bidOption,
        displayName: biddingManager.getBidDisplayName(data.bidOption)
    })
})

biddingManager.addEventListener('bidTimeout', (data) => {
    events.push({ type: 'bidTimeout', data })
    console.log('⏰ 事件: 叫牌超时', data.player)
})

biddingManager.addEventListener('landlordSelected', (data) => {
    events.push({ type: 'landlordSelected', data })
    console.log('🎉 事件: 地主选定', data.landlord)
})

biddingManager.addEventListener('redealRequired', (data) => {
    events.push({ type: 'redealRequired', data })
    console.log('🔄 事件: 需要重新发牌', data.reason)
})

// 测试1: 基本初始化
console.log('\n=== 测试1: 基本初始化 ===')
try {
    const players = {
        west: { name: '西家玩家', cards: [] },
        south: { name: '南家玩家', cards: [] },
        east: { name: '东家玩家', cards: [] }
    }
    
    const firstBidder = biddingManager.initGame(players, 1, 1, 'south')
    console.log(`✅ 游戏初始化成功，首叫者: ${firstBidder}`)
    
    const state = biddingManager.getCurrentState()
    console.log(`✅ 游戏状态: ${state.gameState.phase}`)
    console.log(`✅ 当前叫牌者: ${state.biddingStatus.currentBidder}`)
    console.log(`✅ 是否轮到我: ${state.isMyTurn}`)
    
    testResults.push({ test: '基本初始化', result: 'PASS' })
} catch (error) {
    console.error('❌ 基本初始化失败:', error)
    testResults.push({ test: '基本初始化', result: 'FAIL', error: error.message })
}

// 测试2: 叫牌功能
console.log('\n=== 测试2: 叫牌功能 ===')
try {
    // 等待一下让状态稳定
    await new Promise(resolve => setTimeout(resolve, 100))
    
    let state = biddingManager.getCurrentState()
    console.log(`当前叫牌者: ${state.biddingStatus.currentBidder}`)
    console.log(`可用叫牌选项: ${state.availableBids.join(', ')}`)
    
    // 如果轮到南家（我），进行叫牌
    if (state.isMyTurn) {
        const result = biddingManager.makeBid('one_point')
        if (result.success) {
            console.log('✅ 南家叫1分成功')
        } else {
            console.log('❌ 南家叫牌失败:', result.reason)
        }
    } else {
        // 模拟当前叫牌者的行为
        const currentBidder = state.biddingStatus.currentBidder
        const result = biddingManager.simulateOtherPlayerBid(currentBidder, 'no_bid')
        if (result.success) {
            console.log(`✅ ${currentBidder} 模拟不叫成功`)
        }
    }
    
    testResults.push({ test: '叫牌功能', result: 'PASS' })
} catch (error) {
    console.error('❌ 叫牌功能测试失败:', error)
    testResults.push({ test: '叫牌功能', result: 'FAIL', error: error.message })
}

// 测试3: 模拟完整叫牌流程
console.log('\n=== 测试3: 模拟完整叫牌流程 ===')
try {
    // 重新开始游戏
    biddingManager.reset()
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const players = {
        west: { name: '西家玩家', cards: [] },
        south: { name: '南家玩家', cards: [] },
        east: { name: '东家玩家', cards: [] }
    }
    
    biddingManager.initGame(players, 1, 1, 'south')
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 模拟完整的叫牌过程
    let state = biddingManager.getCurrentState()
    let round = 0
    const maxRounds = 10 // 防止无限循环
    
    while (state.gameState.phase === 'bidding' && round < maxRounds) {
        round++
        console.log(`\n--- 叫牌轮次 ${round} ---`)
        
        state = biddingManager.getCurrentState()
        const currentBidder = state.biddingStatus.currentBidder
        console.log(`当前叫牌者: ${currentBidder}`)
        
        if (state.isMyTurn) {
            // 南家的策略：第一轮叫1分，后续根据情况决定
            let bidOption = 'no_bid'
            if (round === 1) {
                bidOption = 'one_point'
            } else if (state.biddingStatus.highestBid && state.biddingStatus.highestBid.option === 'one_point') {
                bidOption = 'two_point'
            }

            const result = biddingManager.makeBid(bidOption)
            console.log(`南家叫牌: ${biddingManager.getBidDisplayName(bidOption)} - ${result.success ? '成功' : '失败'}`)
        } else {
            // 模拟其他玩家的行为
            let bidOption = 'no_bid'

            // 简单的AI策略
            if (round === 1 && currentBidder === 'west') {
                bidOption = 'no_bid' // 西家第一轮不叫
            } else if (round === 2 && currentBidder === 'east') {
                bidOption = 'two_point' // 东家叫2分
            } else {
                bidOption = 'no_bid'
            }

            const result = biddingManager.simulateOtherPlayerBid(currentBidder, bidOption)
            console.log(`${currentBidder} 模拟叫牌: ${biddingManager.getBidDisplayName(bidOption)} - ${result.success ? '成功' : '失败'}`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 200))
        state = biddingManager.getCurrentState()
    }
    
    if (state.gameState.phase === 'completed') {
        console.log('✅ 叫牌流程完成')
        const finalEvents = events.filter(e => e.type === 'landlordSelected' || e.type === 'redealRequired')
        if (finalEvents.length > 0) {
            console.log('✅ 最终结果事件触发正常')
        }
    } else {
        console.log('⚠️ 叫牌流程未完成，可能超时')
    }
    
    testResults.push({ test: '完整叫牌流程', result: 'PASS' })
} catch (error) {
    console.error('❌ 完整叫牌流程测试失败:', error)
    testResults.push({ test: '完整叫牌流程', result: 'FAIL', error: error.message })
}

// 测试4: 超时处理
console.log('\n=== 测试4: 超时处理测试 ===')
try {
    biddingManager.reset()
    biddingManager.setTimeLimit(1) // 设置1秒超时
    
    const players = {
        west: { name: '西家玩家', cards: [] },
        south: { name: '南家玩家', cards: [] },
        east: { name: '东家玩家', cards: [] }
    }
    
    biddingManager.initGame(players, 1, 1, 'south')
    
    // 等待超时发生
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const timeoutEvents = events.filter(e => e.type === 'bidTimeout')
    if (timeoutEvents.length > 0) {
        console.log('✅ 超时处理正常工作')
        testResults.push({ test: '超时处理', result: 'PASS' })
    } else {
        console.log('⚠️ 未检测到超时事件')
        testResults.push({ test: '超时处理', result: 'PARTIAL' })
    }
} catch (error) {
    console.error('❌ 超时处理测试失败:', error)
    testResults.push({ test: '超时处理', result: 'FAIL', error: error.message })
}

// 测试5: 状态管理
console.log('\n=== 测试5: 状态管理测试 ===')
try {
    const state = biddingManager.getCurrentState()
    
    // 检查状态结构
    const requiredFields = ['gameState', 'biddingStatus', 'isMyTurn', 'currentPlayer', 'availableBids', 'timeLimit', 'players']
    const missingFields = requiredFields.filter(field => !(field in state))
    
    if (missingFields.length === 0) {
        console.log('✅ 状态结构完整')
        console.log(`✅ 游戏阶段: ${state.gameState.phase}`)
        console.log(`✅ 当前玩家: ${state.currentPlayer}`)
        console.log(`✅ 时间限制: ${state.timeLimit}秒`)
        testResults.push({ test: '状态管理', result: 'PASS' })
    } else {
        console.log('❌ 状态结构不完整，缺少字段:', missingFields)
        testResults.push({ test: '状态管理', result: 'FAIL', error: `缺少字段: ${missingFields.join(', ')}` })
    }
} catch (error) {
    console.error('❌ 状态管理测试失败:', error)
    testResults.push({ test: '状态管理', result: 'FAIL', error: error.message })
}

// 输出测试结果
console.log('\n' + '='.repeat(50))
console.log('🎯 叫牌管理器测试结果汇总')
console.log('='.repeat(50))

testResults.forEach((result, index) => {
    const status = result.result === 'PASS' ? '✅' : result.result === 'PARTIAL' ? '⚠️' : '❌'
    console.log(`${index + 1}. ${result.test}: ${status} ${result.result}`)
    if (result.error) {
        console.log(`   错误: ${result.error}`)
    }
})

const passCount = testResults.filter(r => r.result === 'PASS').length
const totalCount = testResults.length
console.log(`\n📊 测试通过率: ${passCount}/${totalCount} (${Math.round(passCount/totalCount*100)}%)`)

// 输出事件统计
console.log('\n📋 事件触发统计:')
const eventCounts = {}
events.forEach(event => {
    eventCounts[event.type] = (eventCounts[event.type] || 0) + 1
})
Object.entries(eventCounts).forEach(([type, count]) => {
    console.log(`  ${type}: ${count}次`)
})

console.log('\n🎉 叫牌管理器测试完成！')

// 清理
biddingManager.destroy()
